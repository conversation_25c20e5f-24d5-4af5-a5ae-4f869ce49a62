# SealMyIdea - Developer Documentation Guide

## 📚 Documentation Overview

This project uses **JSDoc** with the **clean-jsdoc-theme** for comprehensive code documentation. All code is fully internationalized (i18n) with 100% compliance using translation functions.

## 🌐 Internationalization (i18n)

### Translation System

- **Translation Files**: Located in `src/locales/`
  - `en.json` - English translations
  - `es.json` - Spanish translations
- **Translation Hook**: `useTranslation()` from `src/hooks/useTranslation.ts`
- **Usage**: All UI text must use `t('key')` function calls

### Translation Key Structure

```javascript
// Nested keys using dot notation
t('common.welcome'); // "Welcome"
t('dashboard.subtitle'); // Dashboard subtitle
t('validation.errors.required'); // "This field is required"
t('pwa.install_title'); // "Install Seal my idea"
```

### Adding New Translations

1. Add the key to `src/locales/en.json`
2. Add corresponding Spanish translation to `src/locales/es.json`
3. Use `t('your.new.key')` in components

### i18n Compliance Rules

- ❌ **Never** use hardcoded text: `<h1>Welcome</h1>`
- ✅ **Always** use translations: `<h1>{t('common.welcome')}</h1>`
- ❌ **Never** concatenate translations: `t('hello') + ' ' + name`
- ✅ **Use** parameterized translations when needed

## 📖 JSDoc Documentation Standards

### Installation & Setup

```bash
# Generate documentation
npm run docs

# Serve documentation locally (generates docs first, then serves on port 8080)
npm run docs:serve

# Clean documentation
npm run docs:clean

# View documentation
# After running npm run docs, open: file:///path/to/project/docs/index.html
# Or after running npm run docs:serve, open: http://localhost:8080
```

### Documentation Status

✅ **Working**: Documentation generates successfully with clean-jsdoc-theme
✅ **Theme**: Clean, professional theme with proper styling
✅ **Content**: Comprehensive documentation for all major modules
✅ **Examples**: Practical usage examples for all functions
✅ **Navigation**: Organized by modules (utils, helpers, hooks, components, store)

### Documentation Structure

The documentation is generated from JavaScript files in the `docs-src/` directory:

- `docs-src/utils.js` - Utility functions documentation
- `docs-src/helpers.js` - Helper functions documentation
- `docs-src/hooks.js` - Custom hooks documentation
- `docs-src/components.js` - React components documentation
- `docs-src/store.js` - Redux store documentation

**Note**: Due to JSDoc's limited TypeScript support, we maintain separate JavaScript documentation files that mirror the TypeScript implementations with comprehensive JSDoc comments.

### Documentation Requirements

#### 1. File Headers

Every file must start with a JSDoc file header:

```javascript
/**
 * @fileoverview Brief description of the file's purpose
 * @module path/to/module
 * @description Detailed description of functionality
 */
```

#### 2. Function Documentation

```javascript
/**
 * Brief function description
 *
 * @function functionName
 * @param {Type} paramName - Parameter description
 * @returns {Type} Return value description
 *
 * @example
 * // Usage example
 * const result = functionName('example');
 *
 * @description
 * Detailed description of what the function does,
 * including any side effects or important behavior.
 */
```

#### 3. React Component Documentation

```javascript
/**
 * Component description
 *
 * @component
 * @param {Object} props - Component props
 * @returns {JSX.Element} The rendered component
 *
 * @example
 * // Basic usage
 * <ComponentName prop1="value" />
 *
 * @description
 * Detailed component description including:
 * - What it renders
 * - Key features
 * - Integration points
 */
```

#### 4. Interface/Type Documentation

```javascript
/**
 * Interface description
 * @interface InterfaceName
 * @property {Type} propertyName - Property description
 * @property {Type} [optionalProp] - Optional property description
 */
```

#### 5. Hook Documentation

```javascript
/**
 * Custom hook description
 *
 * @hook useHookName
 * @returns {Object} Hook return object
 * @returns {Function} returns.method - Method description
 * @returns {boolean} returns.state - State description
 *
 * @example
 * const { method, state } = useHookName();
 */
```

### Documentation Categories

#### ✅ Must Document

- All exported functions
- All React components
- All custom hooks
- All interfaces/types
- All Redux slices and actions
- All utility functions
- Configuration files

#### 📝 Documentation Sections

1. **@fileoverview** - File purpose
2. **@module** - Module path
3. **@description** - Detailed description
4. **@param** - Parameters with types
5. **@returns** - Return values
6. **@example** - Usage examples
7. **@throws** - Possible exceptions
8. **@since** - Version added
9. **@deprecated** - If deprecated

### Code Examples in Documentation

#### Good Documentation Example

```javascript
/**
 * @fileoverview API helper utilities for HTTP requests
 * @module helpers/ApiHelper
 */

/**
 * Makes authenticated API requests with error handling
 *
 * @template TRequest - Request payload type
 * @template TResponse - Response data type
 * @function getApiData
 * @param {ApiOptions<TRequest>} options - Request configuration
 * @returns {Promise<TResponse | ApiError | undefined>} API response
 *
 * @example
 * // GET request
 * const users = await getApiData<{}, User[]>({
 *   url: '/api/users',
 *   method: 'GET'
 * });
 *
 * @example
 * // POST with data
 * const newUser = await getApiData<CreateUserRequest, User>({
 *   url: '/api/users',
 *   method: 'POST',
 *   data: { name: 'John', email: '<EMAIL>' }
 * });
 */
```

## 🛠 Development Workflow

### Before Committing

1. **Check i18n compliance**: Ensure no hardcoded text remains
2. **Add JSDoc comments**: Document all new functions/components
3. **Generate docs**: Run `npm run docs` to verify documentation builds
4. **Test translations**: Verify all translation keys work in both languages

### Code Review Checklist

- [ ] All text uses `t()` function
- [ ] JSDoc comments added for new code
- [ ] Examples provided in documentation
- [ ] Translation keys added to both language files
- [ ] No hardcoded strings in UI components

## 📁 Project Structure

```
src/
├── components/          # React components (documented)
├── hooks/              # Custom hooks (documented)
├── utils/              # Utility functions (documented)
├── helpers/            # Helper functions (documented)
├── store/              # Redux store & slices (documented)
├── locales/            # Translation files
│   ├── en.json        # English translations
│   └── es.json        # Spanish translations
└── config/             # Configuration files (documented)
```

## 🎯 Quality Standards

### Documentation Quality

- **Comprehensive**: Cover all public APIs
- **Clear**: Use simple, understandable language
- **Examples**: Provide practical usage examples
- **Up-to-date**: Keep documentation synchronized with code

### i18n Quality

- **Complete**: 100% translation coverage
- **Consistent**: Use consistent terminology
- **Contextual**: Provide meaningful translations, not literal ones
- **Tested**: Verify translations in both languages

## 🚀 Deployment

The documentation is automatically generated and can be deployed alongside the application. Access it at `/docs` when running locally with `npm run docs:serve`.

---

**Remember**: Good documentation and proper internationalization are not optional—they're essential for maintainable, accessible software.
