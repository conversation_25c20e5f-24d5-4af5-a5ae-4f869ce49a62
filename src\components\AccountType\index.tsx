'use client';

import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import { formatStorage, getUserData } from '@/utils/CommonFunctions';
import { ArrowLeftOutlined } from '@ant-design/icons';
import {
  Alert,
  Button,
  Col,
  Popconfirm,
  Row,
  Spin,
  Switch,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useNotification } from '../Notification/NotificationContext';
import PlanCard from '../SubscriptionPlan/PlanCard';
import styles from './accountType.module.css';
import SubscriptionModal from './SubscriptionModal';

const { Title, Text } = Typography;

interface UserInfo {
  role: string;
  createdAt: number;
  updatedAt: number;
}

const starterPlan = {
  id: '1',
  object: 'price',
  active: true,
  billing_scheme: 'per_unit',
  created: **********,
  currency: 'usd',
  custom_unit_amount: null,
  livemode: false,
  lookup_key: null,
  metadata: {
    MB: '10',
    cancle_subscription: '0',
  },
  nickname: 'Basic',
  product: '1',
  recurring: {
    interval: 'month',
    interval_count: 1,
    meter: null,
    trial_period_days: null,
    usage_type: 'licensed',
  },
  tax_behavior: 'unspecified',
  tiers_mode: null,
  transform_quantity: null,
  type: 'recurring',
  unit_amount: 0,
  unit_amount_decimal: '0',
};

export interface Prices {
  id: string;
  object: 'price';
  active: boolean;
  billing_scheme: 'per_unit' | 'tiered';
  created: number;
  currency: string;
  custom_unit_amount: number | null;
  livemode: boolean;
  lookup_key: string | null;
  metadata: {
    MB: string;
    [key: string]: string | undefined;
  };
  nickname: string;
  product: string;
  recurring: {
    interval: 'day' | 'week' | 'month' | 'year';
    interval_count: number;
    meter: string | null;
    trial_period_days: number | null;
    usage_type: 'licensed' | 'metered';
  } | null;
  tax_behavior: 'unspecified' | 'inclusive' | 'exclusive';
  tiers_mode: string | null;
  transform_quantity: string | null;
  type: 'one_time' | 'recurring';
  unit_amount: number;
  unit_amount_decimal: string | null;
}

export interface Plans {
  title: string;
  amount: number;
  image: string;
  price_id: string;
  product_id: string;
  is_active: boolean;
  type: string;
  _id: string;
  from: string;
  created_by: string;
  createdAt: Date;
  updatedAt: Date;
  prices: Prices[];
  quantity: number;
}

interface CApiResponse<T> {
  status: boolean;
  data: T;
  message: string;
}

export interface AccountModalProps {
  visible: boolean;
  plan: Prices | null;
  isCurrentPlan: boolean;
}

const AccountType = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const notification = useNotification();
  const [loading, setLoading] = useState(false);
  const [planLoading, setPlanLoading] = useState(false);
  const { user } = useAppSelector(s => s.auth);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [allPlans, setAllPlans] = useState<{
    monthlyPlan: Prices[];
    yearlyPlan: Prices[];
  }>({
    monthlyPlan: [],
    yearlyPlan: [],
  });
  const [planDetails, setPlanDetails] = useState<Plans | null>(null);
  const [duration, setDuration] = useState(true);
  const [modal, setModal] = useState<AccountModalProps>({
    visible: false,
    plan: null,
    isCurrentPlan: false,
  });
  starterPlan.metadata.MB = user?.basic_plan_storage
    ? String(formatStorage(user?.basic_plan_storage, 'Bytes')).replace(
        /\D/g,
        ''
      )
    : '10';
  const [activeIndex, setActiveIndex] = useState<string>('');
  const [loader, setLoader] = useState('');

  const isActivePlanDuration =
    user?.subscriptionData?.priceData?.recurring?.interval;

  const isMonthlyPlan = isActivePlanDuration === 'month';

  const isBasicPlan = user?.is_basic_plan === true;

  const md = useMediaQuery('(max-width: 900px)');

  const getPlans = async () => {
    setPlanLoading(true);
    try {
      const res = await getApiData<{ type: string }, ApiResponse>({
        url: `${Endpoints.getSealPlans}?type=subscription`,
        method: 'GET',
      });
      if (res && res.status === true && res.data) {
        console.log('res', res);
        const data = [starterPlan, ...res.data[0]?.prices];
        setAllPlans({
          monthlyPlan: data.filter(
            (plan: Prices) =>
              plan.recurring?.interval === 'month' ||
              plan.recurring?.interval === 'day'
          ),
          yearlyPlan: data.filter(
            (plan: Prices) => plan.recurring?.interval === 'year'
          ),
        });
        setPlanDetails(res.data[0]);
        if (isBasicPlan) {
          setActiveIndex(data[0]?.id);
        }
      } else {
        notification.error({
          message: res?.message || 'Error fetching plans',
        });
      }
      setPlanLoading(false);
    } catch (error) {
      setPlanLoading(false);
      notification.error({
        message: error?.message || 'Error fetching plans',
      });
    }
  };

  useEffect(() => {
    getUserData();
  }, []);

  useEffect(() => {
    getPlans();
    getAccountDetails();
    console.log(
      'user?.subscription.priceId',
      user,
      user?.subscriptionData?.priceId
    );
    if (user?.subscriptionData) {
      setActiveIndex(user.subscriptionData.priceId);
      setDuration(isMonthlyPlan);
    }
  }, [user]);

  const cancelOrResumeSubscription = async (type: string) => {
    setLoader(type);
    try {
      const res = await getApiData<
        {
          subscription_id?: string | null;
          type: string;
        },
        ApiResponse
      >({
        url: Endpoints.resumeOrCancelSubscription,
        method: 'POST',
        data: {
          subscription_id: user?.subscriptionData?.stripeSubscriptionId,
          type,
        },
      });
      if (res && res.status === true) {
        notification.success({
          message: res?.message || `Subscription ${type} successfully`,
        });
        setTimeout(() => {
          getUserData();
        }, 2000);
      } else {
        notification.error({
          message: res?.message || 'Error generating link',
        });
      }
      setLoader('');
    } catch (error) {
      setLoader('');
      notification.error({
        message: error?.message || 'Error generating link',
      });
    }
  };

  const getAccountDetails = async () => {
    setLoading(true);

    try {
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      const res = await getApiData<{}, CApiResponse<UserInfo>>({
        url: `${Endpoints.userAccountDetails}${user?._id}`,
        method: 'GET',
      });
      if (res && res.status === true && res.data) {
        setUserInfo(res?.data);
      }
    } catch (error) {
      console.error('Error fetching folder data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.header}>
            <Button
              type='link'
              icon={<ArrowLeftOutlined />}
              className={styles.backButton}
              onClick={() => {
                router.replace('/settings');
              }}
            >
              {t('common.back')}
            </Button>
          </div>
          <Title level={2} className={styles.title}>
            {t('accountType.title')}
          </Title>
          <div className={styles.foldersSpinner}>
            <Spin size='default' />
            <Text className={styles.loadingText}>{t('common.loading')}</Text>
          </div>
        </div>
      </div>
    );
  }

  const endDate =
    user?.subscriptionData &&
    dayjs
      .unix(Number(user?.subscriptionData?.currentPeriodEnd))
      .format('MMMM D, YYYY hh:mm A');
  const planName =
    user?.subscriptionData?.priceData?.nickname || 'Current Plan';
  const interval =
    user?.subscriptionData?.priceData?.recurring?.interval === 'year'
      ? 'yearly'
      : 'monthly';

  const message = user?.subscriptionData?.cancelAtPeriodEnd
    ? t('subscriptionPlan.plan_cancelled_title', { planName })
    : t('subscriptionPlan.plan_active_title', { planName });

  const description = user?.subscriptionData?.cancelAtPeriodEnd
    ? t('subscriptionPlan.plan_cancelled_desc', {
        planName,
        endDate,
      })
    : t('subscriptionPlan.plan_active_desc', {
        planName,
        endDate,
        interval,
      });

  const plansToRender = duration ? allPlans.monthlyPlan : allPlans.yearlyPlan;
  const totalStorage = () => {
    const totalStorage = user?.is_basic_plan
      ? Number(user?.basic_plan_storage)
      : Number(user?.subscriptionData?.priceData?.metadata?.total_storage);

    const total_storage = formatStorage(Number(totalStorage), 'Bytes');
    return total_storage;
  };

  const getUsedStorage = () => {
    const remaining_storage_new = formatStorage(
      Number(user?.total_uploaded_storage) || 0,
      'Bytes'
    );
    return remaining_storage_new;
  };

  const getHeight = () => {
    const totalStorage = user?.is_basic_plan
      ? Number(user?.basic_plan_storage)
      : Number(user?.subscriptionData?.priceData?.metadata?.total_storage);

    const height = Math.round(
      totalStorage
        ? ((Number(user?.total_uploaded_storage) || 0) / totalStorage) * 100
        : 0
    );
    return height;
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {' '}
        <div className={styles.header}>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.replace('/settings');
            }}
          >
            {t('common.back')}
          </Button>
        </div>
        <Title level={2} className={styles.title}>
          {t('accountType.title')}
        </Title>
        <Text className={styles.subtitle}>{t('accountType.subtitle')}</Text>
        {!isBasicPlan && (
          <Alert
            message={message}
            showIcon
            description={description}
            type={user?.subscriptionData?.cancelAtPeriodEnd ? 'error' : 'info'}
            style={{ marginBottom: 16 }}
            action={
              user?.subscriptionData?.cancelAtPeriodEnd ? (
                <Popconfirm
                  title={t('common.resume_subscription')}
                  description={t('common.resume_info', {
                    defaultValue:
                      'By resuming, your subscription stays active and billing will continue as normal.',
                  })}
                  styles={{ root: { maxWidth: 280 } }}
                  okText={t('common.confirm')}
                  okButtonProps={{ loading: loader === 'resume' }}
                  cancelText={t('common.cancel')}
                  cancelButtonProps={{ disabled: loader === 'resume' }}
                  onConfirm={() => cancelOrResumeSubscription('resume')}
                >
                  <Button size='small' danger disabled={loader === 'resume'}>
                    {t('common.resume')}
                  </Button>
                </Popconfirm>
              ) : null
            }
          />
        )}
        <div className={styles.accountInfoCard}>
          <div className={styles.infoRow}>
            <Text strong>{t('accountType.account_type_label')}</Text>
            <Text>
              {userInfo?.role
                ? userInfo.role.charAt(0).toUpperCase() + userInfo.role.slice(1)
                : '-'}{' '}
              User
            </Text>
          </div>
          <div className={styles.infoRow}>
            <Text strong>{t('accountType.member_since_label')}</Text>
            <Text>
              {userInfo?.createdAt
                ? dayjs(userInfo.createdAt).format('MMMM YYYY')
                : '-'}
            </Text>
          </div>
          <div className={styles.infoRow}>
            <Text strong>{t('accountType.plan_label')}</Text>
            <Text>
              {isBasicPlan ? t('accountType.basic_free_tier') : planName}
            </Text>
          </div>
        </div>
        <Row gutter={[24, 24]} className={styles.mainSection}>
          <Col xs={24} sm={24} md={8} lg={8}>
            {!planLoading && (
              <div className={styles.usageSection}>
                <Text className={styles.usageLabel}>
                  {t('accountType.usage')}
                </Text>

                <div className={styles.usageProgress}>
                  <div className={styles.percentageText}>{totalStorage()}</div>
                  <div
                    className={styles.fill}
                    style={{
                      height: `${getHeight()}%`,
                      paddingTop: 5,
                      paddingBottom: getHeight() < 5 ? 5 : 0,
                    }}
                  >
                    <Text className={styles.usedText}>{getUsedStorage()}</Text>
                  </div>
                </div>
              </div>
            )}
          </Col>
          <Col xs={24} sm={24} md={16} lg={16}>
            <div className={styles.sectionHeader}>
              <Title level={3} className={styles.sectionTitle}>
                {t('accountType.usage_and_upgrade')}
              </Title>
              <Switch
                checked={duration}
                onChange={checked => setDuration(checked)}
                checkedChildren={t('subscription.monthly')}
                unCheckedChildren={t('subscription.yearly')}
              />
            </div>
            <Row gutter={[24, 24]} className={styles.mainSection}>
              {planLoading ? (
                <Spin size='large' />
              ) : (
                <Col xs={24} sm={24} md={24} lg={24}>
                  <div className={styles.plansSection}>
                    <Row gutter={md ? [10, 10] : [24, 24]}>
                      {plansToRender &&
                        plansToRender.map((plan, index) => {
                          const highlight =
                            String(plan?.id) === String(activeIndex);
                          return (
                            <Col
                              key={index}
                              xs={24}
                              sm={24}
                              md={12}
                              lg={12}
                              xl={12}
                            >
                              <PlanCard
                                title={plan?.nickname}
                                price={plan?.unit_amount}
                                perMonthLabel={
                                  plan?.recurring?.interval === 'year'
                                    ? t('subscriptionPlan.per_year')
                                    : t('subscriptionPlan.per_month')
                                }
                                description={`${formatStorage(
                                  Number(plan?.metadata?.MB),
                                  'MB'
                                )} ${t('subscriptionPlan.basic_description')}`}
                                isActive={highlight}
                                onClick={() => {
                                  setModal({
                                    visible: true,
                                    plan,
                                    isCurrentPlan: highlight,
                                  });
                                }}
                              />
                            </Col>
                          );
                        })}
                    </Row>
                  </div>
                </Col>
              )}
            </Row>
          </Col>
        </Row>
      </div>
      <SubscriptionModal
        visible={modal.visible}
        plan={modal.plan}
        modalData={modal}
        duration
        upgrade={
          isBasicPlan && modal?.plan?.id !== starterPlan.id ? true : false
        }
        planDetails={planDetails ? planDetails : ({} as Plans)}
        onCancel={() =>
          setModal({ visible: false, plan: null, isCurrentPlan: false })
        }
      />
    </div>
  );
};

export default AccountType;
