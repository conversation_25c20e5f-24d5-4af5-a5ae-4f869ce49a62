/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * @fileoverview API helper utilities for making HTTP requests with authentication and error handling
 * @module helpers/ApiHelper
 */

'use client';

import siteConfig from '@/config/site.config';
import { getToken } from '@/utils/CommonFunctions';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import isNull from 'lodash-es/isNull';
import isUndefined from 'lodash-es/isUndefined';
import omitBy from 'lodash-es/omitBy';
import { store } from '../store';
import { logout } from '../store/slices/authSlice';

/**
 * Default headers applied to all API requests
 * @constant {Record<string, string>}
 */
const defaultHeaders: Record<string, string> = {
  'Content-Type': 'application/json',
  Accept: 'application/json',
  api_type: 'web',
  platform: 'web',
  timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
};

export interface ApiResponse {
  status: boolean;
  message: string;

  data?: any;
}

/**
 * Supported HTTP methods for API requests
 * @typedef {'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'} HttpMethod
 */
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * Configuration options for API requests
 * @template TRequest - Type of the request data
 * @typedef {Object} ApiOptions
 * @property {string} url - The API endpoint URL
 * @property {HttpMethod} [method='GET'] - HTTP method to use
 * @property {TRequest} [data] - Request payload data
 * @property {boolean} [customUrl=false] - Whether to use the full URL as provided
 * @property {Record<string, string>} [headers] - Additional headers to include
 * @property {boolean} [formData=false] - Whether the data is FormData
 * @property {AbortSignal} [signal] - AbortController signal for request cancellation
 * @property {function} [onProgress] - Progress callback for upload/download
 */
interface ApiOptions<TRequest> {
  url: string;
  method?: HttpMethod;
  data?: TRequest;
  customUrl?: boolean;
  headers?: Record<string, string>;
  formData?: boolean;
  signal?: AbortSignal;
  onProgress?: (progress: number) => void;
}

/**
 * Standard API error response structure
 * @typedef {Object} ApiError
 * @property {string} status - Error status code or identifier
 * @property {string} message - Human-readable error message
 */
interface ApiError {
  status: string;
  message: string;
}

/**
 * Generic API request function with built-in authentication, error handling, and progress tracking
 *
 * @template TRequest - Type of the request payload data
 * @template TResponse - Type of the expected response data
 * @function getApiData
 * @param {ApiOptions<TRequest>} options - Configuration options for the API request
 * @returns {Promise<TResponse | ApiError | undefined>} Promise resolving to response data, error object, or undefined
 *
 * @example
 * // GET request
 * const users = await getApiData<{}, User[]>({
 *   url: '/api/users',
 *   method: 'GET'
 * });
 *
 * @example
 * // POST request with data
 * const newUser = await getApiData<CreateUserRequest, User>({
 *   url: '/api/users',
 *   method: 'POST',
 *   data: { name: 'John Doe', email: '<EMAIL>' }
 * });
 *
 * @example
 * // Request with progress tracking
 * const result = await getApiData<FormData, UploadResponse>({
 *   url: '/api/upload',
 *   method: 'POST',
 *   data: formData,
 *   formData: true,
 *   onProgress: (progress) => console.log(`Upload: ${progress}%`)
 * });
 */
export async function getApiData<TRequest extends object, TResponse>(
  options: ApiOptions<TRequest>
): Promise<TResponse | ApiError | undefined> {
  const {
    url,
    method = 'GET',
    data,
    customUrl = false,
    headers = {},
    formData = false,
    signal,
    onProgress,
  } = options;

  const token = getToken();
  const fullUrl = customUrl ? url : `${siteConfig.apiUrl}${url}`;

  const combinedHeaders: Record<string, string> = {
    ...defaultHeaders,
    ...headers,
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
  };

  const cleanedData = formData
    ? data
    : (omitBy(data, v => isUndefined(v) || isNull(v)) as TRequest);

  const axiosConfig: AxiosRequestConfig = {
    method,
    url: fullUrl,
    headers: combinedHeaders,
    params: method === 'GET' ? cleanedData : undefined,
    data: method !== 'GET' ? cleanedData : undefined,
    signal,
    onDownloadProgress: event => {
      if (onProgress && event.total) {
        const progress = Math.round((event.loaded * 100) / event.total);
        onProgress(progress);
      }
    },
  };

  try {
    const response: AxiosResponse<TResponse> = await axios(axiosConfig);
    const resData = response.data;

    // Optional: Auto logout if unauthorized
    if (
      (resData as any)?.isUnauthorized ||
      (response as any)?.code === 402 ||
      (response as any)?.code === 401
    ) {
      store.dispatch(logout());
      return;
    }

    return resData;
  } catch (error: unknown) {
    const axiosError = error as {
      response?: { status: number; data: ApiError };
    };

    if (
      (error as any)?.isUnauthorized ||
      (error as any)?.response?.data?.code === 402 ||
      (error as any)?.response?.data?.code === 401
    ) {
      store.dispatch(logout());
      return;
    }

    return {
      status: axiosError?.response?.data?.status || 'error',
      message:
        axiosError?.response?.data?.message || 'An unexpected error occurred',
    };
  }
}

type UploadValue = string | Blob;

interface UploadSuccessResponse {
  status: string | boolean;
  message: string;
  data?: unknown;
}

interface UploadErrorResponse {
  status: 'error';
  message: string;
}

// Enforce that additionalData values are only string or Blob
interface UploadOptions<T extends Record<string, UploadValue>> {
  url: string;
  file: File;
  fieldName?: string;
  additionalData?: T;
  headers?: Record<string, string>;
  onProgress?: (percent: number) => void;
  onSuccess?: (response: UploadSuccessResponse) => void;
  onError?: (error: UploadErrorResponse | string) => void;
  signal?: AbortSignal;
}

/**
 * Uploads a file to the given URL using XMLHttpRequest
 * @template T - Additional data values (must be string or Blob)
 * @param {UploadOptions<T>} options - Upload options
 * @param {string} options.url - URL to upload to
 * @param {File} options.file - File to upload
 * @param {string} [options.fieldName='file'] - Field name for the uploaded file
 * @param {Record<string, UploadValue>} [options.additionalData] - Additional data to include in the upload
 * @param {Record<string, string>} [options.headers] - Additional headers to include in the request
 * @param {(percent: number) => void} [options.onProgress] - Progress callback
 * @param {(response: UploadSuccessResponse) => void} [options.onSuccess] - Success callback
 * @param {(error: UploadErrorResponse | string) => void} [options.onError] - Error callback
 * @param {AbortSignal} [options.signal] - Abort signal for request cancellation
 * @example
 *  interface FileMeta {
      userId: string;
      folderId: string;
      note?: string;
    }

const file = new File(['example'], 'demo.txt');

uploadFile<FileMeta>({
  url: '/upload',
  file,
  additionalData: {
    userId: 'u123',
    folderId: 'f456',
    note: 'Important upload',
  },
  headers: {
    Authorization: `Bearer mytoken`,
  },
  onProgress: (percent) => {
    console.log(`Progress: ${percent}%`);
  },
  onSuccess: (res) => {
    console.log('Upload success:', res);
  },
  onError: (err) => {
    console.error('Upload error:', err);
  },
});

 */

export function uploadFile<T extends Record<string, UploadValue>>({
  url,
  file,
  fieldName = 'file',
  additionalData,
  headers = {},
  onProgress,
  onSuccess,
  onError,
  signal,
}: UploadOptions<T>): void {
  const xhr = new XMLHttpRequest();

  xhr.open('POST', url, true);

  for (const [key, value] of Object.entries(headers)) {
    xhr.setRequestHeader(key, value);
  }

  xhr.upload.onprogress = (event: ProgressEvent) => {
    if (event.lengthComputable && onProgress) {
      const percent = Math.round((event.loaded / event.total) * 100);
      onProgress(percent);
    }
  };

  xhr.onload = () => {
    const isJson = xhr
      .getResponseHeader('Content-Type')
      ?.includes('application/json');

    if (xhr.status >= 200 && xhr.status < 300) {
      try {
        const json: UploadSuccessResponse = isJson
          ? JSON.parse(xhr.responseText)
          : {
              status: 'success',
              message: 'Upload complete',
              data: xhr.responseText,
            };
        onSuccess?.(json);
      } catch {
        onError?.({
          status: 'error',
          message: 'Failed to parse response JSON',
        });
      }
    } else {
      onError?.({
        status: 'error',
        message: `Upload failed with status ${xhr.status}`,
      });
    }
  };

  xhr.onerror = () => {
    onError?.({
      status: 'error',
      message: 'Network error occurred during upload',
    });
  };

  if (signal) {
    signal.addEventListener('abort', () => {
      xhr.abort();
      onError?.({
        status: 'error',
        message: 'Upload cancelled by user',
      });
    });
  }

  const formData = new FormData();
  formData.append(fieldName, file);

  if (additionalData) {
    for (const [key, value] of Object.entries(additionalData)) {
      formData.append(key, value); // Now TypeScript knows it's string | Blob
    }
  }

  xhr.send(formData);
}
