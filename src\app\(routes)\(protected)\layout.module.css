.rootLayout {
  /* min-height: 95vh; */
  min-height: 100vh;
  display: flex;
  flex-direction: row;
}

.contentLayout {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background); /* Your light background */
}

.dashboardContent {
  padding: 24px;
  overflow-y: auto;
  height: calc(100vh - 80px); /* Adjust based on header height */
}

@media (max-width: 624px) {
  .dashboardContent {
    padding: 0px;
  }
}
