'use client';
import { Endpoints } from '@/config/endpoints';
import siteConfig from '@/config/site.config';
import { getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch } from '@/store/hooks';
import { updateUser } from '@/store/slices/authSlice';
import { Button, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import styles from './page.module.css';

const { Title, Text } = Typography;

interface User {
  username?: string;
  email?: string;
  role?: string;
  birth_date: string;
  gender: string;
  purpose_of_joining: string;
  goals: string[];
  interests: string[];
  hear_about_us: string;
}

const finalData = {
  type: 'completed',
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const termsArr = [
  {
    id: 1,
    title: 'Terms & Condition',
    description:
      'By creating an account, I confirm that I have read and agree to the Terms and Conditions.',
  },
  {
    id: 2,
    title: 'who can use this Service',
    description:
      'This service is designed for both individuals and agencies.Individuals—such as professionals, freelancers, or anyone seeking personal digital solutions—can use the platform to streamline their own workflows and projects. Agencies and companies can leverage the service to manage their organization’s needs or provide value-added solutions for their own clients. Whether you’re working solo or representing a business, our platform adapts to your requirements',
  },
  {
    id: 3,
    title: 'Copyright Protection',
    description:
      'Individual and agency users retain full copyright over their creations on this platform. Your work is legally protected under copyright laws in the EU, Switzerland, the United Arab Emirates, and the USA. No content will be used, shared, or distributed without your explicit consent, ensuring your intellectual property rights remain secure and respected across all supported regions.',
  },
];

const TermsPage = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();
  const permission = Notification.permission;

  const termsArr = [
    {
      id: 1,
      title: t('terms.terms_condition'),
      description: t('terms.terms_description'),
    },
    {
      id: 2,
      title: t('terms.who_can_use'),
      description: t('terms.who_can_use_description'),
    },
    {
      id: 3,
      title: t('terms.copyright_protection'),
      description: t('terms.copyright_protection_description'),
    },
  ];

  const updateUserDetails = async () => {
    setLoading(true);
    try {
      const response = await getApiData<
        typeof finalData,
        { status: boolean; message: string; data?: User }
      >({
        url: `${siteConfig.apiUrl}${Endpoints.updateUserDetails}`,
        method: 'POST',
        data: finalData,
        customUrl: true,
      });

      if (response && response.status === true) {
        if (response.data) {
          dispatch(
            updateUser(
              permission === 'granted'
                ? { user: response.data, isAuthenticated: true }
                : { user: response.data }
            )
          );
        }
        if (permission === 'granted') {
          router.replace('/dashboard');
        } else {
          router.replace('/notification-permission');
        }
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.root}>
      <div>
        <Title level={2} className={styles.title}>
          {t('terms.title')}
        </Title>
        <Text className={styles.subtitle}>{t('terms.last_update')}</Text>
      </div>
      <div className={styles.termsContainer}>
        {termsArr.map(term => (
          <div key={term.id} className={styles.termsDiv}>
            <Title level={4} className={styles.termTitle}>
              {term.id}. {term.title}
            </Title>
            <Text className={styles.termDescription}>{term.description}</Text>
          </div>
        ))}
      </div>
      <div>
        <Button
          type='primary'
          className={styles.agreeButton}
          loading={loading}
          onClick={() => {
            updateUserDetails();
          }}
        >
          Agree & Continue
        </Button>
      </div>
    </div>
  );
};

export default TermsPage;
