/* AppearanceSettings CSS Module */

.container {
  /* padding: 24px; */
  background-color: var(--color-background);
  min-height: 100vh;
}

.header {
  margin-bottom: 32px;
}

.title {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
  margin-bottom: 8px !important;
}

.subtitle {
  font-size: 16px !important;
  color: var(--color-text-secondary) !important;
  line-height: 1.5 !important;
}

.section {
  margin-bottom: 24px !important;
  border-radius: 12px !important;
  border: 1px solid var(--color-border) !important;
  background-color: var(--color-card-background) !important;
}

.sectionTitle {
  font-size: 24px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
  margin-bottom: 24px !important;
}

.settingGroup {
  margin-bottom: 24px;
}

.label {
  display: block !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
  margin-bottom: 12px !important;
}

.colorPickerContainer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.colorPicker {
  border-radius: 8px !important;
}

.editButton {
  color: var(--color-text-secondary) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: 8px !important;
  height: 40px !important;
  width: 40px !important;
}

.editButton:hover {
  color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.themeRadioGroup {
  width: 100%;
}

.themeRadio {
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px !important;
  border: 1px solid var(--color-border) !important;
  border-radius: 8px !important;
  margin-bottom: 8px !important;
  transition: all 0.3s ease !important;
}

.themeRadio:hover {
  border-color: var(--color-primary) !important;
  background-color: var(--color-background-secondary) !important;
}

.themeRadio :global(.ant-radio-checked) .ant-radio-inner {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.radioLabel {
  font-size: 14px !important;
  color: var(--color-text-primary) !important;
  margin-left: 8px !important;
}

.uploadContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.logoUpload,
.brandingUpload {
  width: 200px !important;
  height: 120px !important;
}

.logoUpload :global(.ant-upload),
.brandingUpload :global(.ant-upload) {
  width: 100% !important;
  height: 100% !important;
  border: 2px dashed var(--color-border) !important;
  border-radius: 8px !important;
  background-color: var(--color-background) !important;
}

.logoUpload :global(.ant-upload):hover,
.brandingUpload :global(.ant-upload):hover {
  border-color: var(--color-primary) !important;
}

.uploadContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 8px;
}

.uploadIcon {
  font-size: 24px !important;
  color: var(--color-text-secondary) !important;
}

.uploadText {
  font-size: 14px !important;
  color: var(--color-text-secondary) !important;
  text-align: center !important;
}

.watermarkCheckbox {
  margin-bottom: 8px !important;
}

.watermarkCheckbox :global(.ant-checkbox-checked) .ant-checkbox-inner {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.checkboxLabel {
  font-size: 16px !important;
  color: var(--color-text-primary) !important;
  margin-left: 8px !important;
}

.description {
  font-size: 14px !important;
  color: var(--color-text-secondary) !important;
  line-height: 1.5 !important;
  display: block !important;
  margin-left: 24px !important;
}

/* Dark theme specific adjustments */
html[data-theme='dark'] .section {
  background-color: var(--color-background-secondary) !important;
  border-color: var(--color-border-secondary) !important;
}

html[data-theme='dark'] .themeRadio {
  background-color: var(--color-background-tertiary) !important;
}

html[data-theme='dark'] .logoUpload :global(.ant-upload),
html[data-theme='dark'] .brandingUpload :global(.ant-upload) {
  background-color: var(--color-background-tertiary) !important;
  border-color: var(--color-border-secondary) !important;
}

/* Light theme specific adjustments */
html[data-theme='light'] .section {
  background-color: #ffffff !important;
}

html[data-theme='light'] .themeRadio {
  background-color: var(--color-primary) !important;
}
