/**
 * @fileoverview Custom hook for internationalization and translation management
 * @module hooks/useTranslation
 */

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setLoading, setTranslations } from '@/store/slices/languageSlice';
import { useCallback, useEffect, useRef } from 'react';

export type TranslationParams = Record<
  string,
  string | number | boolean | null | undefined
>;

const FALLBACK_LANGUAGE = 'en';

/**
 * Retrieves a nested value from an object using dot-notation.
 */
function getByPath<T extends Record<string, unknown>, Default = string>(
  obj: T | undefined,
  path: string
): Default | undefined {
  if (!obj) {
    return undefined;
  }

  const keys = path.split('.');

  let current: unknown = obj;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = (current as Record<string, unknown>)[key];
    } else {
      return undefined;
    }
  }

  return current as Default;
}

/**
 * Interpolates template strings with provided params.
 */
function interpolate(template: string, params?: TranslationParams): string {
  if (!params) {
    return template;
  }

  return template.replace(/\{(\w+)\}/g, (_, k: string) =>
    params[k] === null || params[k] === undefined ? `{${k}}` : String(params[k])
  );
}

/**
 * Custom hook for managing translations and internationalization
 *
 * @hook useTranslation
 * @returns {Object} Translation utilities and state
 * @returns {function} returns.t - Translation function that accepts a key and returns translated text
 * @returns {string} returns.currentLanguage - Currently selected language code
 * @returns {boolean} returns.loading - Loading state for translation fetching
 * @returns {function} returns.changeLanguage - Function to change language (handled by language selector)
 *
 * @example
 * // Basic usage in a component
 * const { t, currentLanguage, loading } = useTranslation();
 *
 * return (
 *   <div>
 *     <h1>{t('common.welcome')}</h1>
 *     <p>{t('dashboard.subtitle')}</p>
 *     {loading && <span>Loading translations...</span>}
 *   </div>
 * );
 *
 * @example
 * // Using nested translation keys
 * const { t } = useTranslation();
 * const errorMessage = t('validation.errors.required'); // Accesses nested object
 *
 * @description
 * This hook provides internationalization functionality including:
 * - Automatic loading of translation files based on current language
 * - Caching to prevent duplicate requests
 * - Nested key support with dot notation (e.g., 'common.welcome')
 * - Fallback to key name if translation is not found
 * - Loading state management
 * - Integration with Redux store for language state
 */
export const useTranslation = () => {
  const dispatch = useAppDispatch();
  const { currentLanguage, translations, loading } = useAppSelector(
    state => state.language
  );

  // Use ref to track loading to prevent duplicate requests
  const loadingRef = useRef<Set<string>>(new Set());

  useEffect(() => {
    const loadTranslations = async () => {
      console.log('🔄 Loading translations for:', currentLanguage);
      console.log('📦 Current translations:', translations);
      console.log(
        '🔍 Checking if translations exist for:',
        currentLanguage,
        !!translations[currentLanguage]
      );

      // Check if translations are already loaded or currently loading
      if (
        translations[currentLanguage] ||
        loadingRef.current.has(currentLanguage)
      ) {
        console.log('✅ Translations already available for:', currentLanguage);
        // return;
      }

      // Mark as loading
      loadingRef.current.add(currentLanguage);
      dispatch(setLoading(true));
      console.log('🚀 Fetching translations for:', currentLanguage);

      try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const response = require(`@/locales/${currentLanguage}.json`);
        // const response = await fetch(`/locales/${currentLanguage}.json`);
        console.log('📡 Fetch response:', response.status, response.ok);

        if (!response) {
          throw new Error(`Failed to fetch translations: ${response.status}`);
        }

        const data = response;
        console.log('📄 Fetched data:', data);

        // Update translations
        const newTranslations = {
          ...translations,
          [currentLanguage]: data,
        };
        console.log('🆕 Dispatching new translations:', newTranslations);

        dispatch(setTranslations(newTranslations));
        console.log('✅ Translations dispatched successfully');
      } catch (error) {
        console.error('❌ Failed to load translations:', error);
      } finally {
        // Remove from loading set
        loadingRef.current.delete(currentLanguage);
        dispatch(setLoading(false));
      }
    };

    loadTranslations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentLanguage, dispatch]); // Only depend on language and dispatch to avoid infinite re-renders

  /**
   * Translation function with **type-safe** nested key lookup and interpolation.
   *
   * @example
   * t('subscriptionPlan.plan_active_desc', { planName: 'Pro', endDate: 'Aug 31, 2025', interval: 'monthly' })
   */
  const t = useCallback(
    (key: string, params?: TranslationParams): string => {
      const dict =
        translations[currentLanguage] ?? translations[FALLBACK_LANGUAGE];

      const raw = getByPath<typeof dict, string>(dict, key);
      if (typeof raw !== 'string') {
        return key;
      }

      return interpolate(raw, params);
    },
    [currentLanguage, translations]
  );

  return {
    t,
    currentLanguage,
    loading,
    changeLanguage: () => {
      // This will be handled by the language selector component
    },
  };
};
