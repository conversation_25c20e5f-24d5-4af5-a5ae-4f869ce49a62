'use client';

import {
  BulbOutlined,
  CheckCircleOutlined,
  LogoutOutlined,
  ProjectOutlined,
  RocketOutlined,
  SettingOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Layout,
  Row,
  Space,
  Statistic,
  Typography,
} from 'antd';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import styled from 'styled-components';

import ErrorTester from '@/components/debug/ErrorTester';
import PWADebug from '@/components/debug/PWADebug';
import AntdExample from '@/components/examples/AntdExample';
import ThemeToggle from '@/components/ui/ThemeToggle';
import { useTheme } from '@/contexts/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { logout } from '@/store/slices/authSlice';
import { styledTheme } from '@/theme/antdTheme';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

const DashboardLayout = styled(Layout)<{ $isDark: boolean }>`
  min-height: 100vh;
  background: ${props =>
    props.$isDark
      ? styledTheme.gradients.backgroundDark
      : styledTheme.gradients.background};
`;

const StyledHeader = styled(Header)<{ $isDark: boolean }>`
  background: ${props =>
    props.$isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.9)'};
  backdrop-filter: blur(20px);
  border-radius: 16px;
  margin: 1.5rem;
  padding: 0 2rem;
  height: auto;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: ${props =>
    props.$isDark
      ? styledTheme.shadows.dark.medium
      : styledTheme.shadows.light.medium};
  border: 1px solid
    ${props =>
      props.$isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.2)'};
`;

const StyledContent = styled(Content)`
  padding: 0 1.5rem 1.5rem;
`;

const WelcomeSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const ActionSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const StatsCard = styled(Card)<{ $isDark: boolean }>`
  background: ${props =>
    props.$isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.9)'};
  backdrop-filter: blur(20px);
  border: 1px solid
    ${props =>
      props.$isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.2)'};
  box-shadow: ${props =>
    props.$isDark
      ? styledTheme.shadows.dark.medium
      : styledTheme.shadows.light.medium};
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${props =>
      props.$isDark
        ? styledTheme.shadows.dark.large
        : styledTheme.shadows.light.large};
  }

  .ant-statistic-content {
    color: ${styledTheme.colors.primary};
  }

  .ant-statistic-title {
    color: inherit;
  }
`;

const GradientButton = styled(Button)`
  background: ${styledTheme.gradients.primary};
  border: none;
  color: white;
  font-weight: 600;

  &:hover {
    background: ${styledTheme.gradients.primaryHover} !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }
`;

const FeatureCard = styled(Card)<{ $isDark: boolean }>`
  background: ${props =>
    props.$isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.9)'};
  backdrop-filter: blur(20px);
  border: 1px solid
    ${props =>
      props.$isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.2)'};
  box-shadow: ${props =>
    props.$isDark
      ? styledTheme.shadows.dark.medium
      : styledTheme.shadows.light.medium};
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props =>
      props.$isDark
        ? styledTheme.shadows.dark.large
        : styledTheme.shadows.light.large};
  }
`;

const NewDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { theme } = useTheme();
  const router = useRouter();
  const [showAntdExample, setShowAntdExample] = useState(false);
  const isDark = theme === 'dark';

  const handleLogout = (): void => {
    dispatch(logout());
    // Immediate redirect after logout
    router.push('/login');
  };

  const statsData = [
    {
      title: 'Total Projects',
      value: 24,
      icon: <ProjectOutlined style={{ color: styledTheme.colors.primary }} />,
    },
    {
      title: 'Completed Tasks',
      value: 156,
      icon: (
        <CheckCircleOutlined style={{ color: styledTheme.colors.success }} />
      ),
    },
    {
      title: 'Success Rate',
      value: 98,
      suffix: '%',
      icon: <TrophyOutlined style={{ color: styledTheme.colors.warning }} />,
    },
    {
      title: 'Active Users',
      value: 1.2,
      suffix: 'K',
      icon: <UserOutlined style={{ color: styledTheme.colors.info }} />,
    },
  ];

  return (
    <DashboardLayout $isDark={isDark}>
      <StyledHeader $isDark={isDark}>
        <WelcomeSection>
          <Avatar
            size={48}
            icon={<UserOutlined />}
            style={{
              background: styledTheme.gradients.primary,
              border: '2px solid rgba(255, 255, 255, 0.2)',
            }}
          />
          <UserInfo>
            <Title level={4} style={{ margin: 0, color: 'inherit' }}>
              Welcome back, {user?.username}!
            </Title>
            <Text type='secondary'>Here&apos;s your dashboard overview</Text>
          </UserInfo>
        </WelcomeSection>

        <ActionSection>
          <ThemeToggle />
          <Button
            icon={<SettingOutlined />}
            type='text'
            style={{ color: 'inherit' }}
          >
            Settings
          </Button>
          <Button icon={<LogoutOutlined />} onClick={handleLogout} danger>
            Logout
          </Button>
        </ActionSection>
      </StyledHeader>

      <StyledContent>
        <PWADebug />
        <Row gutter={[24, 24]} style={{ marginBottom: '2rem' }}>
          {statsData.map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <StatsCard $isDark={isDark}>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  suffix={stat.suffix}
                  prefix={stat.icon}
                />
              </StatsCard>
            </Col>
          ))}
        </Row>

        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <FeatureCard
              $isDark={isDark}
              title={
                <Space>
                  <RocketOutlined
                    style={{ color: styledTheme.colors.primary }}
                  />
                  <span>Quick Actions</span>
                </Space>
              }
            >
              <Space
                direction='vertical'
                size='large'
                style={{ width: '100%' }}
              >
                <Text>
                  Welcome to your modern dashboard! This boilerplate includes
                  everything you need to build a professional React application
                  with Next.js, Redux, and Ant Design.
                </Text>

                <Divider />

                <Space wrap>
                  <GradientButton
                    icon={<BulbOutlined />}
                    onClick={() => setShowAntdExample(!showAntdExample)}
                  >
                    {showAntdExample ? 'Hide' : 'Show'} Ant Design Demo
                  </GradientButton>

                  <Button icon={<ProjectOutlined />}>New Project</Button>

                  <Button icon={<SettingOutlined />}>Preferences</Button>
                </Space>
              </Space>
            </FeatureCard>
          </Col>

          <Col xs={24} lg={8}>
            <FeatureCard $isDark={isDark} title='Features Included'>
              <Space
                direction='vertical'
                size='small'
                style={{ width: '100%' }}
              >
                <Text>✅ Dark/Light Mode Toggle</Text>
                <Text>✅ Redux with Persistence</Text>
                <Text>✅ Ant Design Components</Text>
                <Text>✅ Styled Components</Text>
                <Text>✅ TypeScript Support</Text>
                <Text>✅ ESLint + Prettier</Text>
                <Text>✅ Error Boundaries</Text>
                <Text>✅ Responsive Design</Text>
              </Space>
            </FeatureCard>
          </Col>
        </Row>

        {showAntdExample && (
          <div style={{ marginTop: '2rem' }}>
            <AntdExample />
          </div>
        )}
      </StyledContent>

      <ErrorTester />
    </DashboardLayout>
  );
};

export default NewDashboard;
