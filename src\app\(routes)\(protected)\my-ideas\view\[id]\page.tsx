'use client';

import { FolderData } from '@/components/myIdeas';
import MyIdeasTab from '@/components/myIdeas/MyIdeasTab';
import { useNotification } from '@/components/Notification/NotificationContext';
import UploadArea from '@/components/uploadFiles/page';
import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { LoaderTypeParams } from '@/utils/CommonInterfaces';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

const IdeaViewPage = () => {
  const pathname = usePathname();
  const notification = useNotification();
  const folderId = pathname.split('/').pop();
  const { t } = useTranslation();
  const [folderDetails, setFolderDetails] = useState<FolderData | null>(null);

  const [loader, setLoader] = useState(false);

  interface Params {
    id: string | undefined;
  }

  const getFolderDetails = async (type?: LoaderTypeParams) => {
    if (!folderId) {
      notification.error({
        message: 'Error',
        description: 'Folder id not found',
      });
      return;
    }
    if (type !== 'silent') {
      setLoader(true);
    }
    try {
      const response = await getApiData<Params, ApiResponse>({
        url: `${Endpoints.getFolderById}/${folderId}`,
        method: 'GET',
      });
      if (response && response.status === true && response.data) {
        setFolderDetails(response.data);
        console.log('Folder details:', response.data);
      } else {
        notification.error({
          message: 'Error',
          description: response?.message || t('common.error'),
        });
      }
      setLoader(false);
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error?.message || t('common.error'),
      });
      setLoader(false);
      console.error('Error fetching folder details:', error);
    }
  };

  useEffect(() => {
    getFolderDetails();
    return () => {};
  }, []);

  return folderDetails?.first_step ? (
    <UploadArea
      setFolderDetails={setFolderDetails}
      folderDetails={folderDetails}
      updateFolderDetails={getFolderDetails}
    />
  ) : (
    <MyIdeasTab
      folderDetails={folderDetails}
      loader={loader}
      updateFolderDetails={(type?: LoaderTypeParams) => {
        getFolderDetails(type);
      }}
    />
  );
};

export default IdeaViewPage;
