/* CreateNewFolderCard CSS Module */

.styledCard {
  border-radius: 12px !important;
  border: 1px solid var(--color-primary) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  height: 200px !important;
  background-color: var(--color-white) !important;
  box-shadow: 0 8px 24px hsla(0, 0%, 0%, 0.12) !important;
}

html[data-theme='light'] .styledCard {
  background-color: var(--color-white) !important;
}

html[data-theme='dark'] .styledCard {
  background-color: transparent !important;
  box-shadow: 0;
  border: 1px solid #ffffff !important;
}

.styledCard:hover {
  transform: translateY(-2px) !important;
}

.styledCard :global(.ant-card-body) {
  padding: 16px !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

.plusIcon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 2px solid var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

html[data-theme='light'] .styledCard:hover .plusIcon {
  background-color: var(--color-white);
}

html[data-theme='dark'] .styledCard:hover .plusIcon {
  background-color: transparent;
}

html[data-theme='light'] .styledCard .plusIcon {
  border-color: var(--color-primary);
}

html[data-theme='dark'] .styledCard .plusIcon {
  border-color: var(--color-white);
}

.styledCard:hover .plusIcon .anticon {
  color: white !important;
  background-color: var(--color-white) !important;
}

.createText {
  font-weight: 600 !important;
  font-size: 16px !important;
  line-height: 1.4 !important;
}

html[data-theme='light'] .createText {
  color: var(--color-primary);
}

html[data-theme='dark'] .createText {
  color: var(--color-white);
}

html[data-theme='light'] .styledCard:hover .createText {
  color: var(--color-primary);
}

html[data-theme='dark'] .styledCard:hover .createText {
  color: var(--color-white);
}
