.mobileContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.mobileCard {
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.cardTitle {
  font-size: 16px;
  display: block;
  margin-bottom: 4px;
}

.cardSubtitle {
  font-size: 14px;
  display: block;
  margin-bottom: 8px;
  color: #888;
}

.cardBody {
  margin-top: 8px;
}

.cardRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  gap: 12px;
}

.cardRow:last-child {
  border-bottom: none;
}

.cardLabel {
  font-weight: 600;
  margin-right: 8px;
}

.table {
  margin-top: 20px;
}

.table :global(.ant-table-tbody > tr > td) {
  border-bottom: none !important;
}

.table :global(.ant-table-thead > tr > th) {
  background-color: transparent !important;
  border-bottom: 1px solid #000000;
}

:global(html[data-theme='dark']) .table :global(.ant-table-thead > tr > th) {
  border-bottom: 1px solid #edeff1 !important;
}

.tableHeader {
  font-size: 18px;
  font-weight: 400;
  color: #000000;
  font-family: var(--font-poppins) !important;
}

.table :global(.ant-table-thead > tr > th) {
  background: #fafafa !important;
  font-weight: 400 !important;
  color: #000000 !important;
  font-size: 18px !important;
  font-family: var(--font-poppins) !important;
}

:global(html[data-theme='dark']) .tableHeader {
  color: #fff !important;
}

@media (max-width: 1200px) {
  .tableHeader {
    font-size: 16px !important;
  }

  .table :global(.ant-table-thead > tr > th) {
    font-size: 16px !important;
  }
}

@media (max-width: 900px) {
  .tableHeader {
    font-size: 16px !important;
  }
  .table :global(.ant-table-thead > tr > th) {
    font-size: 15px !important;
  }
}

@media (max-width: 600px) {
  .tableHeader {
    font-size: 15px !important;
  }
}
