/* profile.module.css */

.container {
  height: 80vh;
  background-color: var(--color-background);
  display: flex;
  /* align-items: center; */
  justify-content: center;
  padding: 24px;
}

.header {
  padding: 0px 20px 0px;
}

.backButton {
  padding: 0;
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
}

.backButton:hover {
  color: var(--color-text-primary);
}
.title {
  text-align: center;
  margin-bottom: 32px;
}

@media screen and (max-width: 768px) {
  .container {
    height: 80vh;
  }
  .title {
    text-align: center;
    margin-bottom: 32px;
    margin-top: 0px;
  }
  .backButton {
    margin-top: 0px !important;
  }
  .header {
    padding: 0px 0px 0px;
  }
}

@media screen and (max-width: 480px) {
  .container {
    height: 100vh;
  }
  .title {
    text-align: center;
    margin-bottom: 32px;
    margin-top: 32px;
  }
  .backButton {
    margin-top: 40px;
  }
  .header {
    padding: 0px 0px 0px;
  }
}
