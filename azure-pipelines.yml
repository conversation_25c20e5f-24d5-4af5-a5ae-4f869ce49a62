# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger: none
pr: none

variables:

  # Azure Resource Manager connection created during pipeline creation
  azureSubscription: '135902ff-5aef-4d1c-8023-53ee5dd5b5b6'

  # Web app name
  webAppName: 'wa-smi-dev-fe'

  # Environment name
  environmentName: 'wa-smi-dev-fe'

  # Agent VM image name
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    variables:
    - group: 'vg-smi-web-dev'
    pool:
      vmImage: $(vmImageName)

    steps:
    - task: NodeTool@0
      inputs:
        versionSource: 'spec'
        versionSpec: '20.x'
      displayName: 'Install Node.js'

    - script: |
        npm install -g yarn
      displayName: 'Install Yarn'

    - script: |
        export NEXT_PUBLIC_GOOGLE_CLIENT_ID=$(NEXT_PUBLIC_GOOGLE_CLIENT_ID)
        export NEXT_PUBLIC_LINKEDIN_CLIENT_ID=$(NEXT_PUBLIC_LINKEDIN_CLIENT_ID)
        export NEXT_PUBLIC_APPLE_CLIENT_ID=$(NEXT_PUBLIC_APPLE_CLIENT_ID)
        export NEXT_PUBLIC_FACEBOOK_APP_ID=$(NEXT_PUBLIC_FACEBOOK_APP_ID)
        export DISABLE_ANTD_COMPATIBLE_WARNING=$(DISABLE_ANTD_COMPATIBLE_WARNING)
        export NEXT_PUBLIC_BASE_URL=$(NEXT_PUBLIC_BASE_URL)
        export NEXT_PUBLIC_WEBSITE_URL=$(NEXT_PUBLIC_WEBSITE_URL)
        yarn
        yarn build
      displayName: 'yarn install with force and yarn build'

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
        includeRootFolder: false
        archiveType: zip
        archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
        replaceExistingArchive: true

    - upload: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
      artifact: drop

- stage: Deploy
  displayName: Deploy stage
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: Deploy
    displayName: Deploy
    environment: $(environmentName)
    pool:
      vmImage: $(vmImageName)
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Azure Web App Deploy: wa-smi-dev-fe'
            inputs:
              azureSubscription: '$(azureSubscription)'
              appType: 'webAppLinux'
              appName: '$(webAppName)'
              package: '$(Pipeline.Workspace)/drop/$(Build.BuildId).zip'
              runtimeStack: 'NODE|20-lts'
              startUpCommand: 'npm run startAzure'