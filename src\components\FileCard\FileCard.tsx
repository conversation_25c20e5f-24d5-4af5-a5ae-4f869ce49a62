import { useNotification } from '@/components/Notification/NotificationContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import {
  ClockCircleOutlined,
  CloseOutlined,
  CodeOutlined,
  FileOutlined,
  FileZipOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Button, Card, Input, Modal, Progress, Space, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import {
  BsFiletypeDoc,
  BsFiletypeDocx,
  BsFiletypeGif,
  BsFiletypeJpg,
  BsFiletypeMov,
  BsFiletypeMp3,
  BsFiletypeMp4,
  BsFiletypePng,
  BsFiletypeSvg,
  BsFiletypeTxt,
  BsFiletypeWav,
} from 'react-icons/bs';
import { GrDocumentPdf } from 'react-icons/gr';
import { LuFileLock2 } from 'react-icons/lu';
import { SiJpeg } from 'react-icons/si';
import { TfiDownload } from 'react-icons/tfi';
import { Endpoints } from '../../config/endpoints';
import { getApiData } from '../../helpers/ApiHelper';
import useMediaQuery from '../../hooks/useMediaQuery';
import { formatBytes, handleDownload } from '../../utils/CommonFunctions';
import { UploadProgress } from '../uploadFiles/page';
import styles from './FileCard.module.css';

const { Title, Text } = Typography;

interface Props {
  filename?: string;
  description?: string;
  size?: string | number;
  onDownload?: () => void;
  onRename?: (newName: string) => void;
  showDescription?: boolean;
  showSize?: boolean;
  showDelete?: boolean;
  progress?: UploadProgress;
  file_url?: string;
  style?: React.CSSProperties;
  type?: string;
  creator?: string;
  createdAt?: Date | string;
  acessRole?: string;
  creatorId?: string;
  removedFile?: (id: string | number) => void;
  fileId?: string | number;
  folderId?: string | number;
}

export interface UploadProgressData {
  percent: number;
  status: 'active' | 'done' | 'error';
}

const FileCard = ({
  fileId = '',
  filename = '',
  description = '',
  size = '',
  onRename = () => {},
  showDescription = false,
  showSize = true,
  showDelete = false,
  progress,
  file_url,
  style,
  type,
  creator = '',
  createdAt = '',
  acessRole = '',
  creatorId = '',
  removedFile = () => {},
  folderId = '',
  // file,
}: Props) => {
  const { theme } = useTheme();
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(filename);
  const inputRef = useRef<InputRef>(null);
  const { user } = useAppSelector(s => s.auth);
  const [removeFileVisible, setRemoveFileVisible] = useState({
    visible: false,
    fileId: '',
  });
  const [removeLoader, setRemoveLoader] = useState(false);
  const { t } = useTranslation();
  const md = useMediaQuery('(min-width: 1200px)');
  const sm = useMediaQuery('(max-width: 600px)');
  const notification = useNotification();

  // Update editedName when filename prop changes
  useEffect(() => {
    setEditedName(filename);
  }, [filename]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      // Select filename without extension
      const nameWithoutExtension = getFileNameWithoutExtension(editedName);
      inputRef.current.setSelectionRange(0, nameWithoutExtension.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditing]);

  const getFileExtension = (filename: string): string => {
    return filename.split('.').pop()?.toLowerCase() || '';
  };

  const getFileNameWithoutExtension = (filename: string): string => {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(0, lastDotIndex) : filename;
  };

  const getFileInfo = (filename: string) => {
    const extension = getFileExtension(filename);

    type FileTypeInfo = {
      icon: React.ComponentType<{ className?: string }>;
    };

    const fileTypes: Record<string, FileTypeInfo> = {
      pdf: {
        icon: GrDocumentPdf,
      },
      doc: {
        icon: BsFiletypeDoc,
      },
      docx: {
        icon: BsFiletypeDocx,
      },
      txt: {
        icon: BsFiletypeTxt,
      },

      jpg: {
        icon: BsFiletypeJpg,
      },
      jpeg: {
        icon: SiJpeg,
      },
      png: {
        icon: BsFiletypePng,
      },
      gif: {
        icon: BsFiletypeGif,
      },
      svg: {
        icon: BsFiletypeSvg,
      },

      mp4: {
        icon: BsFiletypeMp4,
      },
      mov: {
        icon: BsFiletypeMov,
      },

      mp3: {
        icon: BsFiletypeMp3,
      },
      wav: {
        icon: BsFiletypeWav,
      },

      zip: {
        icon: FileZipOutlined,
      },
      rar: {
        icon: FileZipOutlined,
      },

      js: {
        icon: CodeOutlined,
      },
      html: {
        icon: CodeOutlined,
      },
      css: {
        icon: CodeOutlined,
      },
      py: {
        icon: CodeOutlined,
      },
    };

    return (
      fileTypes[extension as keyof typeof fileTypes] || {
        icon: FileOutlined,
        className: styles.default,
        badgeColor: '#8c8c8c',
        label: extension?.toUpperCase() || 'FILE',
      }
    );
  };

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    const trimmedName = editedName.trim();
    if (trimmedName && trimmedName !== filename) {
      onRename(trimmedName);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedName(filename);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const fileInfo = getFileInfo(filename);
  const IconComponent = fileInfo.icon;

  const isValidProgress = (p: unknown): p is UploadProgressData => {
    if (typeof p !== 'object' || p === null) {
      return false;
    }

    const obj = p as Record<string, unknown>;

    return typeof obj.percent === 'number' && typeof obj.status === 'string';
  };
  interface RemoveAccessResponse {
    status: boolean;
    message: string;
  }
  const removeFile = async () => {
    setRemoveLoader(true);
    try {
      const data = {
        folder_id: folderId,
        file_id: fileId,
      };
      const res = await getApiData<
        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        {},
        RemoveAccessResponse
      >({
        url: `${Endpoints.deleteFile}`,
        method: 'POST',
        data: data,
      });
      if (res && res.status === true) {
        notification.success({
          message: t('myIdeasTabs.fileDeleteSuccess'),
          description:
            res?.message || t('myIdeasTabs.fileDeleteSuccessMessage'),
        });
        setRemoveLoader(false);
        removedFile(fileId);
        setRemoveFileVisible({ visible: false, fileId: '' });
      } else {
        setRemoveLoader(false);
        notification.error({
          message: t('myIdeasTabs.fileDeleteFailed'),
          description: res?.message || t('error.something_went_wrong'),
        });
      }
    } catch (error) {
      setRemoveLoader(false);
      console.error('Error marking notification as read:', error);
    }
  };

  return (
    <Card
      className={styles.fileCard}
      bodyStyle={{ padding: sm ? '12px' : '20px' }}
      style={style}
    >
      {showDelete && (
        <div
          className={styles.closeIconDiv}
          onClick={() => {
            setRemoveFileVisible({ visible: true, fileId: '' });
          }}
        >
          <CloseOutlined className={styles.closeIcon} />
        </div>
      )}

      {isValidProgress(progress) && (
        <div className={styles.progressBar}>
          <div style={{ marginTop: 12 }}>
            <Progress
              type='circle'
              percent={progress.percent || 0}
              size='small'
              showInfo={false}
              status={
                progress?.status === 'error'
                  ? 'exception'
                  : progress?.status === 'done'
                    ? 'success'
                    : 'active'
              }
            />
          </div>
        </div>
      )}
      <Space direction='vertical' size={0} style={{ width: '100%' }}>
        <div className={styles.iconContainer}>
          {type === 'Sealed' ? (
            <LuFileLock2 className={styles.fileIcon} />
          ) : (
            <IconComponent className={styles.fileIcon} />
          )}
        </div>

        {isEditing ? (
          <Input
            ref={inputRef}
            value={editedName}
            onChange={e => setEditedName(e.target.value)}
            onKeyDown={handleKeyPress}
            onBlur={handleSave}
            size='small'
            style={{
              background: 'transparent',
              border: 'none',
              boxShadow: 'none',
              padding: 0,
              fontSize: sm ? '14px' : md ? '18px' : '22px',
              fontWeight: 600,
              textAlign: 'left',
              marginBottom: '8px',
              marginTop: '5px',
            }}
          />
        ) : (
          <Title
            level={4}
            className={styles.fileName}
            // onDoubleClick={handleDoubleClick}
            style={{ cursor: 'pointer' }}
            ellipsis={true}
          >
            {filename}
          </Title>
        )}

        {showDescription && (
          <div style={{ marginBottom: '10px' }}>
            <Text className={styles.description}>{description}</Text>
          </div>
        )}

        {showSize && (
          <div>
            <Text className={styles.description}>
              {type === 'Sealed' ? 'Total Size:' : 'File Size:'}
            </Text>
            <Text className={styles.description}>
              {' '}
              {formatBytes(Number(size))}
            </Text>
          </div>
        )}

        <div>
          <div>
            <UserOutlined />
            <Text className={styles.description}> {creator || '-'}</Text>
          </div>

          <div>
            <ClockCircleOutlined />
            <Text className={styles.description}>
              {' '}
              {createdAt ? dayjs(createdAt).format('DD/MM/YYYY hh:mm a') : '-'}
            </Text>
          </div>
        </div>

        {!progress && type !== 'Sealed' && (
          <Button
            type='link'
            icon={<TfiDownload />}
            disabled={
              acessRole === 'legal_user' || creatorId === user?._id
                ? false
                : true
            }
            onClick={() => {
              if (file_url) {
                handleDownload(file_url);
              }
            }}
            className={styles.downloadButton}
            style={{
              marginTop: showSize ? '10px' : '0px',
            }}
          >
            Download
          </Button>
        )}
      </Space>

      <Modal
        title={null}
        open={removeFileVisible?.visible}
        onCancel={() => {
          setRemoveFileVisible({ visible: false, fileId: '' });
        }}
        centered
        closable={false}
        onOk={() => {
          removeFile();
        }}
        confirmLoading={removeLoader}
        width={400}
      >
        <Space direction='vertical' style={{ width: '100%' }} size='large'>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              textAlign: 'center',
            }}
          >
            <Title
              level={5}
              style={{
                color:
                  theme === 'dark'
                    ? 'var(--color-text-tertiary)'
                    : 'var(--color-text-primary)',
              }}
              className={styles.logoutTitle}
            >
              {t('myIdeasTabs.removeFileConfirmation')}
            </Title>
          </div>
        </Space>
      </Modal>
    </Card>
  );
};

export default FileCard;
