'use client';

import { TeamOutlined, UserOutlined } from '@ant-design/icons';
import { Card, Col, Row, Typography } from 'antd';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';

import { useTranslation } from '@/hooks/useTranslation';
import { setPreAuthRole } from '@/store/slices/authSlice';
import { images } from '../../config/images';
import { useAppDispatch } from '../../store/hooks';
import styles from './SelectRole.module.css';

const { Title, Text } = Typography;

const SelectRole: React.FC = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const handleSelectRole = (role: 'individual' | 'agency') => {
    dispatch(setPreAuthRole(role));
    router.push('/login');
  };

  return (
    <div className={styles.container}>
      <div className={styles.topText}>
        <Text className={styles.topLabel}>
          {t('selectRole.select_role_title')}
        </Text>
      </div>

      <div
        className={styles.backgroundWrapper}
        style={{
          backgroundImage: `url(${images.select_role_background})`,
        }}
      >
        <div className={styles.mainContent}>
          <div className={styles.overlayCard}>
            <Row
              gutter={[
                { xs: 16, sm: 24, md: 48, lg: 48, xl: 48 }, // horizontal gutter
                { xs: 16, sm: 24, md: 48, lg: 48, xl: 48 }, // vertical gutter
              ]}
              align='middle'
              justify='space-between'
            >
              <Col xs={24} lg={12}>
                <div className={styles.leftSection}>
                  <Image
                    src={images.swiss_trust}
                    alt='Swiss Trust Layer'
                    width={210}
                    height={150}
                    className={styles.swiss_image}
                  />
                  <Title level={1} className={styles.brand}>
                    {t('selectRole.sealmyidea')}
                  </Title>
                  <Text className={styles.subtitle}>
                    {t('selectRole.copy_right')}
                  </Text>
                </div>
              </Col>

              <Col xs={24} lg={12}>
                <div className={styles.rightSection}>
                  <Card
                    hoverable
                    onClick={() => handleSelectRole('individual')}
                    className={styles.card}
                  >
                    <div className={styles.cardContent}>
                      <UserOutlined className={styles.icon} />
                      <Title className={styles.cardTitle} level={4}>
                        {t('selectRole.individual_title')}
                      </Title>
                    </div>
                  </Card>

                  <Card
                    hoverable
                    onClick={() => handleSelectRole('agency')}
                    className={styles.card}
                  >
                    <div className={styles.cardContent}>
                      <TeamOutlined className={styles.icon} />
                      <Title className={styles.cardTitle} level={4}>
                        {t('selectRole.agency_title')}
                      </Title>
                    </div>
                  </Card>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </div>

      <div className={styles.footer}>
        <Text className={styles.footerText}>{t('selectRole.footer_text')}</Text>
      </div>
    </div>
  );
};

export default SelectRole;
