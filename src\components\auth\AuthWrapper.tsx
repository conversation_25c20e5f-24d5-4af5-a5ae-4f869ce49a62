'use client';

import { Card } from 'antd';
import React from 'react';

import styles from './AuthWrapper.module.css';

interface AuthWrapperProps {
  children: React.ReactNode;
  showThemeToggle?: boolean;
}

export default function AuthWrapper({ children }: AuthWrapperProps) {
  return (
    <div className={styles.authContainer}>
      <Card className={styles.authCard}>{children}</Card>
    </div>
  );
}

// Export individual style classes for use in components
export const authStyles = styles;
