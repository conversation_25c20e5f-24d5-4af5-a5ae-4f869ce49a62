.container {
  background-color: var(--color-background);
}

.pageTitle {
  font-size: 32px !important;
  font-family: var(--font-radio-canada);
  font-weight: 600 !important;
}

.wrapper {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  height: 350px;
  background-color: #efefef;
  border-radius: 13px !important;
  border: 1px dashed #555555;
}

:global(html[data-theme='dark']) .wrapper {
  background-color: #153f6f !important;
}

.dragger {
  border-radius: 8px !important;
  background-color: #efefef !important;
}

.icon {
  font-size: 65px;
  color: var(--color-primary);
}

:global(html[data-theme='dark']) .icon {
  color: #8edafe !important;
}

.title {
  font-weight: 600;
  font-size: 22px !important;
  margin-bottom: 4px;
  font-family: var(--font-radio-canada);
  color: var(--color-black);
}

:global(html[data-theme='dark']) .title {
  color: #ffff !important;
}

.subTitle {
  color: #555555;
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: 400;
  font-family: var(--font-poppins);
}

.pageSubTitle {
  color: #555555 !important;
  font-size: 16px !important;
  font-weight: 300 !important;
  font-family: var(--font-poppins) !important;
}

:global(html[data-theme='dark']) .subTitle {
  color: #edeff1 !important;
}

:global(html[data-theme='dark']) .pageSubTitle {
  color: #edeff1 !important;
}

.orText {
  font-size: 18px;
  color: var(--color-black);
  margin: 8px 0 0;
  font-weight: 500;
  font-family: var(--font-radio-canada);
}

:global(html[data-theme='dark']) .orText {
  color: #ffff !important;
}

.browse {
  color: var(--color-primary);
  font-weight: 500;
  text-decoration: none;
  margin-top: 4px;
  cursor: pointer;
  font-size: 16px;
}

:global(html[data-theme='dark']) .browse {
  color: #8edafe !important;
}

.btnParent {
  display: flex;
  justify-content: center;
  width: 100%;
}

.buttonGroup {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  gap: 12px;
  width: 600px;
}

@media (max-width: 1200px) {
  .title {
    font-size: 18px !important;
  }
  .subTitle {
    font-size: 14px !important;
  }
  .orText {
    font-size: 16px !important;
  }
  .browse {
    font-size: 14px !important;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
    width: 100vw;
  }
  .wrapper {
    height: 250px;
  }
  .title {
    font-size: 16px !important;
  }
  .pageSubTitle {
    font-size: 14px !important;
  }
  .subTitle {
    font-size: 12px !important;
  }
  .orText {
    font-size: 14px !important;
  }
  .browse {
    font-size: 12px !important;
  }
  .buttonGroup {
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-top: 40px;
    gap: 12px;
    width: 600px;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 24px !important;
  }
}
