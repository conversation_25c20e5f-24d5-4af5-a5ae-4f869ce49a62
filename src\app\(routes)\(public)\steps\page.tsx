'use client';

import { useNotification } from '@/components/Notification/NotificationContext';
import ProgressStepBar from '@/components/StepIndicator/StepIndicator';
import StepFour from '@/components/Steps/HearAbout';
import InterestsPage from '@/components/Steps/Interests';
import StepTwo from '@/components/Steps/SelectGoal';
import StepOne from '@/components/Steps/TellAbout';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import siteConfig from '@/config/site.config';
import { useTheme } from '@/contexts/ThemeContext';
import { getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useAppDispatch } from '@/store/hooks';
import { updateUser } from '@/store/slices/authSlice';
import { colors } from '@/theme/antdTheme';
import { But<PERSON>, Col, Form, Row, Typography } from 'antd';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useTranslation } from '../../../../hooks/useTranslation';

import styles from './steps.module.css';

interface UserDetails {
  goals?: string;
  interests?: string;
  hearAboutUs?: string;
  gender?: 'male' | 'female' | 'other' | '';
  birthDate?: string;
  purposeOfJoining?: string;
  fullName?: string;
  email?: string;
  role?: 'individual' | 'agency' | '';
  birthday?: string;
  purpose?: string;
  type?: string;
  platform?: string;
}

interface User {
  username?: string;
  email?: string;
  role?: string;
  birth_date: string;
  gender: string;
  purpose_of_joining: string;
  goals: string[];
  interests: string[];
  hear_about_us: string;
}

const { Title, Text } = Typography;

export default function OnboardingPage() {
  const searchParams = useSearchParams();
  const stepParam = searchParams.get('step');
  const initialStep = stepParam ? parseInt(stepParam, 10) : 0;
  const notification = useNotification();

  const [currentStepIndex, setCurrentStepIndex] = useState(initialStep);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();

  const [skipLoader, setSkipLoader] = useState(false);

  const { t } = useTranslation();
  useEffect(() => {
    const pushDummyState = () => {
      history.pushState(null, '', window.location.href);
    };

    const handleBeforeBack = (event: PopStateEvent) => {
      event.preventDefault();
      pushDummyState(); // Re-add state so back stays blocked

      notification.warning({
        message: 'Action Not Allowed',
        description: 'You must complete all onboarding steps.',
      });
    };

    // Add an initial dummy state
    pushDummyState();

    // Listen for back button presses
    window.addEventListener('popstate', handleBeforeBack);

    return () => {
      window.removeEventListener('popstate', handleBeforeBack);
    };
  }, []);

  useEffect(() => {
    setCurrentStepIndex(initialStep);
  }, [initialStep]);

  // Watch form values to determine if Next button should be disabled
  const formValues = Form.useWatch([], form);

  const goToStep = (index: number) => {
    if (index >= 0 && index < steps.length) {
      setCurrentStepIndex(index);
      router.push(`/steps?step=${index}`);
    }
  };

  const steps = [
    {
      key: 'tell-us',
      title: t('steps.tell_us'),
      // subtitle: 'We’ll tailor your experience based on your input.',
      component: StepOne,
      skip: true,
    },
    {
      key: 'select-goals',
      title: t('steps.select_goals'),
      subtitle: t('steps.select_goals_subtitle'),
      component: StepTwo,
      skip: true,
    },
    {
      key: 'your-interests',
      title: t('steps.your_interests'),
      subtitle: t('steps.your_interests_subtitle'),
      subtitle1: t('steps.your_interests_subtitle1'),
      component: InterestsPage,
      skip: true,
    },
    {
      key: 'how-did-you-hear',
      title: t('steps.how_did_you_hear'),
      subtitle: t('steps.how_did_you_hear_subtitle'),
      component: StepFour,
      skip: true,
    },
  ];

  const CurrentStepComponent = steps[currentStepIndex].component;
  const { title, subtitle, skip, subtitle1 } = steps[currentStepIndex];

  // Function to check if current step has required data
  const isCurrentStepValid = () => {
    if (!formValues) {
      return false;
    }

    switch (currentStepIndex) {
      case 0:
        return formValues.gender && formValues.birthday && formValues.purpose;
      case 1:
        return formValues.goals && formValues.goals.length > 0;
      case 2:
        return formValues.interests && formValues.interests.length > 0;
      case 3:
        return formValues.platform;
      default:
        return false;
    }
  };

  const updateUserDetails = async (data: UserDetails) => {
    setLoading(true);
    try {
      let finalData: UserDetails = {};

      if (currentStepIndex === 0) {
        let formattedBirthDate = data.birthday;
        if (data.birthday) {
          const date = new Date(data.birthday);
          const day = String(date.getDate()).padStart(2, '0');
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const year = String(date.getFullYear());
          formattedBirthDate = `${day}/${month}/${year}`;
        }

        finalData = {
          type: 'screen_1_tell_about_us',
          gender: data.gender,
          birthDate: formattedBirthDate,
          purposeOfJoining: data.purpose,
        };
      }
      // Step 2: Select your goals
      else if (currentStepIndex === 1) {
        finalData = {
          type: 'screen_2_goals',
          goals: data.goals,
        };
      }
      // Step 3: Select your interests
      else if (currentStepIndex === 2) {
        finalData = {
          type: 'screen_3_interest',
          interests: data.interests,
        };
      }
      // Step 4: How did you hear about us
      else if (currentStepIndex === 3) {
        finalData = {
          type: 'screen_4_here_about_us',
          hearAboutUs: data.platform,
        };
      }

      const response = await getApiData<
        typeof finalData,
        { status: boolean; message: string; data?: User }
      >({
        url: `${siteConfig.apiUrl}${Endpoints.updateUserDetails}`,
        method: 'POST',
        data: finalData,
        customUrl: true,
      });

      if (response && response.status === true) {
        // Update user object in Redux state
        if (response.data) {
          dispatch(updateUser({ user: response.data }));
        }

        if (currentStepIndex === steps.length - 1) {
          router.push('/terms');
        } else {
          goToStep(currentStepIndex + 1);
        }
      } else {
        notification.error({
          message: 'Error',
          description: response?.message || 'Please try again later.',
        });
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const skipCurrentScreen = async () => {
    setSkipLoader(true);
    try {
      const screenTypes = [
        'screen_1_tell_about_us',
        'screen_2_goals',
        'screen_3_interest',
        'screen_4_here_about_us',
      ];

      const screenType =
        currentStepIndex < screenTypes.length
          ? screenTypes[currentStepIndex]
          : 'completed';

      const response = await getApiData<
        { screen: string },
        { status: boolean; message: string; data?: User }
      >({
        url: `${siteConfig.apiUrl}${Endpoints.skipScreen}`,
        method: 'POST',
        data: { screen: screenType },
        customUrl: true,
      });

      if (response && response.status === true) {
        if (response.data) {
          dispatch(updateUser({ user: response.data }));
        }

        if (currentStepIndex === steps.length - 1) {
          router.push('/terms');
        } else {
          goToStep(currentStepIndex + 1);
        }
        setSkipLoader(false);
      } else {
        setSkipLoader(false);
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      setSkipLoader(false);
    }
  };

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');
  const xxs = useMediaQuery('(max-width: 480px)');

  return (
    <>
      <div className={styles.root}>
        <ProgressStepBar
          currentStep={currentStepIndex}
          style={{ marginTop: sm ? '20px' : '40px' }}
        />
        {currentStepIndex === 0 && (
          <Button
            type='link'
            onClick={skipCurrentScreen}
            loading={skipLoader}
            style={{
              position: 'absolute',
              right: xs ? '10%' : sm ? '15%' : '20%',
              display: xxs ? 'none' : 'block',
              color: theme === 'dark' ? '#EDEFF1' : '#555555',
            }}
          >
            {t('steps.skip')}
          </Button>
        )}

        {currentStepIndex === 0 && (
          <Row
            justify='space-between'
            align='middle'
            style={{ justifyContent: 'center' }}
          >
            <Image
              src={images.aboutContent}
              alt='About'
              width={360}
              height={360}
            />
          </Row>
        )}
        <Row
          justify='center'
          align='middle'
          style={{ marginTop: 24, marginBottom: 0 }}
        >
          <Col
            style={{
              marginBottom: currentStepIndex === 0 ? '20px' : 0,
            }}
          >
            <Title
              level={2}
              style={{ margin: 10 }}
              className={styles.formTitle}
            >
              {title}
            </Title>
          </Col>
          {skip && currentStepIndex !== 0 && (
            <Button
              type='link'
              onClick={skipCurrentScreen}
              loading={skipLoader}
              style={{
                position: 'absolute',
                right: xs ? '10%' : sm ? '15%' : '20%',
                display: xxs ? 'none' : 'block',
                color: theme === 'dark' ? '#EDEFF1' : '#555555',
              }}
            >
              {t('steps.skip')}
            </Button>
          )}
        </Row>

        {subtitle && (
          <Row style={{ marginBottom: 24 }}>
            <Col
              span={24}
              style={{
                textAlign: 'center',
              }}
            >
              <Text
                type='secondary'
                className={styles.formSubtitle}
                style={{
                  color: colors[theme].subTitleText,
                }}
              >
                {subtitle}
              </Text>
              <Text
                type='secondary'
                className={styles.formSubtitle}
                style={{
                  marginTop: '-10px',
                  color: colors[theme].subTitleText,
                }}
              >
                {subtitle1}
              </Text>
            </Col>
          </Row>
        )}

        <div style={{ minHeight: 300 }}>
          {CurrentStepComponent && (
            <Form form={form} layout='vertical'>
              <CurrentStepComponent form={form} />
            </Form>
          )}
        </div>

        <Row justify='end' style={{ marginTop: 32 }}>
          <Col span={24}>
            <Button
              type='primary'
              loading={loading}
              disabled={!isCurrentStepValid()}
              onClick={async () => {
                await form.validateFields();
                const allValues = form.getFieldsValue(true);
                await updateUserDetails(allValues);
              }}
              style={{ width: '100%' }}
            >
              {t('steps.next')}
            </Button>
          </Col>
          {xxs && (
            <Col span={24} style={{ textAlign: 'center' }}>
              <Button
                type='link'
                onClick={skipCurrentScreen}
                loading={skipLoader}
                style={{
                  marginTop: '10px',
                  color: '#555555',
                }}
              >
                {t('steps.skip')}
              </Button>
            </Col>
          )}
        </Row>
      </div>
    </>
  );
}
