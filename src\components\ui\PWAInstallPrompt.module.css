/* PWA Install Prompt Styles */

.installPrompt {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  margin: 0 auto;
  animation: slideUp 0.3s ease-out;
}

.promptCard {
  background: var(--card-bg, #ffffff) !important;
  border: 1px solid var(--border-color, #e9ecef) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease;
}

:global(html[data-theme='dark']) .promptCard,
:global(body[data-theme='dark']) .promptCard {
  --card-bg: #1f1f1f;
  --border-color: #434343;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4) !important;
}

.promptCard :global(.ant-card-body) {
  padding: 16px !important;
}

.promptContent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.promptIcon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.promptText {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.promptDescription {
  font-size: 12px !important;
  line-height: 1.4 !important;
  margin: 0 !important;
}

.promptActions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.installButton {
  background: #007bff !important;
  border-color: #007bff !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  height: 32px !important;
  padding: 0 12px !important;
}

.installButton:hover {
  background: #0056b3 !important;
  border-color: #0056b3 !important;
}

.dismissButton {
  color: var(--text-secondary, #6c757d) !important;
  font-size: 12px !important;
  height: 28px !important;
  padding: 0 8px !important;
}

.dismissButton:hover {
  background: var(--border-color, #e9ecef) !important;
  color: var(--text-primary, #1a1a1a) !important;
}

/* Animations */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .installPrompt {
    bottom: 16px;
    left: 16px;
    right: 16px;
  }

  .promptContent {
    gap: 10px;
  }

  .promptIcon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .promptActions {
    gap: 6px;
  }

  .installButton {
    font-size: 11px !important;
    height: 30px !important;
    padding: 0 10px !important;
  }

  .dismissButton {
    font-size: 11px !important;
    height: 26px !important;
    padding: 0 6px !important;
  }
}

@media (max-width: 480px) {
  .installPrompt {
    bottom: 12px;
    left: 12px;
    right: 12px;
  }

  .promptCard :global(.ant-card-body) {
    padding: 12px !important;
  }

  .promptContent {
    gap: 8px;
  }

  .promptIcon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .promptText {
    gap: 2px;
  }

  .promptDescription {
    font-size: 11px !important;
  }
}

/* Landscape orientation on mobile */
@media (orientation: landscape) and (max-height: 500px) {
  .installPrompt {
    bottom: 8px;
    left: 8px;
    right: 8px;
    max-width: 350px;
  }

  .promptCard :global(.ant-card-body) {
    padding: 10px !important;
  }

  .promptContent {
    gap: 6px;
  }

  .promptIcon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .promptActions {
    gap: 4px;
  }

  .installButton {
    height: 28px !important;
    font-size: 10px !important;
  }

  .dismissButton {
    height: 24px !important;
    font-size: 10px !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .promptCard {
    border-width: 2px !important;
  }

  .installButton {
    border-width: 2px !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .installPrompt {
    animation: none;
  }

  .promptCard {
    transition: none;
  }
}
