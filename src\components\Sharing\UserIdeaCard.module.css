.card {
  box-shadow: 2.29px 5.72px 29.74px rgba(0, 0, 0, 0.09);
  width: 100%;
}

.propertyTitle {
  font-weight: 400;
  font-size: 16px;
  color: #000000;
  font-family: var(--font-poppins);
}

.date {
  font-weight: 300;
  color: #555555;
  font-family: var(--font-poppins);
  font-size: 14px;
  text-overflow: ellipsis;
  overflow: hidden;
}

.iconWrapper {
  width: 45px;
  height: 45px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: relative;
}

.icon {
  font-size: 25px;
  color: #fff;
}

:global(html[data-theme='dark']) .date {
  color: #012458;
}

.folder {
  font-weight: 500;
  font-family: var(--font-radio-canada);
  color: #000;
  font-size: 16px;
}

:global(html[data-theme='dark']) .folder {
  color: #012458;
}

.roleSelectDrp {
  min-width: 100px;
  height: 20px;
}

.roleSelectDrp :global(.ant-select-selector) {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.roleSelectDrp :global(.ant-select-selection-item) {
  color: #000000 !important;
  font-weight: 400;
  font-size: 16px;
  font-family: var(--font-poppins) !important;
}

.roleSelectDrp :global(.ant-select-arrow) {
  color: #000000 !important;
  font-size: 16px !important;
}

:global(html[data-theme='dark']) .card {
  background-color: #8edafe !important;
}

.statusTag {
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border: none !important;
  align-self: flex-start;
}

.statusTagDraft {
  background-color: #faad14 !important;
  color: white !important;
}

.statusTagSealed {
  background-color: #52c41a !important;
  color: white !important;
}

.statusTagInReview {
  background-color: #0059ff !important;
  color: white !important;
}

.statusTagDefault {
  background-color: #d9d9d9 !important;
  color: #666 !important;
}

@media (max-width: 600px) {
  .propertyTitle {
    font-size: 14px;
  }
  .folder {
    font-size: 14px;
  }

  .date {
    font-size: 14px;
  }
}
