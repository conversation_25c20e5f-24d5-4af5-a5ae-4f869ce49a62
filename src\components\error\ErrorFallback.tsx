/**
 * @fileoverview Error fallback component for handling application errors gracefully
 * @module components/error/ErrorFallback
 */

'use client';

import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch } from '@/store/hooks';
import { logout } from '@/store/slices/authSlice';
import React, { ErrorInfo, useState } from 'react';
import styles from './ErrorFallback.module.css';

/**
 * Props for the ErrorFallback component
 * @interface ErrorFallbackProps
 * @property {Error} [error] - The error object that was caught
 * @property {ErrorInfo} [errorInfo] - React error info containing component stack
 * @property {Function} onReset - Callback function to reset the error boundary
 */
interface ErrorFallbackProps {
  error?: Error;
  errorInfo?: ErrorInfo;
  onReset: () => void;
}

/**
 * Error fallback component that displays when the application encounters an unhandled error
 *
 * @component
 * @param {ErrorFallbackProps} props - The component props
 * @returns {JSX.Element} The rendered error fallback UI
 *
 * @example
 * // Used within an Error Boundary
 * <ErrorBoundary fallback={ErrorFallback}>
 *   <App />
 * </ErrorBoundary>
 *
 * @description
 * This component provides a user-friendly error interface with multiple recovery options:
 * - Try Again: Resets the error boundary and attempts to recover
 * - Reload Page: Performs a full page refresh
 * - Reset & Restart: Clears all application state and reloads
 *
 * The component also displays technical error details for developers in a collapsible section.
 * All text content is internationalized using the translation system.
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  onReset,
}) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const [isResetting, setIsResetting] = useState(false);

  const handleReload = (): void => {
    window.location.reload();
  };

  const handleClearReduxAndReload = async (): Promise<void> => {
    setIsResetting(true);
    try {
      dispatch(logout());
      localStorage.clear();
      sessionStorage.clear();
      await new Promise(resolve => setTimeout(resolve, 500));
      window.location.reload();
    } catch (err) {
      console.error('Error during reset:', err);
      window.location.reload();
    }
  };

  const handleGoHome = (): void => {
    onReset();
    window.location.href = '/';
  };

  return (
    <div className={styles.card}>
      <div className={styles.icon}>💥</div>
      <h1 className={styles.title}>{t('error.something_went_wrong')}</h1>
      <p className={styles.message}>{t('error.error_message')}</p>

      <div className={styles.buttonGroup}>
        <button onClick={handleGoHome} className={styles.primary}>
          {t('error.try_again')}
        </button>
        <button onClick={handleReload} className={styles.secondary}>
          {t('error.reload_page')}
        </button>
        <button
          onClick={handleClearReduxAndReload}
          className={styles.danger}
          disabled={isResetting}
        >
          {isResetting ? t('error.resetting') : t('error.reset_restart')}
        </button>
      </div>

      {(error || errorInfo) && (
        <details className={styles.details}>
          <summary>{t('error.technical_details')}</summary>
          {error && (
            <div>
              <strong>{t('error.error_label')}</strong> {error.message}
              <pre>{error.stack}</pre>
            </div>
          )}
          {errorInfo && (
            <div>
              <strong>{t('error.component_stack')}</strong>
              <pre>{errorInfo.componentStack}</pre>
            </div>
          )}
        </details>
      )}
    </div>
  );
};

export default ErrorFallback;
