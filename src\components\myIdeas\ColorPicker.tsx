'use client';

import { EditOutlined } from '@ant-design/icons';
import { ColorPicker as AntColorPicker, Button, Space } from 'antd';
import React from 'react';

import { useTranslation } from '@/hooks/useTranslation';
import { AggregationColor } from 'antd/es/color-picker/color';
import { colors } from '../../theme/antdTheme';
import styles from './ColorPicker.module.css';

/**
 * Props for ColorPicker component
 *
 * @interface ColorPickerProps
 * @property {string} value - Currently selected color value
 * @property {function} onChange - Callback when color is changed
 * @property {string[]} defaultColors - Array of default color options
 */
interface ColorPickerProps {
  value?: string;
  onChange: (color: string) => void;
  defaultColors?: string[];
}

/**
 * Default color palette for folder selection
 */
const DEFAULT_COLORS = [
  colors.secondary,
  '#ff6b35', // Orange
  '#dc2626', // Red
  '#10b981', // Green
  '#eab308', // Yellow
  '#ec4899', // Pink
];

/**
 * ColorPicker component for selecting folder colors
 *
 * @component ColorPicker
 * @param {ColorPickerProps} props - Component props
 * @returns {JSX.Element} Rendered color picker component
 *
 * @example
 * const [selectedColor, setSelectedColor] = useState('#ff6b35');
 *
 * <ColorPicker
 *   value={selectedColor}
 *   onChange={setSelectedColor}
 *   defaultColors={['#ff6b35', '#dc2626', '#10b981']}
 * />
 *
 * @description
 * This component provides a color selection interface with:
 * - Predefined color options that match the design
 * - Custom color picker for advanced selection
 * - Visual feedback for selected color
 * - Hover effects for better UX
 */
const ColorPicker: React.FC<ColorPickerProps> = ({
  value,
  onChange,
  defaultColors = DEFAULT_COLORS,
}) => {
  const { t } = useTranslation();
  // const [showCustomPicker, setShowCustomPicker] = useState(false);

  const handleColorSelect = (color: string) => {
    onChange(color);
  };

  const handleCustomColorChange = (color: AggregationColor) => {
    const hexColor = color.toHexString();
    onChange(hexColor);
  };

  const isCustomColor = value && !defaultColors.includes(value);

  return (
    <div className={styles.colorContainer}>
      <Space size={12} wrap>
        {defaultColors.map(color => (
          <div
            key={color}
            className={`${styles.colorOption} ${value === color ? styles.colorOptionSelected : ''}`}
            style={{ backgroundColor: color }}
            onClick={() => handleColorSelect(color)}
            title={t('createFolder.colorPicker.selectColor')}
          />
        ))}

        <div className={styles.colorPickerWrapper}>
          <AntColorPicker
            value={value}
            onChange={handleCustomColorChange}
            showText={false}
            trigger='click'
            placement='bottomLeft'
          >
            <Button
              title={t('createFolder.colorPicker.customColor')}
              icon={<EditOutlined />}
            />
          </AntColorPicker>
        </div>
      </Space>
    </div>
  );
};

export default ColorPicker;
