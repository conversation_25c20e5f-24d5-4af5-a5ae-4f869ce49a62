import type { ThemeConfig } from 'antd';
import { theme } from 'antd';

// Color palette
export const colors = {
  primary: '#012458',
  primaryHover: 'rgb(77,102,138)',
  primaryActive: '#012458',
  secondary: '#8EDAFE',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#012458',
  buttonHover: '#6ad0fe', // New hover color for buttons
  cardBackground: '#f5f5f5',
  black: '#000000',
  hearText: '#EDEFF1',
  white: '#FFFFFF',
  green: '#00FF00',

  // Gradient colors
  gradientStart: '#012458',
  gradientEnd: '#764ba2',

  // Light theme colors
  light: {
    background: '#ffffff',
    mainBackground: '#ffffff',
    backgroundSecondary: '#f5f7fa',
    backgroundTertiary: '#fafbfc',
    text: '#333333',
    textSecondary: '#666666',
    textTertiary: '#999999',
    border: '#e1e5e9',
    borderSecondary: '#f0f0f0',
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadow1: 'rgba(0, 0, 0, 0.2)',
    shadowHover: 'rgba(0, 0, 0, 0.15)',
    colorBgContainer: '#EDEFF1',
    titleText: '#000000',
    subTitleText: '#555555',
    stepActive: '#012458',
    stepInActive: '#EFEFEF',
    cardText: '#EDEFF1',
    drawerBackground: '#9bdfff',
  },

  // Dark theme colors
  dark: {
    background: '#141414',
    mainBackground: '#012458',
    backgroundSecondary: '#1f1f1f',
    backgroundTertiary: '#262626',
    text: '#ffffff',
    textSecondary: '#d9d9d9',
    textTertiary: '#8c8c8c',
    border: '#434343',
    borderSecondary: '#303030',
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowHover: 'rgba(0, 0, 0, 0.4)',
    inputBg: '#06164A',
    titleText: '#ffffff',
    subTitleText: '#EDEFF1',
    stepActive: '#8EDAFE',
    stepInActive: '#06164A',
    cardText: '#EDEFF1',
    drawerBackground: '#012458',
  },
  // CSS module variables
  buttonBg: '#8edafe',
  buttonPrimary: '#012458',
};

// Common component styles
const commonComponents = {
  Button: {
    borderRadius: 10,
    controlHeight: 45,
    fontWeight: 500,
    boxShadow: 'none',
    primaryShadow: 'none',
    colorPrimary: colors.primary,
    colorPrimaryActive: colors.primary,
    colorPrimaryBg: colors.primary,
    colorPrimaryHover: 'rgb(77,102,138)',
    colorPrimaryTextActive: 'rgb(255,255,255)',
    colorPrimaryTextHover: 'rgb(77,102,138)',
    colorInfo: colors.primary,
    colorInfoActive: colors.primary,
    colorInfoHover: colors.primary,
    colorLink: colors.primary,
    colorLinkHover: 'rgb(77,102,138)',
    colorLinkActive: colors.primary,
    colorPrimaryText: colors.primary,

    colorBgContainer: '#EDEFF1',
    defaultColor: '#555555',
    defaultShadow: 'none',
    defaultHoverColor: '#555555',
    defaultBorderColor: 'none',

    // defaultHoverColor: colors.primary,
    // groupBorderColor: colors.primary,
    // defaultActiveBorderColor: colors.primary,
    // defaultActiveBg: colors.primary,
    // defaultHoverBorderColor: colors.primary,
  },
  Input: {
    borderRadius: 10,
    controlHeight: 45,
    paddingInline: 16,
    fontSize: 16,
    activeBorderColor: colors.primary,
    addonBg: 'rgb(245,245,245)',
    colorBorder: 'rgb(245,245,245)',
    hoverBorderColor: colors.primary,
    colorBgContainer: '#F5F5F5',
    colorText: '#000000',
    colorTextPlaceholder: '#bfbfbf',
    boxShadow: 'none',
    boxShadowHover: 'none',
    boxShadowActive: 'none',
  },
  Card: {
    borderRadius: 16,
    paddingLG: 24,
  },
  Form: {
    itemMarginBottom: 24,
    verticalLabelPadding: '0 0 8px',
  },
  Typography: {
    titleMarginBottom: '0.5em',
    titleMarginTop: '1.2em',
    colorLink: colors.primary,
    colorLinkActive: colors.primary,
  },
  Menu: {
    borderRadius: 8,
    itemHeight: 40,
    itemMarginInline: 4,
    itemBorderRadius: 6,
  },
  Divider: {
    marginLG: 24,
  },
  Space: {
    size: 16,
  },
  Radio: {
    buttonSolidCheckedBg: colors.primary,
    buttonSolidCheckedActiveBg: colors.primary,
    buttonSolidCheckedHoverBg: colors.primary,
    colorPrimary: colors.primary,
    colorPrimaryActive: colors.primary,
    colorPrimaryHover: colors.primary,
  },
  Table: {
    colorLink: colors.primary,
    colorLinkHover: colors.primary,
    colorPrimaryBorder: colors.primary,
  },
  Select: {
    optionSelectedColor: 'rgb(255,255,255)',
    optionSelectedBg: colors.primary,
    selectorBg: 'rgb(245,245,245)',
    colorBorder: 'rgb(245,245,245)',
    controlHeight: 50,
  },

  DatePicker: {
    colorBgContainer: 'rgb(245,245,245)',
    colorBorder: 'rgb(245,245,245)',
    controlHeight: 50,
  },
  Tooltip: {
    colorTextLightSolid: colors.light.text,
  },
  Spin: {
    colorPrimary: colors.primary,
  },
  Alert: {
    colorInfoBg: '#ccd3de',
  },
  Switch: {
    algorithm: true,
    colorPrimary: colors.primary,
  },
};

// Light theme configuration
export const lightTheme: ThemeConfig = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: colors.primary,
    colorSuccess: colors.success,
    colorWarning: colors.warning,
    colorError: colors.error,
    colorInfo: colors.info,
    colorBgBase: colors.light.background,
    colorBgContainer: colors.light.background,
    colorBgElevated: colors.light.background,
    colorBgLayout: colors.light.backgroundSecondary,
    colorBgSpotlight: colors.light.backgroundTertiary,
    colorText: colors.light.text,
    colorTextSecondary: colors.light.textSecondary,
    colorTextTertiary: colors.light.textTertiary,
    colorTextQuaternary: colors.light.textTertiary,
    colorBorder: colors.light.border,
    colorBorderSecondary: colors.light.borderSecondary,
    fontFamily: 'inherit',
    fontSize: 14,
    fontSizeHeading1: 38,
    fontSizeHeading2: 30,
    fontSizeHeading3: 24,
    fontSizeHeading4: 20,
    fontSizeHeading5: 16,
    borderRadius: 8,
    borderRadiusLG: 12,
    borderRadiusXS: 4,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
    boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.3)',
    boxShadowTertiary: '0 8px 24px rgba(0, 0, 0, 0.4)',
    motionDurationSlow: '0.3s',
    motionDurationMid: '0.2s',
    motionDurationFast: '0.1s',
  },
  components: commonComponents,
};

// Dark theme configuration
export const darkTheme: ThemeConfig = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: colors.primary,
    colorSuccess: colors.success,
    colorWarning: colors.warning,
    colorError: colors.error,
    colorInfo: colors.info,

    // Background colors
    colorBgBase: colors.dark.background,
    colorBgContainer: colors.dark.backgroundSecondary,
    colorBgElevated: colors.dark.backgroundTertiary,
    colorBgLayout: colors.dark.background,
    colorBgSpotlight: colors.dark.backgroundTertiary,

    // Text colors
    colorText: colors.dark.text,
    colorTextSecondary: colors.dark.textSecondary,
    colorTextTertiary: colors.dark.textTertiary,
    colorTextQuaternary: colors.dark.textTertiary,

    // Border colors
    colorBorder: colors.dark.border,
    colorBorderSecondary: colors.dark.borderSecondary,

    // Font settings
    fontFamily: 'inherit',
    fontSize: 14,
    fontSizeHeading1: 38,
    fontSizeHeading2: 30,
    fontSizeHeading3: 24,
    fontSizeHeading4: 20,
    fontSizeHeading5: 16,

    // Border radius
    borderRadius: 8,
    borderRadiusLG: 12,
    borderRadiusXS: 4,

    // Shadows
    boxShadow: `0 2px 8px ${colors.dark.shadow}`,
    boxShadowSecondary: `0 4px 12px ${colors.dark.shadow}`,
    boxShadowTertiary: `0 8px 24px ${colors.dark.shadowHover}`,

    // Motion
    motionDurationSlow: '0.3s',
    motionDurationMid: '0.2s',
    motionDurationFast: '0.1s',
  },
  components: {
    ...commonComponents,
    Button: {
      ...commonComponents.Button,
      colorPrimary: colors.buttonBg,
      colorPrimaryActive: colors.buttonBg,
      colorPrimaryBg: colors.buttonBg,
      colorPrimaryHover: '#6ad0fe',
      colorPrimaryText: colors.buttonPrimary,
      colorPrimaryTextActive: colors.buttonPrimary,
      colorPrimaryTextHover: colors.buttonPrimary,
      colorText: colors.buttonPrimary,
      colorTextBase: colors.buttonPrimary,
      colorTextLightSolid: colors.buttonPrimary,
      primaryColor: colors.buttonPrimary,
      // defaultBorderColor: colors.buttonBg,
      // defaultColor: colors.buttonPrimary,
      // defaultBg: colors.buttonBg,
      // defaultHoverBg: 'rgba(142, 218, 254, 0.8)',
      // defaultHoverColor: colors.buttonPrimary,
      // defaultHoverBorderColor: 'rgba(142, 218, 254, 0.9)',

      colorBgContainer: '#39547d',
      defaultColor: colors.dark.text,
      defaultShadow: 'none',
      defaultHoverColor: colors.dark.text,
      defaultBorderColor: 'none',
      defaultHoverBorderColor: colors.dark.text,
    },
    Card: {
      ...commonComponents.Card,
      colorBgContainer: colors.dark.backgroundSecondary,
    },
    Input: {
      ...commonComponents.Input,
      colorBgContainer: colors.dark.inputBg,
      activeBorderColor: colors.light.background,
      addonBg: 'rgb(245,245,245)',
      colorBorder: colors.dark.inputBg,
      hoverBorderColor: colors.light.background,
      colorText: colors.dark.text,
      colorTextPlaceholder: colors.dark.text,
      boxShadow: 'none',
    },
    Select: {
      optionSelectedColor: colors.dark.inputBg,
      optionSelectedBg: colors.dark.text,
      colorBgElevated: colors.dark.inputBg,
      selectorBg: colors.dark.inputBg,
      colorBorder: colors.dark.inputBg,
      colorBgBase: colors.dark.inputBg,
      controlHeight: 50,
      colorText: colors.dark.text,
      colorTextPlaceholder: colors.dark.text,
    },
    DatePicker: {
      colorBgContainer: colors.dark.inputBg,
      colorBorder: colors.dark.inputBg,
      controlHeight: 50,
      colorText: colors.dark.text,
      colorTextLightSolid: colors.dark.inputBg,
      colorBgElevated: colors.dark.inputBg,
      colorPrimary: colors.dark.text,
    },
    Tooltip: {
      colorTextLightSolid: colors.dark.text,
    },
    Spin: {
      colorPrimary: colors.white,
    },
    Alert: {
      colorInfoBg: '#39547d',
    },
    Switch: {
      algorithm: true,
      colorPrimary: colors.secondary,
    },
    Table: {
      colorPrimaryBorder: colors.primary,
      colorBgContainer: 'transparent',
      bodySortBg: 'transparent',
      rowHoverBg: colors.dark.backgroundSecondary,
    },
  },
};

// Styled-components theme for consistency
export const styledTheme = {
  colors,
  light: colors.light,
  dark: colors.dark,
  gradients: {
    primary: `linear-gradient(135deg, ${colors.gradientStart} 0%, ${colors.gradientEnd} 100%)`,
    primaryHover: `linear-gradient(135deg, ${colors.primaryHover} 0%, #6a4190 100%)`,
    background: `linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)`,
    backgroundDark: `linear-gradient(135deg, #2c3e50 0%, #34495e 100%)`,
  },
  shadows: {
    light: {
      small: `0 2px 8px ${colors.light.shadow}`,
      medium: `0 4px 12px ${colors.light.shadow}`,
      large: `0 8px 24px ${colors.light.shadowHover}`,
    },
    dark: {
      small: `0 2px 8px ${colors.dark.shadow}`,
      medium: `0 4px 12px ${colors.dark.shadow}`,
      large: `0 8px 24px ${colors.dark.shadowHover}`,
    },
  },
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
    xlarge: '16px',
  },
  transitions: {
    fast: '0.1s ease',
    medium: '0.2s ease',
    slow: '0.3s ease',
  },
};
