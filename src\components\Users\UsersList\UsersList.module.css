.container {
  width: 100%;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
}

.tab {
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer !important;
}

.activeTab {
  padding: 10px 20px;
  background-color: #012458;
  border-radius: 12px;
  cursor: pointer !important;
}

.text {
  color: #555555;
  font-family: var(--font-poppins);
  font-weight: 300;
  font-size: 14px;
}

:global(html[data-theme='dark']) .activeTab {
  background-color: #8edafe !important;
}
:global(html[data-theme='dark']) .activeText {
  color: #012458 !important;
}

:global(html[data-theme='dark']) .text {
  color: #edeff1 !important;
}

.activeText {
  color: #ffff;
  font-family: var(--font-poppins);
  font-weight: 300;
  font-size: 14px;
}

.tabContainer {
  display: flex;
  gap: 10px;
}

.statusTag {
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border: none !important;
}

.statusTagDraft {
  background-color: #faad14 !important;
  color: white !important;
}

.statusTagSealed {
  background-color: #52c41a !important;
  color: white !important;
}

.statusTagInReview {
  background-color: #0059ff !important;
  color: white !important;
}

.statusTagDefault {
  background-color: #d9d9d9 !important;
  color: #666 !important;
}
.loadMoreWrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 26px;
  width: 100%;
}
@media (max-width: 900) {
  .pageTitle {
    font-size: 28px !important;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .pageTitle {
    font-size: 24px !important;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 22px !important;
  }
}

.foldersSpinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dragger .ant-upload-drag .ant-upload {
  background: 'transparent';
  padding: 0px;
  border-width: 0px;
}
