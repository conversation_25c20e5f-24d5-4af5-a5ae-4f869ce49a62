'use client';

import { Button, Input, InputRef, Modal, Typography } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { Endpoints } from '../../config/endpoints';
import siteConfig from '../../config/site.config';
import { getApiData } from '../../helpers/ApiHelper';
import { useTranslation } from '../../hooks/useTranslation';
import { useAppSelector } from '../../store/hooks';
import { useNotification } from '../Notification/NotificationContext';
import styles from './Modal.module.css';

interface VerificationModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface response {
  status?: boolean;
}

interface VerifyOtpResponse {
  status: boolean;
  message: string;
  data?: {
    user?: {
      id: string;
      email: string;
      is2FAEnabled?: boolean;
    };
  };
  token?: string;
  otp?: number;
}

const { Title, Text } = Typography;

const VerificationModal: React.FC<VerificationModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [timeLeft, setTimeLeft] = useState(180); // 3 minutes in seconds
  const [isExpired, setIsExpired] = useState(false);
  const [isResending, setIsResending] = useState(false);

  const notification = useNotification();
  const inputRefs = useRef<(InputRef | null)[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const { t } = useTranslation();
  const { user } = useAppSelector(s => s.auth);

  const [loading, setLoading] = useState(false);

  // Format time in seconds
  const formatTime = (seconds: number) => {
    return `${seconds}s`;
  };

  // Start countdown timer
  const startTimer = useCallback(() => {
    setTimeLeft(180); // Reset to 3 minutes
    setIsExpired(false);

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimeLeft(prevTime => {
        if (prevTime <= 1) {
          setIsExpired(true);
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [notification]);

  // Clear timer
  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Reset modal state
  const resetModalState = useCallback(() => {
    setOtp(['', '', '', '', '', '']);
    setTimeLeft(180);
    setIsExpired(false);
    setLoading(false);
    setIsResending(false);
    clearTimer();
  }, [clearTimer]);

  // Start timer when modal becomes visible
  useEffect(() => {
    if (visible) {
      startTimer();
    } else {
      resetModalState();
    }

    if (visible) {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    }

    return () => {
      clearTimer();
    };
  }, [visible, startTimer, resetModalState, clearTimer]);

  // Handle OTP input changes
  const handleOTPChange = useCallback(
    (index: number, value: string) => {
      if (value.length > 1 || isExpired) {
        return;
      }

      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }

      if (value) {
        const isComplete = newOtp.every(digit => digit !== '');
        if (isComplete) {
          handleVerifyWithOtp(newOtp.join(''));
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [otp, isExpired]
  );

  // Handle key down events
  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Resend OTP
  const resendOTP = async () => {
    if (isResending) {
      return;
    }

    setIsResending(true);
    try {
      const response = await getApiData<
        { email: string; type: string },
        VerifyOtpResponse
      >({
        url: `${siteConfig.apiUrl}${Endpoints.resendOtp}`,
        method: 'POST',
        data: {
          email: user?.email || '',
          type: 'verify_2fa_otp',
        },
        customUrl: true,
      });

      if (response && 'status' in response && response.status) {
        notification.success({
          message: 'OTP Resent',
          description:
            'otp' in response && response.otp !== undefined
              ? `Your new OTP is ${response.otp}`
              : 'A new verification code has been sent to your email.',
        });

        // Reset OTP inputs and restart timer
        setOtp(['', '', '', '', '', '']);
        startTimer();

        // Focus on first input
        setTimeout(() => {
          inputRefs.current[0]?.focus();
        }, 100);
      } else {
        notification.error({
          message: 'Resend Failed',
          description:
            response?.message || 'Failed to resend code. Please try again.',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Resend Failed',
        description:
          error?.message || 'Failed to resend code. Please try again.',
      });
    } finally {
      setIsResending(false);
    }
  };

  const handleVerifyWithOtp = async (otpCode?: string) => {
    const finalOtpCode = otpCode || otp.join('');

    if (finalOtpCode.length !== 6) {
      notification.error({
        message: 'Verification failed.',
        description: 'Please enter the complete 6-digit code',
      });
      return;
    }

    setLoading(true);

    try {
      const response = await getApiData<
        { email: string; OTP: string; type: string },
        VerifyOtpResponse
      >({
        url: `${siteConfig.apiUrl}${Endpoints.verifyOtp}`,
        method: 'POST',
        data: {
          email: user?.email || '',
          OTP: finalOtpCode,
          type: 'verify_2fa_otp',
        },
        customUrl: true,
      });

      if (response && response.status === true) {
        clearTimer();
        onSuccess();
        onClose();
        resetModalState();
      } else {
        notification.error({
          message: 'Verification failed.',
          description:
            response?.message || 'Invalid verification code. Please try again.',
        });
        setOtp(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
      }
    } catch {
      notification.error({
        message: 'Verification failed.',
        description: 'Verification failed. Please try again.',
      });
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } finally {
      setLoading(false);
    }
  };

  const handleModalClose = () => {
    onClose();
    resetModalState();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleModalClose}
      footer={null}
      centered
      width={600}
      maskClosable={false}
      closable={true}
      keyboard={false}
    >
      <div className={styles.container}>
        <Title level={3} className={styles.title}>
          {t('emailVerification.title')}
        </Title>

        <Text className={styles.subTitle}>
          {t('emailVerification.subtitle')}
        </Text>

        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '8px',
            marginBottom: '24px',
          }}
        >
          {otp.map((digit, index) => (
            <Input
              key={index}
              ref={el => {
                inputRefs.current[index] = el;
              }}
              value={digit}
              onChange={e => {
                const value = e.target.value;
                if (/^[0-9]*$/.test(value)) {
                  // Only allow digits ( no special characters)
                  handleOTPChange(index, value);
                }
              }}
              onKeyDown={e => handleKeyDown(index, e)}
              maxLength={1}
              disabled={isExpired}
              style={{
                width: isMobile ? '45px' : '50px',
                height: isMobile ? '45px' : '50px',
                textAlign: 'center',
                fontSize: '18px',
                opacity: isExpired ? 0.5 : 1,
              }}
            />
          ))}
        </div>

        {/* Timer Display / Resend Text */}
        <div
          style={{
            textAlign: 'center',
            marginBottom: '16px',
            padding: '8px',
          }}
        >
          {isExpired ? (
            <Text
              style={{
                fontSize: '16px',
                cursor: 'pointer',
                textDecoration: 'underline',
              }}
              onClick={resendOTP}
            >
              {isResending ? 'Sending...' : 'Resend OTP'}
            </Text>
          ) : (
            <Text
              style={{
                fontSize: '16px',
              }}
            >
              Resend in {formatTime(timeLeft)}
            </Text>
          )}
        </div>

        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            type='primary'
            onClick={() => handleVerifyWithOtp()}
            loading={loading}
            disabled={isExpired}
            style={{
              width: '120px',
            }}
          >
            {t('emailVerification.verify')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default VerificationModal;
