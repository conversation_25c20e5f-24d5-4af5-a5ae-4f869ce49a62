.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
}

.content {
  border-radius: 10px;
  min-width: 650px;
}

.iconSection {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 20px;
}

.textSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 40px;
}

.divider {
  margin: 0px;
  border-color: #666666;
  border-width: 0.5;
}
.propSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.propTitle {
  font-size: 16px !important;
  font-family: var(--font-poppins) !important;
  color: #000000 !important;
  font-weight: 400;
}

.propText {
  font-size: 16px !important;
  font-family: var(--font-poppins) !important;
  color: #000000 !important;
  font-weight: 400;
}

.buttonSection {
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 20px;
  margin-top: 40px;
  width: 650px;
}

:global(html[data-theme='dark']) .propTitle {
  color: #fff !important;
}

:global(html[data-theme='dark']) .propText {
  color: #fff !important;
}

@media (max-width: 1200) {
  .content {
    min-width: 450px;
  }

  .buttonSection {
    width: 450px;
  }
}

@media (max-width: 900) {
  .pageTitle {
    font-size: 28px !important;
  }

  .content {
    min-width: 400px;
  }

  .buttonSection {
    width: 400px;
  }
}

@media (max-width: 768px) {
  .pageTitle {
    font-size: 28px !important;
  }

  .content {
    min-width: 80vw;
  }

  .buttonSection {
    width: 80vw;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
  }

  .iconSection {
    margin-top: 0px;
  }
}

@media (max-width: 600px) {
  .pageTitle {
    font-size: 24px !important;
  }

  .successText {
    font-size: 22px !important;
  }

  .propTitle {
    font-size: 14px !important;
  }

  .propText {
    font-size: 14px !important;
  }
  .textSection {
    gap: 12px;
  }

  .content {
    padding: 20px 12px;
  }

  .content {
    min-width: 100%;
  }

  .buttonSection {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 22px !important;
  }

  .successText {
    font-size: 20px !important;
  }
}
