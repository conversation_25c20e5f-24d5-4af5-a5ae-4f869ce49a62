/* eslint-disable @typescript-eslint/no-explicit-any */
import { Endpoints } from '@/config/endpoints';
import siteConfig from '@/config/site.config';
import { getApiData } from '@/helpers/ApiHelper';
import {
  FirebaseApp,
  FirebaseOptions,
  getApp,
  getApps,
  initializeApp,
} from 'firebase/app';
import {
  deleteToken,
  getMessaging,
  getToken,
  isSupported,
  Messaging,
} from 'firebase/messaging';

// Types
export type NotificationPermission = 'default' | 'granted' | 'denied';

// Static Firebase configuration (no env dependencies)
const firebaseConfig: FirebaseOptions = {
  apiKey: 'AIzaSyDnQuITJw2uVkofT4O95omZviFTriflLlU',
  authDomain: 'swiss-trust-layer.firebaseapp.com',
  projectId: 'swiss-trust-layer',
  storageBucket: 'swiss-trust-layer.firebasestorage.app',
  messagingSenderId: '815385848837',
  appId: '1:815385848837:web:ba65ccff720f8e0950c06c',
  measurementId: 'G-P9PT3507MY',
};

const vapidKey =
  'BAGsS75cD7Y3iPCEc-6eSubZKrcpeERSoWbCPa9TuX8-7dPj3Qqj6ZNj937klmojfOA-5Mdz4FeAwQaQjTo-nd8';

interface Response {
  status: boolean;
  message: string;
}

// Global variables
let app: FirebaseApp | null = null;
let messaging: Messaging | null = null;
let isFirebaseReady = false;
let isMessagingReady = false;
let initializationPromise: Promise<boolean> | null = null;

// Debug logging function
const debugLog = (message: string, data?: any) => {
  console.log(`🔥 Firebase Debug: ${message}`, data || '');
};

// Initialize Firebase app
const initializeFirebase = async (): Promise<boolean> => {
  if (isFirebaseReady && app) {
    debugLog('Firebase already initialized');
    return true;
  }

  try {
    debugLog('Initializing Firebase app...');

    if (getApps().length === 0) {
      app = initializeApp(firebaseConfig);
      debugLog('Firebase app created');
    } else {
      app = getApp();
      debugLog('Firebase app retrieved');
    }

    isFirebaseReady = true;
    debugLog('✅ Firebase app initialized successfully');
    return true;
  } catch (error) {
    debugLog('❌ Error initializing Firebase app', error);
    isFirebaseReady = false;
    return false;
  }
};

// Initialize messaging
const initializeMessaging = async (): Promise<boolean> => {
  if (typeof window === 'undefined') {
    debugLog('Server-side environment, skipping messaging');
    return false;
  }

  if (isMessagingReady && messaging) {
    debugLog('Messaging already initialized');
    return true;
  }

  if (!app) {
    debugLog('Firebase app not ready, initializing...');
    const appReady = await initializeFirebase();
    if (!appReady) {
      debugLog('❌ Failed to initialize Firebase app');
      return false;
    }
  }

  try {
    debugLog('Checking messaging support...');

    // Check if messaging is supported
    const messagingSupported = await isSupported();
    if (!messagingSupported) {
      debugLog('❌ Firebase messaging not supported');
      return false;
    }

    debugLog('Checking service worker support...');
    if (!('serviceWorker' in navigator)) {
      debugLog('❌ Service Worker not supported');
      return false;
    }

    debugLog('Registering service worker...');

    // Simple service worker registration
    // try {
    //   // Register the service worker
    //   const swRegistration = await navigator.serviceWorker.register(
    //     '/firebase-messaging-sw.js'
    //   );
    //   debugLog('✅ Service worker registered successfully', swRegistration);

    //   // Wait for service worker to be ready
    //   await navigator.serviceWorker.ready;
    //   debugLog('✅ Service worker is ready');
    // } catch (swError) {
    //   debugLog('❌ Service worker registration failed', swError);
    //   return false;
    // }

    debugLog('Initializing Firebase messaging...');
    messaging = getMessaging(app!);
    isMessagingReady = true;

    debugLog('✅ Firebase messaging initialized successfully');
    return true;
  } catch (error) {
    debugLog('❌ Error initializing messaging', error);
    isMessagingReady = false;
    return false;
  }
};

// Save token to backend
export const saveToken = async (token: string): Promise<boolean> => {
  if (!token) {
    debugLog('❌ No token provided to save');
    return false;
  }

  try {
    debugLog('💾 Saving FCM token to backend...');

    const res = await getApiData<{ fcm_token: string }, Response>({
      url: `${siteConfig.apiUrl}${Endpoints.saveFcmToken}`,
      method: 'POST',
      data: { fcm_token: token },
      customUrl: true,
    });

    if (res?.status) {
      localStorage.setItem('fcm_token', token);
      debugLog('✅ FCM token saved successfully');
      return true;
    } else {
      debugLog('⚠️ Failed to save FCM token', res?.message);
      return false;
    }
  } catch (error) {
    debugLog('❌ Error saving token', error);
    return false;
  }
};

// Request notification permission
export const reqNotificationPermission =
  async (): Promise<NotificationPermission> => {
    try {
      debugLog('Requesting notification permission...');
      const permission = await Notification.requestPermission();
      debugLog('Permission result:', permission);
      return permission as NotificationPermission;
    } catch (error) {
      debugLog('❌ Error requesting permission', error);
      return 'denied';
    }
  };

// Generate FCM token
export const generateToken = async (
  existingToken?: string
): Promise<NotificationPermission | null> => {
  debugLog('🔄 Starting token generation process...');

  // Check browser environment
  if (typeof window === 'undefined') {
    debugLog('❌ Server-side environment detected');
    return null;
  }

  // Check notification support
  if (!('Notification' in window)) {
    debugLog('❌ Notifications not supported');
    return null;
  }

  try {
    // Initialize Firebase and messaging if needed
    if (!isFirebaseReady) {
      debugLog('Initializing Firebase...');
      const firebaseInit = await initializeFirebase();
      if (!firebaseInit) {
        debugLog('❌ Firebase initialization failed');
        return null;
      }
    }

    if (!isMessagingReady) {
      debugLog('Initializing messaging...');
      const messagingInit = await initializeMessaging();
      if (!messagingInit) {
        debugLog('❌ Messaging initialization failed');
        return null;
      }
    }

    // Check current permission
    let permission = Notification.permission;
    debugLog('Current permission:', permission);

    // Request permission if needed
    if (permission === 'default') {
      permission = await reqNotificationPermission();
    }

    if (permission === 'granted') {
      debugLog('✅ Permission granted, generating token...');

      if (!messaging) {
        debugLog('❌ Messaging not available');
        return permission;
      }

      debugLog('Generating FCM token...');

      try {
        // Simple token generation with just VAPID key
        const fcmToken = await getToken(messaging, {
          vapidKey,
          serviceWorkerRegistration:
            await navigator.serviceWorker.getRegistration(
              '/firebase-cloud-messaging-push-scope'
            ),
        });
        debugLog(
          'FCM token result:',
          fcmToken ? 'Generated successfully' : 'Failed to generate'
        );

        if (fcmToken) {
          debugLog('✅ FCM Token generated successfully');
          await saveToken(fcmToken);
          return permission;
        } else {
          debugLog('⚠️ No FCM token generated');
          return permission;
        }
      } catch (tokenError) {
        debugLog('❌ Error generating FCM token:', tokenError);
        return permission;
      }
    } else {
      debugLog('❌ Permission not granted:', permission);
      return permission;
    }
  } catch (error) {
    debugLog('❌ Error in token generation', error);
    return null;
  }
};

export const deleteFirebaseToken = async () => {
  try {
    if (!messaging) {
      debugLog('❌ Messaging not available');
      return;
    }
    await deleteToken(messaging);
    localStorage.removeItem('fcm_token');
    debugLog('✅ FCM Token deleted successfully');
  } catch (error) {
    debugLog('❌ Error deleting FCM token:', error);
  }
};

// Initialize on client side
if (typeof window !== 'undefined') {
  debugLog('Client-side detected, starting initialization...');
  initializationPromise = initializeFirebase().then(async firebaseReady => {
    if (firebaseReady) {
      await initializeMessaging();
    }
    return firebaseReady;
  });
}

// Export functions and variables
export { messaging };
export const getFirebaseApp = () => app;
export const getFirebaseMessaging = () => messaging;
export const isFirebaseInitialized = () => isFirebaseReady;
export const isMessagingInitialized = () => isMessagingReady;
export const getInitializationPromise = () => initializationPromise;

export const getFirebase = async () => {
  console.log('🔍 Running Firebase notification diagnostics...');

  // Test Firebase initialization
  const firebaseInit = isFirebaseInitialized();
  console.log('🔥 Firebase Debug: Firebase initialized:', firebaseInit);

  // Check Firebase app instance
  const firebaseApp = getFirebaseApp();
  console.log('🔥 Firebase Debug: Firebase app instance:', firebaseApp);

  // Check initialization promise
  const initPromise = getInitializationPromise();
  console.log('🔥 Firebase Debug: Initialization promise:', initPromise);

  if (initPromise) {
    try {
      await initPromise;
      console.log('🔥 Firebase Debug: Initialization promise resolved');
    } catch (error) {
      console.error(
        '🔥 Firebase Debug: Initialization promise rejected:',
        error
      );
    }
  }

  // Test messaging initialization
  const messagingInit = isMessagingInitialized();
  console.log('🔥 Firebase Debug: Messaging initialized:', messagingInit);

  // Check messaging instance
  const messagingInstance = getFirebaseMessaging();
  console.log('🔥 Firebase Debug: Messaging instance:', messagingInstance);

  // Check browser environment
  console.log('🔥 Firebase Debug: Window object:', typeof window);
  console.log(
    '🔥 Firebase Debug: Service Worker support:',
    'serviceWorker' in navigator
  );
  console.log(
    '🔥 Firebase Debug: Notification support:',
    'Notification' in window
  );

  // Test permission status
  const permission = Notification.permission;
  console.log('Notification permission:', permission);

  // Test PWA support
  const pwaSupported = 'serviceWorker' in navigator && 'PushManager' in window;
  console.log('PWA supported:', pwaSupported);

  // Test service worker registrations
  let serviceWorkersRegistered = false;
  try {
    const registrations = await navigator.serviceWorker.getRegistrations();
    serviceWorkersRegistered = registrations.length > 0;
    console.log(
      'Service workers registered:',
      serviceWorkersRegistered,
      registrations.length
    );
  } catch (error) {
    console.error('Error checking service workers:', error);
  }

  // Test token generation
  let tokenGenerated = false;
  try {
    const token = await generateToken();
    tokenGenerated = !!token;
    console.log('Token generation successful:', tokenGenerated);
  } catch (error) {
    console.error('Token generation failed:', error);
  }
};
