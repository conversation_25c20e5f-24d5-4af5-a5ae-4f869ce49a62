'use client';

import { BellOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Divider, Space, Typography } from 'antd';
import React, { useState } from 'react';
import {
  generateToken,
  getFirebaseApp,
  getFirebaseMessaging,
  getInitializationPromise,
  isFirebaseInitialized,
  isMessagingInitialized,
} from '../../notifications/firebase';
import { useNotificationContext } from '../FirebaseNotification/NotificationProvider';

const { Title, Text, Paragraph } = Typography;

const NotificationTest: React.FC = () => {
  const { showInAppNotification, isFirebaseReady } = useNotificationContext();
  const [testResults, setTestResults] = useState<{
    firebaseInit: boolean | null;
    messagingInit: boolean | null;
    permissionStatus: string | null;
    tokenGenerated: boolean | null;
    pwaSupported: boolean | null;
    serviceWorkersRegistered: boolean | null;
  }>({
    firebaseInit: null,
    messagingInit: null,
    permissionStatus: null,
    tokenGenerated: null,
    pwaSupported: null,
    serviceWorkersRegistered: null,
  });

  /**
   * Run diagnostics on Firebase notification integration.
   *
   * @remarks
   * Tests the following:
   * - Firebase initialization
   * - Messaging initialization
   * - Notification permission status
   * - Token generation
   *
   * @returns {Promise<void>}
   */
  const runDiagnostics = async () => {
    console.log('🔍 Running Firebase notification diagnostics...');

    // Test Firebase initialization
    const firebaseInit = isFirebaseInitialized();
    console.log('🔥 Firebase Debug: Firebase initialized:', firebaseInit);

    // Check Firebase app instance
    const firebaseApp = getFirebaseApp();
    console.log('🔥 Firebase Debug: Firebase app instance:', firebaseApp);

    // Check initialization promise
    const initPromise = getInitializationPromise();
    console.log('🔥 Firebase Debug: Initialization promise:', initPromise);

    if (initPromise) {
      try {
        await initPromise;
        console.log('🔥 Firebase Debug: Initialization promise resolved');
      } catch (error) {
        console.error(
          '🔥 Firebase Debug: Initialization promise rejected:',
          error
        );
      }
    }

    // Test messaging initialization
    const messagingInit = isMessagingInitialized();
    console.log('🔥 Firebase Debug: Messaging initialized:', messagingInit);

    // Check messaging instance
    const messagingInstance = getFirebaseMessaging();
    console.log('🔥 Firebase Debug: Messaging instance:', messagingInstance);

    // Check browser environment
    console.log('🔥 Firebase Debug: Window object:', typeof window);
    console.log(
      '🔥 Firebase Debug: Service Worker support:',
      'serviceWorker' in navigator
    );
    console.log(
      '🔥 Firebase Debug: Notification support:',
      'Notification' in window
    );

    // Test permission status
    const permission = Notification.permission;
    console.log('Notification permission:', permission);

    // Test PWA support
    const pwaSupported =
      'serviceWorker' in navigator && 'PushManager' in window;
    console.log('PWA supported:', pwaSupported);

    // Test service worker registrations
    let serviceWorkersRegistered = false;
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      serviceWorkersRegistered = registrations.length > 0;
      console.log(
        'Service workers registered:',
        serviceWorkersRegistered,
        registrations.length
      );
    } catch (error) {
      console.error('Error checking service workers:', error);
    }

    // Test token generation
    let tokenGenerated = false;
    try {
      const token = await generateToken();
      tokenGenerated = !!token;
      console.log('Token generation successful:', tokenGenerated);
    } catch (error) {
      console.error('Token generation failed:', error);
    }

    setTestResults({
      firebaseInit,
      messagingInit,
      permissionStatus: permission,
      tokenGenerated,
      pwaSupported,
      serviceWorkersRegistered,
    });
  };

  const testInAppNotification = () => {
    showInAppNotification(
      'Test Notification',
      'This is a test in-app notification to verify the system is working correctly.',
      'info'
    );
  };

  const testSuccessNotification = () => {
    showInAppNotification(
      'Success Test',
      'This is a success notification test.',
      'success'
    );
  };

  const testWarningNotification = () => {
    showInAppNotification(
      'Warning Test',
      'This is a warning notification test.',
      'warning'
    );
  };

  const testErrorNotification = () => {
    showInAppNotification(
      'Error Test',
      'This is an error notification test.',
      'error'
    );
  };

  const testBackgroundNotification = async () => {
    try {
      // Test if service worker can show notifications
      const registration = await navigator.serviceWorker.getRegistration(
        '/firebase-cloud-messaging-push-scope'
      );
      if (registration) {
        registration.active?.postMessage({
          type: 'TEST_BACKGROUND_NOTIFICATION',
        });
        console.log('🧪 Background notification test sent to service worker');
      } else {
        console.warn('⚠️ Service worker registration not found');
      }
    } catch (error) {
      console.error('❌ Error testing background notification:', error);
    }
  };

  const getStatusIcon = (status: boolean | null) => {
    if (status === null) {
      return '⏳';
    }
    return status ? '✅' : '❌';
  };

  const getStatusColor = (status: boolean | null) => {
    if (status === null) {
      return 'default';
    }
    return status ? 'success' : 'error';
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>Firebase Notification System Test</Title>

        <Paragraph>
          Use this page to test and diagnose the Firebase notification system.
        </Paragraph>

        <Alert
          message='VAPID Key Required'
          description='Make sure you have set the NEXT_PUBLIC_FIREBASE_VAPID_KEY in your .env file. You can get this from Firebase Console > Project Settings > Cloud Messaging > Web Push certificates.'
          type='warning'
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Divider>System Status</Divider>

        <Space direction='vertical' style={{ width: '100%' }}>
          <Text>
            {getStatusIcon(isFirebaseReady)} Firebase Ready:{' '}
            {isFirebaseReady ? 'Yes' : 'No'}
          </Text>

          <Text>
            {getStatusIcon(testResults.firebaseInit)} Firebase Initialized:{' '}
            {testResults.firebaseInit === null
              ? 'Not tested'
              : testResults.firebaseInit
                ? 'Yes'
                : 'No'}
          </Text>

          <Text>
            {getStatusIcon(testResults.messagingInit)} Messaging Initialized:{' '}
            {testResults.messagingInit === null
              ? 'Not tested'
              : testResults.messagingInit
                ? 'Yes'
                : 'No'}
          </Text>

          <Text>
            {getStatusIcon(testResults.pwaSupported)} PWA Supported:{' '}
            {testResults.pwaSupported === null
              ? 'Not tested'
              : testResults.pwaSupported
                ? 'Yes'
                : 'No'}
          </Text>

          <Text>
            {getStatusIcon(testResults.serviceWorkersRegistered)} Service
            Workers:{' '}
            {testResults.serviceWorkersRegistered === null
              ? 'Not tested'
              : testResults.serviceWorkersRegistered
                ? 'Registered'
                : 'Not Registered'}
          </Text>

          <Text>
            Permission Status:{' '}
            {testResults.permissionStatus || Notification.permission}
          </Text>

          <Text>
            {getStatusIcon(testResults.tokenGenerated)} Token Generation:{' '}
            {testResults.tokenGenerated === null
              ? 'Not tested'
              : testResults.tokenGenerated
                ? 'Success'
                : 'Failed'}
          </Text>
        </Space>

        <Divider>Diagnostic Tests</Divider>

        <Space wrap>
          <Button
            type='primary'
            icon={<CheckCircleOutlined />}
            onClick={runDiagnostics}
          >
            Run Diagnostics
          </Button>
        </Space>

        <Divider>In-App Notification Tests</Divider>

        <Space wrap>
          <Button icon={<BellOutlined />} onClick={testInAppNotification}>
            Test Info Notification
          </Button>

          <Button type='primary' onClick={testSuccessNotification}>
            Test Success
          </Button>

          <Button
            style={{ backgroundColor: '#faad14', borderColor: '#faad14' }}
            onClick={testWarningNotification}
          >
            Test Warning
          </Button>

          <Button danger onClick={testErrorNotification}>
            Test Error
          </Button>
        </Space>

        <Divider>Background Notification Tests</Divider>

        <Space wrap>
          <Button
            icon={<BellOutlined />}
            onClick={testBackgroundNotification}
            type='dashed'
          >
            Test Background Notification
          </Button>
        </Space>

        <Divider>Instructions</Divider>

        <Paragraph>
          <Title level={4}>To complete the setup:</Title>
          <ol>
            <li>
              Go to{' '}
              <a
                href='https://console.firebase.google.com'
                target='_blank'
                rel='noopener noreferrer'
              >
                Firebase Console
              </a>
            </li>
            <li>Select your project</li>
            <li>Go to Project Settings (gear icon)</li>
            <li>Click on Cloud Messaging tab</li>
            <li>Scroll to Web Push certificates</li>
            <li>Generate a new key pair or copy the existing VAPID key</li>
            <li>Add it to your .env file as NEXT_PUBLIC_FIREBASE_VAPID_KEY</li>
            <li>Restart your development server</li>
          </ol>
        </Paragraph>
      </Card>
    </div>
  );
};

export default NotificationTest;
