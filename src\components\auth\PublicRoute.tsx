'use client';

import React from 'react';

import ProtectedRoute from './ProtectedRoute';

interface PublicRouteProps {
  children: React.ReactNode;
}

/**
 * PublicRoute component for pages that should only be accessible when NOT authenticated
 * (e.g., login page, registration page)
 * Middleware handles the actual redirects
 */
const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  return <ProtectedRoute requireAuth={false}>{children}</ProtectedRoute>;
};

export default PublicRoute;
