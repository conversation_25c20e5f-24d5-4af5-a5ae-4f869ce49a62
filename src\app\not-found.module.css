/* app/not-found.module.css */

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #1a1a1a;
}

html[data-theme='dark'] .container {
  background-color: var(--color-primary);
  color: #fff;
}
html[data-theme='light'] .container {
  background-color: var(--color-background);
}

.content {
  text-align: center;
  animation: fadeInUp 0.6s ease-in-out;
}

.title {
  font-size: 6rem;
  font-weight: 700;
  margin: 0;
}

html[data-theme='light'] .title {
  color: var(--color-primary);
}
html[data-theme='dark'] .title {
  color: var(--color-white);
}

.subtitle {
  font-size: 1.5rem;
  margin-top: 0.5rem;
  color: var(--color-text-secondary);
}

.message {
  font-size: 1rem;
  color: var(--color-text-secondary);
  max-width: 400px;
  margin: 1rem auto 2rem;
}

.link {
  display: inline-block;
  font-size: 1rem;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

html[data-theme='light'] .link {
  color: var(--color-primary);
}
html[data-theme='dark'] .link {
  color: var(--color-white);
}

.link:hover {
  border-bottom: 1px solid var(--color-primary);
  color: var(--color-primary);
}

html[data-theme='dark'] .link:hover {
  border-bottom: 1px solid var(--color-white);
}
