import { NextRequest, NextResponse } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/forgot-password',
  '/verify-otp',
  // '/terms',
  '/offline',
  '/agency-login',
  '/new-password',
  '/check-your-inbox',
  '/two-factor-authentication',
  '/signup',
  '/steps',
  '/linkedin-callback',
  '/verify-certificate',
  '/firebase-messaging-sw.js',
  // '/notification-permission',
];

// Define static assets that should be publicly accessible
const publicAssets = [
  '/manifest.json',
  '/sw.js',
  '/workbox-',
  '/icons/',
  '/favicon',
  '/_next/',
  '/api/',
  '/verify-certificate/',
];

const authRoutes = ['/', '/login', '/forgot-password', '/verify-otp'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the current path is a public asset
  const isPublicAsset = publicAssets.some(asset => pathname.startsWith(asset));

  // Skip authentication for public assets
  if (isPublicAsset) {
    return NextResponse.next();
  }

  // Get authentication status from cookies
  const authToken = request.cookies.get('auth-token')?.value;

  const onBoardingSteps = request.cookies.get('onboarding-steps')?.value;
  // const isOnboardingCompleted = onBoardingSteps === 'completed';
  const isAuthenticated = authToken === 'authenticated';

  // Check if the current route is public
  const isPublicRoute = publicRoutes.includes(pathname);

  // Check if the current route is an auth route
  const isAuthRoute = authRoutes.includes(pathname);

  const isOnboardingPendingOrCompleted =
    !onBoardingSteps || onBoardingSteps === 'completed';
  // If user is authenticated and trying to access auth routes, redirect to dashboard
  if (
    isAuthenticated &&
    isOnboardingPendingOrCompleted &&
    (isAuthRoute || publicRoutes.includes(pathname))
  ) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // If user is not authenticated and trying to access protected routes, redirect to login
  if (!isAuthenticated && !isPublicRoute) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Allow the request to continue
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
