/* Dashboard Wrapper - Single source of truth for all dashboard styling */

/* Main Layout */
.dashboardLayout {
  min-height: 100vh;
  background: var(--dashboard-bg);
  transition: background-color 0.3s ease;

  /* Light mode variables */
  --dashboard-bg: #f5f7fa;
  --sidebar-bg: #ffffff;
  --header-bg: #ffffff;
  --content-bg: #ffffff;
  --text-primary: #1a1a1a;
  --text-secondary: #6c757d;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dark mode variables */
:global(html[data-theme='dark']) .dashboardLayout,
:global(body[data-theme='dark']) .dashboardLayout {
  --dashboard-bg: #0f1419;
  --sidebar-bg: #1f1f1f;
  --header-bg: #1f1f1f;
  --content-bg: #1f1f1f;
  --text-primary: #ffffff;
  --text-secondary: #d9d9d9;
  --border-color: #434343;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Header Styles */
.header {
  background: var(--header-bg) !important;
  border-bottom: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-light) !important;
  padding: 0 24px !important;
  height: 64px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menuButton {
  display: none !important;
  background: transparent !important;
  border: none !important;
  color: var(--text-primary) !important;
  font-size: 18px !important;
  cursor: pointer !important;
  padding: 8px !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
}

.menuButton:hover {
  background: var(--border-color) !important;
}

/* Sidebar Styles */
.sidebar {
  background: var(--sidebar-bg) !important;
  border-right: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-light) !important;
  height: 100vh !important;
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
  width: 256px !important;
  z-index: 200 !important;
  overflow-y: auto !important;
  transition: transform 0.3s ease !important;
}

.sidebarCollapsed {
  width: 80px !important;
}

.sidebarHidden {
  transform: translateX(-100%) !important;
}

.sidebarHeader {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 64px;
}

.logo {
  width: 32px;
  height: 32px;
  background: #dc3545;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo :global(.anticon) {
  font-size: 16px;
  color: white;
}

.logoText {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

.sidebarCollapsed .logoText {
  opacity: 0;
  width: 0;
}

/* Menu Styles */
.menu {
  background: transparent !important;
  border: none !important;
  padding: 8px 0 !important;
}

.menu :global(.ant-menu-item) {
  margin: 4px 12px !important;
  border-radius: 6px !important;
  height: 40px !important;
  line-height: 40px !important;
  color: var(--text-secondary) !important;
  transition: all 0.2s ease !important;
}

.menu :global(.ant-menu-item:hover) {
  background: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.menu :global(.ant-menu-item-selected) {
  background: #007bff !important;
  color: white !important;
}

.menu :global(.ant-menu-item-selected:hover) {
  background: #0056b3 !important;
  color: white !important;
}

.menu :global(.ant-menu-item .anticon) {
  font-size: 16px !important;
  margin-right: 12px !important;
}

/* Content Area */
.content {
  margin-left: 256px;
  min-height: 100vh;
  background: var(--dashboard-bg);
  transition: margin-left 0.3s ease;
}

.contentCollapsed {
  margin-left: 80px;
}

.contentMobile {
  margin-left: 0;
}

.contentInner {
  padding: 24px;
  background: var(--content-bg);
  min-height: calc(100vh - 64px);
  margin-top: 64px;
}

/* Cards and Components */
.card {
  background: var(--content-bg) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 8px !important;
  box-shadow: var(--shadow-light) !important;
  margin-bottom: 24px !important;
}

.card :global(.ant-card-head) {
  border-bottom: 1px solid var(--border-color) !important;
  background: var(--content-bg) !important;
}

.card :global(.ant-card-head-title) {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

/* User Profile */
.userProfile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.userProfile:hover {
  background: var(--border-color);
}

.userAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.userInfo {
  flex: 1;
  min-width: 0;
}

.userName {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userRole {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Logout Button */
.logoutButton {
  width: 100% !important;
  margin: 16px 12px 12px 12px !important;
  background: transparent !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
  border-radius: 6px !important;
  height: 40px !important;
  transition: all 0.2s ease !important;
}

.logoutButton:hover {
  background: #dc3545 !important;
  border-color: #dc3545 !important;
  color: white !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 240px !important;
  }

  .content {
    margin-left: 240px;
  }

  .contentCollapsed {
    margin-left: 80px;
  }
}

@media (max-width: 768px) {
  .menuButton {
    display: block !important;
  }

  .sidebar {
    transform: translateX(-100%);
  }

  .sidebarVisible {
    transform: translateX(0) !important;
  }

  .content {
    margin-left: 0;
  }

  .contentCollapsed {
    margin-left: 0;
  }

  .header {
    padding: 0 16px !important;
  }

  .contentInner {
    padding: 16px;
  }

  .logoText {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0 12px !important;
  }

  .contentInner {
    padding: 12px;
  }

  .card {
    margin-bottom: 16px !important;
  }

  .sidebarHeader {
    padding: 12px 16px;
  }

  .userProfile {
    padding: 8px 12px;
  }

  .logoutButton {
    margin: 12px 8px 8px 8px !important;
  }
}

/* Overlay for mobile */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
  display: none;
}

.overlayVisible {
  display: block;
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

.sidebarAnimateIn {
  animation: slideIn 0.3s ease-out;
}

.sidebarAnimateOut {
  animation: slideOut 0.3s ease-out;
}
