'use client';

import { useCallback, useEffect } from 'react';

interface ErrorHandlerOptions {
  onError?: (error: Error, errorInfo?: string) => void;
  logToConsole?: boolean;
  logToService?: boolean;
}

export const useErrorHandler = (options: ErrorHandlerOptions = {}): void => {
  const { onError, logToConsole = true, logToService = false } = options;

  const handleError = useCallback(
    (error: Error, errorInfo?: string): void => {
      if (logToConsole) {
        console.error('Global error caught:', error);
        if (errorInfo) {
          console.error('Error info:', errorInfo);
        }
      }

      if (logToService) {
        // Here you would send the error to your error tracking service
        // Example: Sentry, LogRocket, Bugsnag, etc.

        console.log('Would send to error service:', { error, errorInfo });
      }

      onError?.(error, errorInfo);
    },
    [onError, logToConsole, logToService]
  );

  useEffect(() => {
    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
      handleError(new Error(event.reason), 'Unhandled Promise Rejection');
    };

    // Handle global JavaScript errors
    const handleGlobalError = (event: ErrorEvent): void => {
      handleError(
        new Error(event.message),
        `Global Error at ${event.filename}:${event.lineno}:${event.colno}`
      );
    };

    // Add event listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);

    // Cleanup
    return (): void => {
      window.removeEventListener(
        'unhandledrejection',
        handleUnhandledRejection
      );
      window.removeEventListener('error', handleGlobalError);
    };
  }, [handleError]);
};

export default useErrorHandler;
