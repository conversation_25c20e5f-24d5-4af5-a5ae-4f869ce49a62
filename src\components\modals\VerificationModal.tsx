'use client';

import { Button, Input, InputRef, Modal, Typography } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { Endpoints } from '../../config/endpoints';
import { getApiData } from '../../helpers/ApiHelper';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../Notification/NotificationContext';
import styles from './Modal.module.css';

interface VerificationModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface response {
  status?: boolean;
  message?: string;
}

const { Title, Text } = Typography;

const VerificationModal: React.FC<VerificationModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);

  const notification = useNotification();

  const inputRefs = useRef<(InputRef | null)[]>([]);

  const { t } = useTranslation();

  const [loading, setLoading] = useState(false);

  // Handle OTP input changes
  const handleOTPChange = useCallback(
    (index: number, value: string) => {
      if (value.length > 1) {
        return;
      }

      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }

      if (value) {
        const isComplete = newOtp.every(digit => digit !== '');
        if (isComplete) {
          verify2FA(newOtp.join(''));
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [otp]
  );

  // Handle key down events
  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const verify2FA = async (otpCode?: string) => {
    const finalOtpCode = otpCode || otp.join('');

    if (finalOtpCode.length !== 6) {
      notification.error({
        message: 'Verification failed.',
        description: 'Please enter the complete 6-digit code',
      });
      return;
    }

    try {
      setLoading(true);
      const response = await getApiData<{ token: string }, response>({
        url: Endpoints.verify2FAOtp,
        method: 'POST',
        data: {
          token: finalOtpCode,
        },
      });

      if (response && response.status === true) {
        onSuccess();
        onClose();
        setOtp(['', '', '', '', '', '']);
        setLoading(false);
      } else {
        notification.error({
          message: 'Verification failed.',
          description:
            response?.message || 'Invalid verification code. Please try again.',
        });
        setOtp(['', '', '', '', '', '']);
        setLoading(false);
        inputRefs.current[0]?.focus();
      }
    } catch (error) {
      notification.error({
        message: 'Verification failed.',
        description:
          error?.message || 'Invalid verification code. Please try again.',
      });
      setOtp(['', '', '', '', '', '']);
      setLoading(false);
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={() => {
        onClose();
        setOtp(['', '', '', '', '', '']);
      }}
      footer={null}
      centered
      width={600}
    >
      <div className={styles.container}>
        <Title level={3} className={styles.title}>
          {t('twoFAModal.title')}
        </Title>

        <Text className={styles.subTitle}>{t('twoFAModal.subTitle')}</Text>

        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '8px',
            marginBottom: '24px',
          }}
        >
          {otp.map((digit, index) => (
            <Input
              key={index}
              ref={el => {
                inputRefs.current[index] = el;
              }}
              value={digit}
              onChange={e => {
                const value = e.target.value;
                if (/^[0-9]*$/.test(value)) {
                  // Only allow digits ( no special characters)
                  handleOTPChange(index, value);
                }
              }}
              onKeyDown={e => handleKeyDown(index, e)}
              maxLength={1}
              style={{
                width: isMobile ? '45px' : '50px',
                height: isMobile ? '45px' : '50px',
                textAlign: 'center',
                fontSize: '18px',
              }}
            />
          ))}
        </div>

        <Button
          type='primary'
          onClick={() => verify2FA()}
          loading={loading}
          style={{
            width: '200px',
            marginTop: '10px',
          }}
        >
          {t('twoFAModal.verify')}
        </Button>
      </div>
    </Modal>
  );
};

export default VerificationModal;
