import { Button, Typography } from 'antd';
import Lottie from 'lottie-react';
import { useRouter } from 'next/navigation';
import { isMobile } from 'react-device-detect';
import animationData from '../../../public/lottie/sealSucess.json';
import { useTranslation } from '../../hooks/useTranslation';
import styles from './page.module.css';

const { Title, Text } = Typography;

const SealProcessDone = () => {
  const router = useRouter();

  const { t } = useTranslation();

  return (
    <div className={styles.container}>
      <Lottie
        animationData={animationData}
        loop={true}
        style={{
          width: isMobile ? '100px' : '150px',
          height: isMobile ? '100px' : '150px',
        }}
      />
      <Title level={2} className={styles.title}>
        {t('sealProcessDone.title')}
      </Title>
      <Text className={styles.text}>{t('sealProcessDone.text')}</Text>
      <Button
        type='primary'
        style={{
          marginTop: '40px',
          width: '200px',
        }}
        onClick={() => {
          router.push('/my-ideas');
        }}
      >
        {t('sealProcessDone.button')}
      </Button>
    </div>
  );
};

export default SealProcessDone;
