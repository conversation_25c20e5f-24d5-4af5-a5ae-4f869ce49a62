'use client';

import { useEffect } from 'react';

const ServiceWorkerRegistration: React.FC = () => {
  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      // Register service workers in sequence to avoid conflicts
      const registerServiceWorkers = async () => {
        try {
          // First, register Firebase messaging service worker with specific scope
          const messagingRegistration = await navigator.serviceWorker.register(
            '/firebase-messaging-sw.js',
            {
              scope: '/firebase-cloud-messaging-push-scope',
              updateViaCache: 'none', // Ensure fresh service worker updates
            }
          );
          console.log(
            '✅ Firebase Messaging Service Worker registered successfully'
          );

          // Then register PWA service worker (if available) - this handles app caching
          try {
            const pwaRegistration = await navigator.serviceWorker.register(
              '/sw.js',
              {
                scope: '/', // PWA service worker handles the entire app
                updateViaCache: 'imports',
              }
            );
            console.log('✅ PWA Service Worker registered successfully');

            // Check for updates on PWA service worker
            pwaRegistration.addEventListener('updatefound', () => {
              const newWorker = pwaRegistration.installing;
              if (newWorker) {
                newWorker.addEventListener('statechange', () => {
                  if (
                    newWorker.state === 'installed' &&
                    navigator.serviceWorker.controller
                  ) {
                    // New service worker is available
                    console.log(
                      '🔄 New PWA service worker available, reloading...'
                    );
                    window.location.reload();
                  }
                });
              }
            });
          } catch (pwaError) {
            console.warn(
              '⚠️ PWA Service Worker not available (development mode):',
              pwaError
            );
          }

          // Check for updates on Firebase messaging service worker
          messagingRegistration.addEventListener('updatefound', () => {
            const newWorker = messagingRegistration.installing;
            if (newWorker) {
              console.log('🔄 Firebase messaging service worker updating...');
            }
          });
        } catch (error) {
          console.error('❌ Service Worker registration failed:', error);
        }
      };

      registerServiceWorkers();

      // Listen for messages from service workers
      navigator.serviceWorker.addEventListener('message', event => {
        console.log('📨 Message from service worker:', event.data);

        if (event.data && event.data.type === 'SKIP_WAITING') {
          console.log('🔄 Service worker requesting reload...');
          window.location.reload();
        }

        // Handle Firebase messaging events
        if (event.data && event.data.type === 'FCM_MESSAGE') {
          console.log(
            '📩 FCM message received in main thread:',
            event.data.payload
          );
        }
      });
    }
  }, []);

  return null;
};

export default ServiceWorkerRegistration;
