'use client';
import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { debounce } from '@/helpers/lodashFunctions';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import {
  downloadZip,
  formatBytes,
  getChunkSize,
  getStatusText,
  getUserData,
} from '@/utils/CommonFunctions';
import { LoaderTypeParams } from '@/utils/CommonInterfaces';
import { uploadProcessStart } from '@/utils/UploadFile';
import { Button, Col, Form, Row, Spin, Typography, Upload } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { UploadChangeParam } from 'antd/es/upload';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import dayjs from 'dayjs';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { BiSolidCloudUpload } from 'react-icons/bi';
import { useTheme } from '../../contexts/ThemeContext';
import FileCard from '../FileCard/FileCard';
import VerificationModal from '../modals/VerificationModal';
import { useNotification } from '../Notification/NotificationContext';
import SealProcessDone from '../SealProcessDone/SealProcessDone';
import { SASResponse, UploadProgress } from '../uploadFiles/page';
import styles from './DraftIdea.module.css';
import { CombinedFile, FolderData } from './FolderCard';

const { Text } = Typography;

const FolderView = ({
  folderDetails,
  loader,
  updateFolderDetails,
  viewMoreClick,
}: {
  folderDetails?: FolderData | null;
  loader?: boolean;
  updateFolderDetails: (type?: LoaderTypeParams) => void;
  viewMoreClick: (tab: string) => void;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const pathname = usePathname();
  const { user } = useAppSelector(s => s.auth);
  const abortControllers = useRef<Record<string, AbortController>>({});
  const [fileList, setFileList] = useState<CombinedFile[]>(
    folderDetails?.files || []
  );

  const max_storage =
    user?.is_basic_plan === true
      ? Number(user?.basic_plan_storage) - Number(user?.total_uploaded_storage)
      : Number(user?.subscriptionData?.priceData?.metadata?.total_storage) -
        Number(user?.total_uploaded_storage);

  const maxStorageRef = useRef(max_storage);

  useEffect(() => {
    maxStorageRef.current = max_storage;
  }, [max_storage]);

  const theme = useTheme();

  const searchParams = useSearchParams();
  const type = searchParams.get('type');

  const [progressMap, setProgressMap] = useState<
    Record<string, UploadProgress>
  >({});
  const notification = useNotification();
  const folderId = pathname.split('/').pop();
  const [sealLoader, setSealLoader] = useState<boolean>(false);
  const [draftLoader, setDraftLoader] = useState<boolean>(false);
  const [showAllFiles, setShowAllFiles] = useState<boolean>(false);
  const [downloadLoading, setDownloadLoading] = useState<boolean>(false);

  const [openVerificationModal, setVerificationModal] = useState(false);

  useEffect(() => {
    form.setFieldsValue({
      notes: folderDetails?.notes || '',
    });
    return () => {};
  }, [folderDetails]);

  const router = useRouter();

  const uploadedUIDsRef = useRef<Set<string>>(new Set());

  const debouncedOnChange = useRef(
    debounce((info: UploadChangeParam<UploadFile>) => {
      const preFiles = folderDetails?.files ?? [];

      // Check individual file sizes first
      const oversizedFiles = info.fileList.filter(
        file => file.size && file.size > maxStorageRef.current
      );

      if (oversizedFiles.length > 0) {
        notification.error({
          message: 'File Size Error',
          description: `You cannot upload files larger than ${formatBytes(maxStorageRef.current)} `,
        });
        return;
      }

      // Calculate total size
      const existingFilesSize = preFiles.reduce(
        (total, file) => total + (file.size || 0),
        0
      );
      const newFilesSize = info.fileList.reduce(
        (total, file) => total + (file.size || 0),
        0
      );
      const totalSizeMB = existingFilesSize + newFilesSize;

      if (totalSizeMB > maxStorageRef.current) {
        notification.error({
          message: 'Error',
          description: `You cannot upload files larger than ${formatBytes(maxStorageRef.current)} `,
        });
        return;
      }

      const allFiles = [...preFiles, ...info.fileList];
      setFileList(allFiles);

      const newFiles = info.fileList.filter(
        file => !uploadedUIDsRef.current.has(file.uid)
      );

      for (let index = 0; index < newFiles.length; index++) {
        const file = newFiles[index];
        const isLast = index === newFiles.length - 1;

        uploadedUIDsRef.current.add(file.uid);
        handleUpload(file, isLast);
      }
    }, 200)
  ).current;

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    onChange(info) {
      debouncedOnChange(info);
    },
    fileList,
    showUploadList: false,
  };

  useEffect(() => {
    if (folderDetails?.files) {
      setFileList(folderDetails.files);
    }
  }, [folderDetails]);

  const sm = useMediaQuery('(max-width: 768px)');
  const md = useMediaQuery('(max-width: 900px)');

  // Helper functions for file display management
  const getFilesPerRow = () => {
    // Based on your Col spans: xl={6} lg={8} md={12} sm={12} xs={12}
    if (typeof window !== 'undefined') {
      if (window.innerWidth >= 1200) {
        return 4;
      } // xl: 24/6 = 4 files per row
      if (window.innerWidth >= 992) {
        return 3;
      } // lg: 24/8 = 3 files per row
      return 2; // md, sm, xs: 24/12 = 2 files per row
    }
    return 4; // default for SSR
  };

  const getVisibleFiles = () => {
    if (showAllFiles) {
      return fileList;
    }

    const filesPerRow = getFilesPerRow();
    const maxVisibleFiles =
      docStatus === 'Draft' ? filesPerRow * 2 - 1 : filesPerRow * 2;

    return fileList.slice(0, maxVisibleFiles);
  };

  const shouldShowViewMore = () => {
    const filesPerRow = getFilesPerRow();
    const maxVisibleFiles = filesPerRow * 2 - 1;
    return fileList.length > maxVisibleFiles && !showAllFiles;
  };

  const shouldShowViewLess = () => {
    const filesPerRow = getFilesPerRow();
    const maxVisibleFiles = filesPerRow * 2 - 1;
    return showAllFiles && fileList.length > maxVisibleFiles;
  };

  interface UpdateFolderRequest {
    _id?: string;
    first_step: boolean;
    notes?: string;
  }
  const updateFolderStep = async (type?: string) => {
    const data = await form.getFieldsValue();
    if (type === 'draft') {
      setDraftLoader(true);
    } else if (type === 'seal') {
      setSealLoader(true);
    }
    try {
      const res = await getApiData<UpdateFolderRequest, ApiResponse>({
        url: Endpoints.updateFolder,
        method: 'POST',
        data: {
          _id: folderId,
          first_step: false,
          notes: data.notes,
        },
      });

      if (res && res.status === true) {
        if (type === 'seal') {
          router.push(`/my-ideas/ready-to-seal/${folderId}`);
        } else if (type === 'draft') {
          router.push(`/my-ideas`);
        } else {
          updateFolderDetails('silent');
        }
      } else {
        notification.error({
          message: 'Error',
          description: res?.message || 'Please try again later.',
        });
      }
      setDraftLoader(false);
      setSealLoader(false);
    } catch (error) {
      setDraftLoader(false);
      setSealLoader(false);
      notification.error({
        message: 'Error',
        description: error?.message || 'Please try again later.',
      });
    }
  };

  interface SasRequest {
    fileName: string;
    file_type: string;
    file_size: number;
    userId: string;
    folder_id: string;
    totalChunks: number;
  }

  /**
   * Uploads a single file in chunks to Azure using SAS URL.
   * Supports pause/resume and cancel via AbortController.
   */
  const uploadFile = async (
    file: UploadFile,
    isLast: boolean
  ): Promise<void> => {
    const originFile = file || (file as UploadFile);
    if (
      !originFile ||
      !originFile.name ||
      !originFile.size ||
      !originFile.type
    ) {
      return;
    }

    try {
      const controller = new AbortController();
      abortControllers.current[file.uid] = controller;
      const res = await getApiData<SasRequest, SASResponse>({
        url: Endpoints.getSasToken,
        method: 'POST',
        data: {
          userId: user?._id || '',
          fileName: originFile.name,
          file_size: originFile.size,
          file_type: originFile.type,
          folder_id: folderId || '',
          totalChunks: getChunkSize(originFile.size),
        },
      });

      if (res && res.status === true) {
        console.log('SAS URL:', res.data.sasUrl);
        const data = res?.data || {};
        uploadProcessStart(
          data,
          originFile,
          controller,
          file,
          updateFolderStep,
          setProgressMap,
          notification,
          isLast
        );
      } else {
        throw new Error('SAS URL missing');
      }
    } catch (err) {
      if (err.name === 'AbortError') {
        notification.error({
          message: `${originFile.name} upload canceled.`,
        });
      } else {
        notification.error({
          message: `Error uploading ${originFile.name}`,
        });
      }
      setProgressMap(prev => ({
        ...prev,
        [file.uid]: { percent: 0, status: 'error' },
      }));
    }
  };

  /** Upload all files in queue */
  const handleUpload = async (
    file: UploadFile,
    isLast: boolean
  ): Promise<void> => {
    // for (const file of files) {
    //   console.log('in for files ====>', file);
    //   if (progressMap[file.uid]?.status === 'done') {
    //     continue;
    //   }
    await uploadFile(file, isLast);
    // }
  };

  useEffect(() => {
    getUserData();
  }, [fileList]);

  const docStatus = getStatusText(folderDetails?.status || '-1');

  return (
    <div className={styles.container}>
      <Row gutter={[16, 32]}>
        {loader ? (
          <div className={styles.foldersSpinner}>
            <Spin size='large' />
          </div>
        ) : (
          <>
            <Col span={24}>
              <div className={styles.docDataContainer}>
                <div className={styles.docData}>
                  <Text className={styles.docDataLabel}>
                    {t('draftIdea.status')}
                  </Text>
                  <Text className={styles.docDataText}>
                    {getStatusText(folderDetails?.status || '-1')}
                  </Text>
                </div>
                <div className={styles.docData}>
                  <Text className={styles.docDataLabel}>
                    {t('draftIdea.submittedOn')}
                  </Text>
                  <Text className={styles.docDataText}>
                    {dayjs(folderDetails?.createdAt).format('MMM DD, YYYY')}
                  </Text>
                </div>
                <div className={styles.docData}>
                  <Text className={styles.docDataLabel}>
                    {t('draftIdea.documentId')}
                  </Text>
                  <Text className={styles.docDataText}>
                    {folderDetails?._id}
                  </Text>
                </div>
                {docStatus === 'Sealed' && (
                  <div className={styles.docData}>
                    <Text className={styles.docDataLabel}>
                      {t('draftIdea.verification')}
                    </Text>
                    <Text className={styles.docDataText}>[View on chain]</Text>
                  </div>
                )}
              </div>
            </Col>
            {type === 'success' ? (
              <Col
                span={24}
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '60vh',
                }}
              >
                <SealProcessDone />
              </Col>
            ) : (
              <>
                <Col
                  span={24}
                  style={{
                    marginTop: sm ? '-15px' : '0px',
                  }}
                >
                  <label className={styles.formLabel}>
                    {t('draftIdea.attachedFiles')}
                  </label>
                  <Row
                    className={styles.fileCardRow}
                    style={{
                      marginTop: '10px',
                    }}
                    gutter={[12, 12]}
                  >
                    {docStatus === 'Sealed' ? (
                      <Col xl={6} lg={8} md={12} sm={12} xs={12}>
                        <FileCard
                          type='Sealed'
                          style={{
                            minWidth: '100%',
                          }}
                          size={folderDetails?.total_file_storage || 0}
                          filename={
                            folderDetails?.files
                              ? folderDetails?.files?.length > 1
                                ? `${folderDetails?.files?.length} Sealed Files`
                                : `${folderDetails?.files?.length} Sealed File`
                              : 'Sealed File'
                          }
                          creator={
                            folderDetails?.files &&
                            folderDetails?.files?.length > 0
                              ? folderDetails?.files[0]?.fullname
                              : '-'
                          }
                          createdAt={
                            folderDetails?.files &&
                            folderDetails?.files?.length > 0
                              ? folderDetails?.files[0]?.createdAt
                              : '-'
                          }
                          creatorId={folderDetails?.shared_by_owner_id}
                          acessRole={folderDetails?.folder_access_role}
                        />
                      </Col>
                    ) : (
                      getVisibleFiles().map(file => {
                        const uid = file.uid ?? file._id ?? file.name;
                        const filename =
                          file.file_name || file.name || file.fileName || '';
                        const size = String(file.file_size || file.size || '');
                        const progress = file.uid
                          ? progressMap[file.uid]
                          : undefined;
                        return (
                          <Col key={uid} xl={6} lg={8} md={12} sm={12} xs={12}>
                            <FileCard
                              key={uid}
                              filename={filename}
                              size={size}
                              file_url={file.file_url}
                              progress={progress}
                              style={{
                                minWidth: '100%',
                              }}
                              fileId={file?._id}
                              creator={file?.fullname}
                              createdAt={
                                file?.createdAt
                                  ? dayjs(file.createdAt).toDate()
                                  : new Date()
                              }
                              creatorId={folderDetails?.shared_by_owner_id}
                              acessRole={folderDetails?.folder_access_role}
                              showDelete={
                                folderDetails?.shared_by_owner_id === user?._id
                                  ? true
                                  : false
                              }
                              folderId={folderDetails?._id}
                              removedFile={(fileId: string | number) => {
                                setFileList(prev =>
                                  prev.filter(n => n._id !== fileId)
                                );
                              }}
                            />
                          </Col>
                        );
                      })
                    )}

                    {docStatus === 'Draft' &&
                      (folderDetails?.shared_by_owner_id === user?._id ||
                        folderDetails?.folder_access_role === 'admin' ||
                        folderDetails?.folder_access_role === 'user') && (
                        <Col xl={6} lg={8} md={12} sm={12} xs={12}>
                          <Upload.Dragger
                            {...uploadProps}
                            style={{
                              backgroundColor:
                                theme?.theme === 'dark' ? '#16406f' : '#D7FFF1',
                            }}
                            className={styles.dragger}
                          >
                            <div
                              className={styles.uploadParent}
                              style={{
                                minWidth: '100%',
                              }}
                            >
                              <div
                                className={styles.uploadContainer}
                                style={{
                                  minWidth: '100%',
                                }}
                              >
                                <div className={styles.iconBox}>
                                  <BiSolidCloudUpload className={styles.icon} />
                                </div>
                                <div className={styles.label}>
                                  {t('draftIdea.upload_ideas')}
                                </div>
                              </div>
                            </div>
                          </Upload.Dragger>
                        </Col>
                      )}
                  </Row>

                  {/* View More/View Less button */}
                  {(shouldShowViewMore() || shouldShowViewLess()) && (
                    <div className={styles.viewMoreContainer}>
                      <Button
                        type='link'
                        onClick={() => viewMoreClick('All Files')}
                        className={styles.viewMoreButton}
                      >
                        {showAllFiles
                          ? 'View Less'
                          : `${t('draftIdea.view_more')} (${fileList.length - (getFilesPerRow() * 2 - 1)} ${t('draftIdea.more_files')})`}
                      </Button>
                    </div>
                  )}
                </Col>

                <Form
                  form={form}
                  layout='vertical'
                  initialValues={{ notes: folderDetails?.notes || '' }}
                  style={{ width: '100%' }}
                >
                  <Col
                    span={24}
                    style={{
                      marginTop: sm ? '-20px' : '5px',
                    }}
                  >
                    <Form.Item
                      name='notes'
                      label={
                        <Text className={styles.formLabel}>
                          {t('draftIdea.notes')}
                        </Text>
                      }
                    >
                      <TextArea
                        disabled={
                          docStatus === 'Sealed' ||
                          folderDetails?.folder_access_role === 'legal_user'
                        }
                        rows={4}
                        className={styles.textArea}
                        style={{ marginTop: sm ? '0px' : '10px' }}
                      />
                    </Form.Item>
                  </Col>
                </Form>
                {docStatus !== 'Sealed' && docStatus !== 'In Review' && (
                  <Col
                    span={24}
                    style={{
                      marginTop: sm ? '-30px' : '5px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '30px',
                      }}
                    >
                      <Text className={styles.formLabel}>
                        {t('draftIdea.ready_to_seal_prompt')}
                      </Text>
                      <Text className={styles.promptText}>
                        {t('draftIdea.ready_to_seal_prompt_description')}
                      </Text>
                    </div>
                  </Col>
                )}

                {docStatus === 'In Review' && (
                  <>
                    <Col
                      span={24}
                      style={{
                        marginTop: '-20px',
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: '30px',
                        }}
                      >
                        <Text className={styles.formLabel}>
                          {t('draftIdea.seal_progress')}
                        </Text>
                      </div>
                    </Col>
                    <Col span={24} style={{ marginTop: '-15px' }}>
                      <div className={styles.docDataContainer}>
                        <div className={styles.docData}>
                          <Text className={styles.docDataLabel}>
                            {t('draftIdea.owner')}
                          </Text>
                          <Text className={styles.docDataText}>
                            {/* {getStatusText(folderDetails?.status || '-1')} */}
                          </Text>
                        </div>
                        <div className={styles.docData}>
                          <Text className={styles.docDataLabel}>
                            {t('draftIdea.review_started')}
                          </Text>
                          <Text className={styles.docDataText}>
                            {/* {dayjs(folderDetails?.createdAt).format('MMM DD, YYYY')} */}
                          </Text>
                        </div>
                        <div className={styles.docData}>
                          <Text className={styles.docDataLabel}>
                            {t('draftIdea.current_stage')}
                          </Text>
                          <Text className={styles.docDataText}>
                            {t('draftIdea.awaiting_review')}
                          </Text>
                        </div>
                      </div>
                    </Col>
                  </>
                )}

                {docStatus === 'Sealed' ? (
                  <Col span={24}>
                    <div className={styles.btnParent}>
                      <div
                        className={styles.buttonGroup}
                        style={{
                          width: sm ? '100%' : md ? '430px' : '550px',
                          display: 'flex',
                          flexDirection: sm ? 'column' : 'row',
                          marginTop: sm ? '-20px' : '0px',
                        }}
                      >
                        <Button
                          type='primary'
                          onClick={() => {
                            router.push(
                              `/my-ideas/sealed-documents/${folderId}`
                            );
                          }}
                          style={{
                            width: '100%',
                          }}
                        >
                          {t('draftIdea.download_proof')}
                        </Button>
                        <Button
                          type='primary'
                          loading={downloadLoading}
                          onClick={async () => {
                            setVerificationModal(true);
                          }}
                          style={{
                            width: '100%',
                          }}
                        >
                          {downloadLoading
                            ? t('draftIdea.downloading')
                            : t('draftIdea.Download_proof_data')}
                        </Button>
                      </div>
                    </div>
                  </Col>
                ) : docStatus !== 'In Review' ? (
                  <Col span={24}>
                    <div className={styles.btnParent}>
                      <div
                        className={styles.buttonGroup}
                        style={{
                          width: sm ? '100%' : '450px',
                        }}
                      >
                        <Button
                          type='primary'
                          disabled={
                            folderDetails?.shared_by_owner_id === user?._id
                              ? false
                              : true
                          }
                          onClick={() => {
                            if (fileList.length === 0) {
                              notification.error({
                                message: 'Error',
                                description: 'Please upload at least one file.',
                              });
                              return;
                            }
                            if (folderId) {
                              updateFolderStep('seal');
                            }
                          }}
                          loading={sealLoader}
                          style={{
                            width: '100%',
                          }}
                        >
                          {t('draftIdea.seal_my_idea_now')}
                        </Button>
                        <Button
                          type='default'
                          disabled={
                            folderDetails?.folder_access_role === 'legal_user'
                          }
                          onClick={() => {
                            if (folderId) {
                              updateFolderStep('draft');
                            }
                          }}
                          loading={draftLoader}
                          style={{
                            width: '100%',
                          }}
                        >
                          {t('draftIdea.save_draft')}
                        </Button>
                      </div>
                    </div>
                  </Col>
                ) : null}
              </>
            )}
          </>
        )}
      </Row>
      <VerificationModal
        visible={openVerificationModal}
        onClose={() => setVerificationModal(false)}
        onSuccess={async () => {
          if (folderDetails?._id && folderDetails?.name) {
            setDownloadLoading(true);
            try {
              await downloadZip(
                folderDetails?._id || '',
                folderDetails?.name || ''
              );
            } catch (error) {
              console.error('Download failed:', error);
            } finally {
              setDownloadLoading(false);
            }
          }
        }}
      />
    </div>
  );
};

export default FolderView;
