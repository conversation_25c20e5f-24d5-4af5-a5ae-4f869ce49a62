import ErrorBoundary from '@/components/error/ErrorBoundary';
import { NotificationProvider } from '@/components/Notification/NotificationContext';
import AntdProvider from '@/components/providers/AntdProvider';
import ReduxProvider from '@/components/providers/ReduxProvider';
import ServiceWorkerRegistration from '@/components/pwa/ServiceWorkerRegistration';
import PWAInstallBanner from '@/components/ui/PWAInstallBanner';
import PWAStatus from '@/components/ui/PWAStatus';
import siteConfig from '@/config/site.config';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { colors } from '@/theme/antdTheme';
import '@ant-design/v5-patch-for-react-19';
import { GoogleOAuthProvider } from '@react-oauth/google';
import type { Metadata, Viewport } from 'next';
import { Poppins, Radio_Canada_Big } from 'next/font/google';
import { NotificationFirebaseProvider } from '../components/FirebaseNotification/NotificationProvider';
import './globals.css';
import { StyledComponentsRegistry } from './styled-components/registry';

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  style: ['normal'],
  display: 'swap',
  variable: '--font-poppins',
});

const radioCanadaBig = Radio_Canada_Big({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  style: ['normal'],
  display: 'swap',
  variable: '--font-radio-canada',
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: colors.primary,
};

export const metadata: Metadata = {
  title: 'Seal my idea',
  description: 'Seal my idea - Secure Authentication Platform',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Seal my idea',
  },
  formatDetection: {
    telephone: false,
  },
  icons: {
    icon: '/icons/icon.svg',
    shortcut: '/icons/icon.svg',
    apple: [
      { url: '/icons/152X152.png', sizes: '152x152' },
      { url: '/icons/192X192.png', sizes: '192x192' },
    ],
  },
  openGraph: {
    type: 'website',
    siteName: 'Seal my idea',
    title: 'Seal my idea',
    description: 'Seal my idea - Secure Authentication Platform',
  },
  twitter: {
    card: 'summary',
    title: 'Seal my idea',
    description: 'Seal my idea - Secure Authentication Platform',
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'Seal my idea',
    'msapplication-TileColor': colors.primary,
    'msapplication-TileImage': '/icons/icon-144x144.png',
    'msapplication-tap-highlight': 'no',
    'application-name': 'Seal my idea',
    'msapplication-config': 'none',
  },
  robots: {
    index: false, // ❌ do not index
    follow: false, // ❌ do not follow links
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en'>
      <body className={`${poppins.variable} ${radioCanadaBig.variable}`}>
        <StyledComponentsRegistry>
          <ThemeProvider>
            <NotificationFirebaseProvider>
              <NotificationProvider>
                <ReduxProvider>
                  <ErrorBoundary>
                    <AntdProvider>
                      <ServiceWorkerRegistration />
                      <PWAInstallBanner />
                      <GoogleOAuthProvider
                        clientId={`${siteConfig.googleClientId}`}
                      >
                        {children}
                      </GoogleOAuthProvider>
                      {/* <PWAInstallPrompt /> */}
                      <PWAStatus />
                    </AntdProvider>
                  </ErrorBoundary>
                </ReduxProvider>
              </NotificationProvider>
            </NotificationFirebaseProvider>
          </ThemeProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
