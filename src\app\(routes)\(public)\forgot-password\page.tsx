'use client';

import AuthWrapper, { authStyles } from '@/components/auth/AuthWrapper';
import { useNotification } from '@/components/Notification/NotificationContext';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import siteConfig from '@/config/site.config';
import { useTheme } from '@/contexts/ThemeContext';
import { getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { MailOutlined } from '@ant-design/icons';
import { Button, Form, Input, Typography } from 'antd';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

const { Title, Text } = Typography;

interface forgotPasswordResponse {
  status: boolean;
  message: string;
  otp?: string;
}

const ForgotPasswordPage: React.FC = () => {
  const notification = useNotification();
  const { theme } = useTheme();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const { t } = useTranslation();

  const handleSubmit = async (values: { email: string }) => {
    setLoading(true);

    try {
      setLoading(true);

      const response = await getApiData<
        { email: string; type: string },
        forgotPasswordResponse
      >({
        url: `${siteConfig.apiUrl}${Endpoints.forgotPassword}`,
        method: 'POST',
        data: {
          email: values.email,
          type: 'forgot_password_verification',
        },
        customUrl: true,
      });

      if (response && response.status === true) {
        notification.success({
          message: 'OTP Sent',
          description: `Your OTP is ${response?.otp}`,
        });
        router.push(
          `/verify-otp?email=${encodeURIComponent(values.email)}&from=forgot_password`
        );
        setLoading(false);
      } else {
        notification.error({
          message: 'Forgot password failed.',
          description: response?.message || 'Please try again later.',
        });
        setLoading(false);
      }
    } catch (error) {
      notification.error({
        message: 'Forgot password failed.',
        description: error?.message || 'Network error. Please try again.',
      });
      setLoading(false);
    }
  };

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');

  return (
    <AuthWrapper>
      <div className={authStyles.logoSection}>
        <Image
          src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
          alt='Swiss Trust Layer'
          className={authStyles.logoImage}
          width={180}
          height={180}
          style={{
            width: xs ? '175px' : sm ? '190px' : '220px',
            height: 'auto',
          }}
        />

        <Title level={2} className={authStyles.formTitle}>
          {t('forgotPassword.title')}
        </Title>
        <Text className={authStyles.formSubtitle}>
          {t('forgotPassword.subtitle')}
          <br />
          {t('forgotPassword.subtitle1')}
        </Text>
      </div>

      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        autoComplete='off'
      >
        <Form.Item
          label={t('forgotPassword.email')}
          name='email'
          rules={[
            {
              required: true,
              message: `${t('forgotPassword.required_email')}`,
            },
            { type: 'email', message: `${t('forgotPassword.valid_email')}` },
          ]}
          className={authStyles.formItem}
        >
          <Input
            placeholder={t('forgotPassword.email_placeholder')}
            type='email'
            className={authStyles.emailInput}
            prefix={<MailOutlined />}
          />
        </Form.Item>

        <Form.Item className={authStyles.sendCodeButton}>
          <Button
            type='primary'
            htmlType='submit'
            loading={loading}
            disabled={loading}
            className={authStyles.primaryButton}
            style={{
              marginTop: '20px',
            }}
          >
            {t('forgotPassword.send_code')}
          </Button>
        </Form.Item>
      </Form>

      {/* <div className={authStyles.backToLoginLink}>
        <Link href='/login'>
          <ArrowLeftOutlined /> {t('forgotPassword.back_to_login')}
        </Link>
      </div> */}
    </AuthWrapper>
  );
};

export default ForgotPasswordPage;
