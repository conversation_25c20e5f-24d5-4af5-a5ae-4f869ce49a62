/**
 * @fileoverview Common utility functions for the SealMyIdea application
 * @module utils/CommonFunctions
 */

import dayjs from 'dayjs';
import { isAndroid, isIOS } from 'react-device-detect';
import { Endpoints } from '../config/endpoints';
import siteConfig from '../config/site.config';
import { ApiResponse, getApiData } from '../helpers/ApiHelper';
import { store } from '../store';
import { updateUser } from '../store/slices/authSlice';

/**
 * Retrieves the authentication token from localStorage
 * @function getToken
 * @returns {string} The authentication token or empty string if not found
 * @example
 * const token = getToken();
 * if (token) {
 *   // Use token for authenticated requests
 * }
 */
export const getToken = (): string => {
  let token = '';
  if (localStorage.getItem('token')) {
    token = localStorage.getItem('token') || '';
  }
  return token;
};

/**
 * Detects the current platform/device type
 * @function getPlatform
 * @returns {'ios' | 'android' | 'web'} The detected platform
 * @example
 * const platform = getPlatform();
 * if (platform === 'ios') {
 *   // iOS-specific logic
 * } else if (platform === 'android') {
 *   // Android-specific logic
 * } else {
 *   // Web-specific logic
 * }
 */
export const getPlatform = (): 'ios' | 'android' | 'web' => {
  if (isIOS) {
    return 'ios';
  }
  if (isAndroid) {
    return 'android';
  }
  return 'web';
};

/**
 * Calculates optimal chunk size for a file upload based on its total size.
 * Ensures the number of chunks stays within Azure Blob Storage limits while optimizing performance.
 *
 * Azure Blob Constraints:
 * - Max blocks per blob: 50,000
 * - Max block size: 4000 MiB (≈ 3.8 GB)
 * - Max blob size: ~190 TB
 *
 * @param fileSizeInBytes - Total file size in bytes.
 * @returns Recommended chunk size in bytes.
 */
export function getChunkSize(fileSizeInBytes: number): number {
  const MB = 1024 * 1024;
  const GB = 1024 * MB;

  // For files < 100 MB: Use small 2MB chunks for finer control and lower memory usage
  if (fileSizeInBytes < 100 * MB) {
    return 2 * MB;
  }

  // For files 100 MB – 1 GB: Use 10MB chunks for decent upload performance with reliable retries
  if (fileSizeInBytes < 1 * GB) {
    return 10 * MB;
  }

  // For files 1 GB – 10 GB: Use 50MB chunks for balanced performance and lower chunk count
  if (fileSizeInBytes < 10 * GB) {
    return 50 * MB;
  }

  // For files 10 GB – 100 GB: Use 100MB chunks for faster uploads and fewer total chunks
  if (fileSizeInBytes < 100 * GB) {
    return 100 * MB;
  }

  // For files > 100 GB: Use larger 200MB chunks to keep total chunk count low
  return 200 * MB;
}

export type StatusCode = '-1' | '0' | '2' | '3';

export function getStatusText(status: StatusCode): string {
  const statusMap: Record<StatusCode, string> = {
    '-1': 'Cancelled',
    '0': 'Draft',
    '2': 'In Review',
    '3': 'Sealed',
  };

  return statusMap[status];
}

export const handleDownload = (url: string) => {
  fetch(url)
    .then(resp => {
      if (!resp.ok) {
        throw new Error(`HTTP error ${resp.status}`);
      }
      return resp.blob();
    })
    .then(blob => {
      const decodedUrl = decodeURIComponent(url);
      const parts = decodedUrl.split('/');
      const rawFilename = parts[parts.length - 1]; // Get last part
      const filename = rawFilename || 'downloaded-file.zip'; // fallback

      // Create and trigger the download
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(blobUrl);
    })
    .catch(err => {
      console.error('Download failed', err);
    });
};

export function formatBytes(bytes: number): string {
  if (bytes === 0) {
    return '0 Bytes';
  }
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${value} ${sizes[i]}`;
}

/**
 * Format a given value in bytes to a human-readable string with a desired unit, and
 * auto-scale to the most appropriate unit.
 *
 * @param {number} value - Value in bytes
 * @param {string} [unit='Bytes'] - Desired unit to format the value with
 * @returns {string} Formatted string
 * @throws {Error} If the unit is invalid
 */
export function formatStorage(
  value: number,
  unit: 'Bytes' | 'KB' | 'MB' | 'GB' | 'TB' = 'Bytes'
) {
  if (value === 0) {
    return '0 Bytes';
  }

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  // Find index of input unit
  const startIndex = sizes.indexOf(unit);
  if (startIndex === -1) {
    throw new Error(`Invalid unit: ${unit}`);
  }

  // Convert value to bytes first
  const bytes = value * Math.pow(k, startIndex);

  // Auto-scale
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const finalValue = parseFloat((bytes / Math.pow(k, i)).toFixed(2));

  return `${finalValue} ${sizes[i]}`;
}

export async function downloadZip(folderId: string, name: string) {
  try {
    const token = getToken();
    const res = await fetch(`${siteConfig.apiUrl}${Endpoints.downloadFile}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ folder_id: folderId }),
    });

    if (!res.ok) {
      throw new Error('Failed to download file');
    }

    const blob = await res.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = name || 'downloaded-file.zip';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading zip:', error);
  }
}

// utils/downloadFile.js
export async function downloadFile(fileUrl: string, fileName = 'Receipt') {
  try {
    const response = await fetch(fileUrl);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();

    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    console.log(`✅ File "${fileName}" downloaded successfully.`);
    return {
      status: true,
      message: `✅ File "${fileName}" downloaded successfully.`,
    };
  } catch (error) {
    console.error('❌ Download failed:', error);
    return { status: false, message: error?.message };
  }
}

// ✅ Common function to convert bytes to MB (no forced rounding)
export const bytesToMB = (bytes: unknown) => {
  const num = typeof bytes === 'string' ? BigInt(bytes) : BigInt(Number(bytes));
  const MB = 1024 * 1024;

  const mb = Number(num) / Number(MB); // convert to MB as a number
  const str = mb.toString(); // turn into string

  // If it has a decimal part, slice it to 2 digits
  if (str.includes('.')) {
    const [intPart, decPart] = str.split('.');
    return `${intPart}.${decPart.slice(0, 2)}`;
  }
  return Number(str); // no decimal part
  // const num = Number(bytes);
  // return isNaN(num) ? 0 : num / (1024 * 1024);
};

export const getUserData = async () => {
  try {
    const res = await getApiData<
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      {},
      ApiResponse & { data?: { total_storage?: number } }
    >({
      url: Endpoints.userData,
      method: 'GET',
    });
    if (res && res.status === true) {
      store.dispatch(
        updateUser({
          user: res.data,
          isAuthenticated: true,
        })
      );
    }
  } catch (error) {
    console.log('Error fetching user data:', error);
  }
};

export const downloadFromInternalApi = async (
  fileUrl: string,
  fileName = 'Receipt'
) => {
  try {
    const resp = await fetch(
      `/api/download-invoice?url=${encodeURIComponent(fileUrl)}`
    );

    if (!resp.ok) {
      throw new Error(`HTTP error ${resp.status}`);
    }

    const blob = await resp.blob();
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'invoice.pdf'; // you can make this dynamic if needed
    document.body.appendChild(a);
    a.click();
    a.remove();

    window.URL.revokeObjectURL(url);
    return {
      status: true,
      message: `✅ File "${fileName}" downloaded successfully.`,
    };
  } catch (error) {
    console.error('❌ Download failed:', error);
    return { status: false, message: error?.message };
  }
};

/**
 * Format a Unix timestamp to a human-readable date
 * @param {string | number | undefined | null} value - The timestamp to format
 * @returns {string} A human-readable date in the format MMM DD, YYYY hh:mm A
 * @example
 *  > formatUnix(1598059584)
 *  "Aug 27, 2020 10:13 AM"
 */
export function formatUnix(value: string | number | undefined | null) {
  if (!value) {
    return 'No date';
  }

  // Clean spaces/newlines if it's a string
  const num = Number(String(value).trim());

  if (isNaN(num)) {
    return 'Invalid timestamp';
  }

  // Detect if it's in seconds (10 digits) or ms (13 digits)
  const ts = num.toString().length === 10 ? dayjs.unix(num) : dayjs(num);

  return ts.format('MMM DD, YYYY hh:mm A');
}
