'use client';

import { CloseOutlined, DownloadOutlined } from '@ant-design/icons';
import { Button, Card, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './PWAInstallPrompt.module.css';

const { Text } = Typography;

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return;
      }

      // Check for iOS Safari
      if ((window.navigator as { standalone?: boolean }).standalone === true) {
        setIsInstalled(true);
        return;
      }

      // Check if user has already dismissed the prompt recently
      const lastDismissed = localStorage.getItem('pwa-install-dismissed');
      if (lastDismissed) {
        const dismissedTime = parseInt(lastDismissed, 10);
        const now = Date.now();
        // Show again after 24 hours
        if (now - dismissedTime < 24 * 60 * 60 * 1000) {
          return;
        }
      }
    };

    checkIfInstalled();

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);

      // Show install prompt after a shorter delay for better visibility
      setTimeout(() => {
        if (!isInstalled) {
          setShowInstallPrompt(true);
        }
      }, 1000);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener(
        'beforeinstallprompt',
        handleBeforeInstallPrompt
      );
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      return;
    }

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      if (outcome === 'accepted') {
        setIsInstalled(true);
      }

      setShowInstallPrompt(false);
      setDeferredPrompt(null);
    } catch {
      // Handle installation error silently
      setShowInstallPrompt(false);
    }
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    // Don't show again for 24 hours
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  // Don't show if already installed or not ready
  if (isInstalled || !showInstallPrompt) {
    return null;
  }

  return (
    <div className={styles.installPrompt}>
      <Card className={styles.promptCard}>
        <div className={styles.promptContent}>
          <div className={styles.promptIcon}>
            <DownloadOutlined />
          </div>
          <div className={styles.promptText}>
            <Text strong>Install Seal my idea</Text>
            <Text type='secondary' className={styles.promptDescription}>
              Install our app for a better experience with offline access and
              quick launch.
            </Text>
          </div>
          <div className={styles.promptActions}>
            <Button
              type='primary'
              icon={<DownloadOutlined />}
              onClick={handleInstallClick}
              className={styles.installButton}
            >
              Install
            </Button>
            <Button
              type='text'
              icon={<CloseOutlined />}
              onClick={handleDismiss}
              className={styles.dismissButton}
            >
              Not now
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PWAInstallPrompt;
