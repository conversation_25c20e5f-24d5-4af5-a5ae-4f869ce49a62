# Developer Setup Guide

This guide ensures that **any developer** can see linting and formatting errors directly in their IDE, regardless of their VS Code setup.

## 🚀 Quick Setup (5 minutes)

### 1. Install Dependencies

```bash
npm install
```

### 2. Install Recommended VS Code Extensions

When you open this project in VS Code, you'll see a popup asking to install recommended extensions. Click **"Install All"**.

**Manual Installation:**

- ESLint (`dbaeumer.vscode-eslint`)
- Prettier (`esbenp.prettier-vscode`)
- Styled Components (`styled-components.vscode-styled-components`)

### 3. Enable VS Code Settings

The project includes `.vscode/settings.json` that automatically:

- ✅ Shows ESLint errors **in real-time** as you type
- ✅ Formats code on save with Prettier
- ✅ Auto-fixes ESLint issues on save
- ✅ Shows TypeScript errors inline

## 🔍 How to See Errors in Your IDE

### ESLint Errors (Code Quality)

- **Red squiggly lines** under problematic code
- **Problems panel** (Ctrl+Shift+M) shows all issues
- **Status bar** shows error count
- **Hover** over red lines to see error details

### TypeScript Errors

- **Red squiggly lines** under type errors
- **Problems panel** shows type issues
- **Auto-completion** with type hints

### Prettier Formatting

- **Auto-formats** on save (Ctrl+S)
- **Manual format**: Shift+Alt+F
- **Format selection**: Ctrl+K Ctrl+F

## 🛡️ Git Hooks (Automatic Quality Checks)

The project uses **Husky** to run quality checks before commits:

```bash
# This runs automatically before each commit:
npx lint-staged
```

**What it does:**

- ✅ Runs ESLint and auto-fixes issues
- ✅ Formats code with Prettier
- ✅ Only processes staged files (fast!)
- ❌ **Blocks commit** if there are unfixable errors

## 📋 Manual Quality Checks

```bash
# Check everything at once
npm run check-all

# Individual checks
npm run lint              # ESLint check
npm run lint:fix          # ESLint with auto-fix
npm run format            # Format all files
npm run format:check      # Check if files are formatted
npm run type-check        # TypeScript check
npm run test              # Run tests
```

## 🚨 Troubleshooting

### "ESLint is not working"

1. Check VS Code extensions are installed
2. Reload VS Code window (Ctrl+Shift+P → "Reload Window")
3. Check the Output panel (View → Output → ESLint)

### "Prettier is not formatting"

1. Check default formatter: Ctrl+Shift+P → "Format Document With..." → Choose Prettier
2. Check `.prettierrc.json` exists in project root
3. Ensure "Format on Save" is enabled in settings

### "Git hooks not working"

```bash
# Reinstall hooks
npm run prepare
```

### "TypeScript errors not showing"

1. Check TypeScript extension is enabled
2. Open a `.ts` file to activate TypeScript
3. Check status bar for TypeScript version

## 🎯 IDE-Independent Error Detection

Even without VS Code, developers can see errors:

### 1. Terminal Commands

```bash
# See all linting errors
npm run lint

# See TypeScript errors
npm run type-check

# See formatting issues
npm run format:check
```

### 2. Git Hooks

- **Pre-commit hook** prevents bad code from being committed
- Runs automatically - no setup needed
- Works in any Git client or terminal

### 3. CI/CD Integration

The `npm run check-all` command can be used in:

- GitHub Actions
- GitLab CI
- Jenkins
- Any CI/CD system

## 🔧 Customizing Rules

### ESLint Rules

Edit `eslint.config.mjs` to modify linting rules:

```javascript
rules: {
  'no-console': 'warn',  // Change to 'off' to disable
  // Add your custom rules
}
```

### Prettier Rules

Edit `.prettierrc.json` to modify formatting:

```json
{
  "semi": true,
  "singleQuote": true,
  "printWidth": 80
}
```

### TypeScript Rules

Edit `tsconfig.json` for TypeScript settings:

```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true
  }
}
```

## 📚 What's Included

### Code Quality Tools

- **ESLint**: Catches bugs and enforces code style
- **Prettier**: Consistent code formatting
- **TypeScript**: Type safety and better IntelliSense
- **Husky**: Git hooks for quality gates
- **lint-staged**: Fast, staged-file-only checks

### UI Libraries

- **Ant Design**: Professional React components
- **Styled Components**: CSS-in-JS with theme support
- **Redux Toolkit**: State management with persistence

### Testing

- **Jest**: Unit testing framework
- **React Testing Library**: Component testing utilities

## 🎉 Benefits

✅ **Immediate feedback** - See errors as you type  
✅ **Consistent code style** - Automatic formatting  
✅ **Prevent bugs** - Catch issues before runtime  
✅ **Team consistency** - Same rules for everyone  
✅ **Git safety** - Bad code can't be committed  
✅ **IDE agnostic** - Works in terminal too

## 🆘 Need Help?

1. Check this guide first
2. Run `npm run check-all` to see all issues
3. Check the VS Code Output panel for detailed errors
4. Ask team members for help with specific rules

---

**Remember**: The goal is to catch errors **before** they reach production! 🚀
