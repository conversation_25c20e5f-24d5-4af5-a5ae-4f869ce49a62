{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.enable": true, "eslint.run": "onType", "eslint.alwaysShowStatus": true, "typescript.validate.enable": true, "typescript.showUnused": true, "typescript.suggest.includeCompletionsForModuleExports": true, "editor.rulers": [80, 120], "editor.renderWhitespace": "boundary", "editor.trimAutoWhitespace": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "editor.tabSize": 2, "editor.insertSpaces": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "files.associations": {"*.css": "css", "*.scss": "scss"}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/coverage": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/.next": true}}