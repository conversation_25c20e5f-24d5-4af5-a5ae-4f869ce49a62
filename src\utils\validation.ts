/**
 * @fileoverview Form validation utilities for the SealMyIdea application
 * @module utils/validation
 */

/**
 * Result object returned by validation functions
 * @typedef {Object} ValidationResult
 * @property {boolean} isValid - Whether the validation passed
 * @property {string[]} errors - Array of validation error messages
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validates login form data (username and password)
 * @function validateLoginForm
 * @param {string} username - The username to validate
 * @param {string} password - The password to validate
 * @returns {ValidationResult} Object containing validation result and any errors
 * @example
 * const result = validateLoginForm('john_doe', 'password123');
 * if (!result.isValid) {
 *   console.log('Validation errors:', result.errors);
 * }
 */
export const validateLoginForm = (
  username: string,
  password: string
): ValidationResult => {
  const errors: string[] = [];

  if (!username.trim()) {
    errors.push('Username is required');
  } else if (username.length < 3) {
    errors.push('Username must be at least 3 characters long');
  }

  if (!password.trim()) {
    errors.push('Password is required');
  } else if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validates email address format using regex
 * @function validateEmail
 * @param {string} email - The email address to validate
 * @returns {boolean} True if email format is valid, false otherwise
 * @example
 * const isValid = validateEmail('<EMAIL>'); // returns true
 * const isInvalid = validateEmail('invalid-email'); // returns false
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
