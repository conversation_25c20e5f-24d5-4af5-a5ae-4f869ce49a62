{"source": {"include": ["./src/", "./README.md"], "includePattern": "\\.(js|jsx|ts|tsx)$", "exclude": ["./node_modules/", "./src/__tests__/"]}, "opts": {"destination": "./docs/", "recurse": true, "readme": "./README.md"}, "plugins": ["plugins/markdown"], "templates": {"cleverLinks": false, "monospaceLinks": false, "theme": "clean-jsdoc-theme"}, "theme_opts": {"default_theme": "light", "homepageTitle": "SealMyIdea Documentation", "title": "SealMyIdea - Secure Digital Copyright Protection", "favicon": "./public/favicon.png", "logo": "./public/icons/icon-192x192.png", "displayModuleHeader": true, "includeFilesListInHomepage": true, "search": true, "menu": [{"title": "GitHub Repository", "link": "https://github.com/your-repo/sealmyidea", "target": "_blank"}, {"title": "Live Demo", "link": "https://sealmyidea.vercel.app", "target": "_blank"}], "meta": [{"name": "author", "content": "SealMyIdea Team"}, {"name": "description", "content": "Comprehensive documentation for SealMyIdea - A secure digital copyright protection platform"}], "footer": "Copyright © 2025 SealMyIdea. All rights reserved.", "create_style": "./src/styles/jsdoc-custom.css", "overlay_scrollbar": {"options": {"theme": "os-theme-light"}}}}