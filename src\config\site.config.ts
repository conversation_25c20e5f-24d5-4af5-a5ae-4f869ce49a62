const prod = process.env.NODE_ENV === 'production';

// Hide console logs in production mode
if (prod) {
  // console.log = () => {};
}

// const baseUrl = process?.env?.NEXT_PUBLIC_BASE_URL;
const baseUrl = 'http://192.168.0.125:3005';
// const baseUrl = 'https://staging-api.swisstrustlayer.com';

const socketUrl = process?.env?.NEXT_PUBLIC_BASE_URL;

const website = process?.env?.NEXT_PUBLIC_WEBSITE_URL;

const shareWebUrl = process?.env?.NEXT_PUBLIC_WEBSITE_URL;

const siteConfig = {
  prod,
  siteName: 'Seal My Idea Web',
  website,
  apiUrl: baseUrl,
  domainUrl: baseUrl,
  socketUrl: socketUrl,
  shareWebUrl,
  googleAutoCompleteKey: process?.env?.NEXT_PUBLIC_GOOGLE_AUTO_COMPLETE,
  googleClientId: process?.env?.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
  linkedinClientId: process?.env?.NEXT_PUBLIC_LINKEDIN_CLIENT_ID,
  appleClientId: process?.env?.NEXT_PUBLIC_APPLE_CLIENT_ID,
  facebookAppId: process?.env?.NEXT_PUBLIC_FACEBOOK_APP_ID,
};

export default siteConfig;
