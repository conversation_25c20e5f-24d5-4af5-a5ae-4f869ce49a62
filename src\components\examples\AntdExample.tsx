'use client';

import {
  CheckCircleOutlined,
  LockOutlined,
  MailOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  Row,
  Select,
  Space,
  Switch,
  Typography,
  message,
} from 'antd';
import React, { useState } from 'react';
import styled from 'styled-components';

import { useTranslation } from '@/hooks/useTranslation';

const { Title, Text } = Typography;
const { Option } = Select;

// Styled components working alongside Ant Design
const StyledContainer = styled.div`
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
`;

const StyledCard = styled(Card)`
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;

  .ant-card-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;

    .ant-card-head-title {
      color: white;
      font-weight: 600;
    }
  }
`;

const GradientButton = styled(Button)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  height: 45px;
  font-weight: 600;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
  }
`;

interface FormData {
  username: string;
  email: string;
  password: string;
  notifications: boolean;
  birthDate: string | null;
  country: string;
}

const AntdExample: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const onFinish = async (values: FormData): Promise<void> => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Form values:', values);
      message.success('Form submitted successfully!');
      setSubmitted(true);
    } catch {
      message.error('Failed to submit form');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = (): void => {
    form.resetFields();
    setSubmitted(false);
  };

  return (
    <StyledContainer>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <StyledCard title={t('examples.antd_form_title')}>
            {submitted ? (
              <div style={{ textAlign: 'center', padding: '2rem' }}>
                <CheckCircleOutlined
                  style={{
                    fontSize: '48px',
                    color: '#52c41a',
                    marginBottom: '1rem',
                  }}
                />
                <Title level={3}>Form Submitted Successfully!</Title>
                <Text type='secondary'>{t('examples.information_saved')}</Text>
                <br />
                <br />
                <Button type='primary' onClick={resetForm}>
                  {t('examples.submit_another_form')}
                </Button>
              </div>
            ) : (
              <Form
                form={form}
                layout='vertical'
                onFinish={onFinish}
                autoComplete='off'
              >
                <Form.Item
                  label='Username'
                  name='username'
                  rules={[
                    { required: true, message: 'Please input your username!' },
                    {
                      min: 3,
                      message: 'Username must be at least 3 characters',
                    },
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder='Enter your username'
                    size='large'
                  />
                </Form.Item>

                <Form.Item
                  label='Email'
                  name='email'
                  rules={[
                    { required: true, message: 'Please input your email!' },
                    { type: 'email', message: 'Please enter a valid email!' },
                  ]}
                >
                  <Input
                    prefix={<MailOutlined />}
                    placeholder='Enter your email'
                    size='large'
                  />
                </Form.Item>

                <Form.Item
                  label='Password'
                  name='password'
                  rules={[
                    { required: true, message: 'Please input your password!' },
                    {
                      min: 6,
                      message: 'Password must be at least 6 characters',
                    },
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder='Enter your password'
                    size='large'
                  />
                </Form.Item>

                <Form.Item
                  label='Country'
                  name='country'
                  rules={[
                    { required: true, message: 'Please select your country!' },
                  ]}
                >
                  <Select placeholder='Select your country' size='large'>
                    <Option value='us'>United States</Option>
                    <Option value='uk'>United Kingdom</Option>
                    <Option value='ca'>Canada</Option>
                    <Option value='au'>Australia</Option>
                    <Option value='de'>Germany</Option>
                  </Select>
                </Form.Item>

                <Form.Item label='Birth Date' name='birthDate'>
                  <DatePicker
                    style={{ width: '100%' }}
                    size='large'
                    placeholder='Select your birth date'
                  />
                </Form.Item>

                <Form.Item
                  name='notifications'
                  valuePropName='checked'
                  style={{ marginBottom: '2rem' }}
                >
                  <Space>
                    <Switch />
                    <Text>Enable email notifications</Text>
                  </Space>
                </Form.Item>

                <Form.Item>
                  <Space
                    style={{ width: '100%', justifyContent: 'space-between' }}
                  >
                    <Button type='default' onClick={resetForm}>
                      {t('examples.reset')}
                    </Button>
                    <GradientButton
                      type='primary'
                      htmlType='submit'
                      loading={loading}
                      size='large'
                    >
                      {t('examples.submit_form')}
                    </GradientButton>
                  </Space>
                </Form.Item>
              </Form>
            )}
          </StyledCard>
        </Col>

        <Col xs={24} lg={12}>
          <StyledCard title={t('examples.component_showcase')}>
            <Space direction='vertical' style={{ width: '100%' }} size='large'>
              <Alert
                message={t('examples.antd_integration')}
                description={t('examples.antd_integration_description')}
                type='info'
                showIcon
              />

              <Divider>{t('examples.button_variants')}</Divider>

              <Space wrap>
                <Button type='primary'>{t('examples.primary')}</Button>
                <Button type='default'>{t('examples.default')}</Button>
                <Button type='dashed'>{t('examples.dashed')}</Button>
                <Button type='text'>{t('examples.text')}</Button>
                <Button type='link'>{t('examples.link')}</Button>
              </Space>

              <Divider>{t('examples.custom_styled_button')}</Divider>

              <GradientButton type='primary' size='large'>
                {t('examples.custom_gradient_button')}
              </GradientButton>

              <Divider>{t('examples.typography')}</Divider>

              <div>
                <Title level={4}>This is a Title</Title>
                <Text>This is regular text. </Text>
                <Text type='secondary'>This is secondary text. </Text>
                <Text type='success'>This is success text. </Text>
                <Text type='warning'>This is warning text. </Text>
                <Text type='danger'>This is danger text.</Text>
              </div>
            </Space>
          </StyledCard>
        </Col>
      </Row>
    </StyledContainer>
  );
};

export default AntdExample;
