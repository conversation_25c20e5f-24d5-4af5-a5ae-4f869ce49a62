'use client';

import { EditOutlined, UploadOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  Col,
  ColorPicker,
  Radio,
  Row,
  Space,
  Typography,
  Upload,
} from 'antd';
import React, { useState } from 'react';

import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { AggregationColor } from 'antd/es/color-picker/color';
import { UploadChangeParam } from 'antd/es/upload';
import styles from './AppearanceSettings.module.css';

const { Title, Text } = Typography;

/**
 * AppearanceSettings component for managing theme and branding settings
 *
 * @component AppearanceSettings
 * @returns {JSX.Element} Rendered appearance settings component
 *
 * @example
 * <AppearanceSettings />
 *
 * @description
 * This component provides interface for users to:
 * - Switch between dark, light, and system default themes
 * - Customize primary color
 * - Upload agency logo
 * - Configure document styling and watermark settings
 * - All styling uses theme variables for consistency
 */
const AppearanceSettings: React.FC = () => {
  const { t } = useTranslation();
  const { theme, setTheme } = useTheme();
  const [primaryColor, setPrimaryColor] = useState('#012458');
  const [watermarkEnabled, setWatermarkEnabled] = useState(true);

  const handleThemeChange = (value: string) => {
    if (value === 'system') {
      // Handle system default - could implement system preference detection
      const prefersDark = window.matchMedia(
        '(prefers-color-scheme: dark)'
      ).matches;
      setTheme(prefersDark ? 'dark' : 'light');
    } else {
      setTheme(value as 'light' | 'dark');
    }
  };

  const handleColorChange = (color: AggregationColor) => {
    const hexColor = color.toHexString();
    setPrimaryColor(hexColor);
    // TODO: Implement color theme update
  };

  const handleLogoUpload = (info: UploadChangeParam) => {
    // TODO: Implement logo upload functionality
    console.log('Logo upload:', info);
  };

  const handleBrandingUpload = (info: UploadChangeParam) => {
    // TODO: Implement branding upload functionality
    console.log('Branding upload:', info);
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <Title level={1} className={styles.title}>
          {t('appearanceSettings.agencyCustomization')}
        </Title>
        <Text className={styles.subtitle}>
          {t('appearanceSettings.subtitle')}
        </Text>
      </div>

      {/* Appearance Settings Section */}
      <Card className={styles.section}>
        <Title level={2} className={styles.sectionTitle}>
          {t('appearanceSettings.title')}
        </Title>

        <Row gutter={[32, 32]}>
          {/* Primary Color */}
          <Col xs={24} md={12}>
            <div className={styles.settingGroup}>
              <Text className={styles.label}>
                {t('appearanceSettings.primaryColor')}
              </Text>
              <div className={styles.colorPickerContainer}>
                <ColorPicker
                  value={primaryColor}
                  onChange={handleColorChange}
                  showText
                  size='large'
                  className={styles.colorPicker}
                />
                <Button
                  icon={<EditOutlined />}
                  className={styles.editButton}
                  type='text'
                />
              </div>
            </div>
          </Col>

          {/* Theme Style */}
          <Col xs={24} md={12}>
            <div className={styles.settingGroup}>
              <Text className={styles.label}>
                {t('appearanceSettings.themeStyle')}
              </Text>
              <Radio.Group
                value={theme}
                onChange={e => handleThemeChange(e.target.value)}
                className={styles.themeRadioGroup}
              >
                <Space direction='vertical' size='middle'>
                  <Radio value='dark' className={styles.themeRadio}>
                    <span className={styles.radioLabel}>
                      {t('appearanceSettings.themes.dark')}
                    </span>
                  </Radio>
                  <Radio value='light' className={styles.themeRadio}>
                    <span className={styles.radioLabel}>
                      {t('appearanceSettings.themes.light')}
                    </span>
                  </Radio>
                  <Radio value='system' className={styles.themeRadio}>
                    <span className={styles.radioLabel}>
                      {t('appearanceSettings.themes.systemDefault')}
                    </span>
                  </Radio>
                </Space>
              </Radio.Group>
            </div>
          </Col>
        </Row>

        {/* Agency Logo */}
        <div className={styles.settingGroup}>
          <Text className={styles.label}>
            {t('appearanceSettings.agencyLogo')}
          </Text>
          <div className={styles.uploadContainer}>
            <Upload
              name='logo'
              listType='picture-card'
              className={styles.logoUpload}
              showUploadList={false}
              onChange={handleLogoUpload}
            >
              <div className={styles.uploadContent}>
                <UploadOutlined className={styles.uploadIcon} />
                <Text className={styles.uploadText}>
                  {t('appearanceSettings.uploadLogo')}
                </Text>
              </div>
            </Upload>
          </div>
        </div>
      </Card>

      {/* Document Styling Section */}
      <Card className={styles.section}>
        <Title level={2} className={styles.sectionTitle}>
          {t('appearanceSettings.documentStyling')}
        </Title>

        <div className={styles.settingGroup}>
          <Text className={styles.label}>
            {t('appearanceSettings.pdfSealBranding')}
          </Text>
          <div className={styles.uploadContainer}>
            <Upload
              name='branding'
              listType='picture-card'
              className={styles.brandingUpload}
              showUploadList={false}
              onChange={handleBrandingUpload}
            >
              <div className={styles.uploadContent}>
                <UploadOutlined className={styles.uploadIcon} />
                <Text className={styles.uploadText}>
                  {t('appearanceSettings.uploadCustomBranding')}
                </Text>
              </div>
            </Upload>
          </div>
        </div>
      </Card>

      {/* Watermark Settings Section */}
      <Card className={styles.section}>
        <Title level={2} className={styles.sectionTitle}>
          {t('appearanceSettings.watermarkSettings')}
        </Title>

        <div className={styles.settingGroup}>
          <Checkbox
            checked={watermarkEnabled}
            onChange={e => setWatermarkEnabled(e.target.checked)}
            className={styles.watermarkCheckbox}
          >
            <span className={styles.checkboxLabel}>
              {t('appearanceSettings.addWatermark')}
            </span>
          </Checkbox>
          <Text className={styles.description}>
            {t('appearanceSettings.watermarkDescription')}
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default AppearanceSettings;
