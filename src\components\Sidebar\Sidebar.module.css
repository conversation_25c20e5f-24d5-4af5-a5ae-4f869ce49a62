/* components/Sidebar.module.css */
.sidebar {
  width: 280px;
  height: 100%;
  background-color: #9bdfff; /* Light theme default */
  padding: 1rem;
  display: none;
  transition: background-color 0.3s ease;
  border-right: 1px solid var(--color-border);
}

/* Dark theme sidebar */
html[data-theme='dark'] .sidebar {
  background-color: var(--color-secondary);
}

.sidebarContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.logo {
  height: 85px !important;
  /* border-bottom: 1px solid #fff; */
  text-align: center;
  margin-bottom: 0.7rem;
}

.menu {
  list-style: none;
  padding: 0;
  margin: 0;
  cursor: pointer !important;
}

.menuItem {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  gap: 0.75rem;
  margin: 0.6rem 0;
}

.logoutTitle {
  font-size: 18px !important;
  font-weight: 500 !important;
  font-family: var(--font-radio-canada) !important;
}

html[data-theme='dark'] .menuItemLabel {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-white);
  /* font-family: radio-canada-big; */
}
html[data-theme='light'] .menuItemLabel {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-primary);
  /* font-family: radio-canada-big; */
}

html[data-theme='dark'] .menuItemLabel:hover {
  font-size: 14px;
  color: var(--color-primary);
}
html[data-theme='light'] .menuItemLabel:hover {
  font-size: 14px;
  color: var(--color-white);
}

html[data-theme='light'] .menuItem {
  color: var(--color-primary);
}

html[data-theme='dark'] .menuItem {
  color: var(--color-white);
}

html[data-theme='dark'] .menuItem:hover {
  background-color: var(--color-secondary);
  color: var(--color-primary);
}
html[data-theme='light'] .menuItem:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
}

html[data-theme='dark'] .menuItem:hover .menuItemLabel {
  color: var(--color-primary);
}
html[data-theme='light'] .menuItem:hover .menuItemLabel {
  color: var(--color-white);
}

html[data-theme='light'] .active {
  background-color: var(--color-primary);
  color: var(--color-white);
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
}
html[data-theme='dark'] .active {
  background-color: var(--color-secondary);
  color: var(--color-primary);
}

html[data-theme='dark'] .active span {
  color: var(--color-primary);
}
html[data-theme='light'] .active span {
  color: var(--color-white);
}

/* Safe New Idea special styling */
.menuItem.safe-new-idea-item {
  background-color: var(--color-success);
  color: var(--color-white) !important;
  margin: 0.5rem 0;
  border-radius: 8px;
}

.menuItem.safe-new-idea-item:hover {
  background-color: var(--color-success-hover);
}

.menuItem.safe-new-idea-item span {
  color: var(--color-white);
  font-weight: 600;
}

.logoutContainer {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.logout {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #000;
  cursor: pointer !important;
  padding: 0.75rem;
  width: 100%;
}

.logout:hover .logoutText {
  color: var(--color-white) !important;
}

.logout:hover .logoutIcon {
  color: var(--color-white) !important;
}

.logout:hover {
  background-color: var(--color-primary);
  border-radius: 8px;
}

.logoutText {
  font-size: 15px !important;
  font-weight: 500 !important;
  color: var(--color-primary) !important;
  font-family: var(--font-poppins);
}

:global(html[data-theme='dark']) .logoutText {
  color: var(--color-white) !important;
}
:global(html[data-theme='dark']) .logoutIcon {
  color: var(--color-white) !important;
}

:global(html[data-theme='dark']) .logout:hover {
  background-color: #8edafe;
  border-radius: 8px;
}

:global(html[data-theme='dark']) .logout:hover .logoutText,
:global(html[data-theme='dark']) .logout:hover .logoutIcon {
  color: var(--color-primary) !important;
}

.logoutIcon {
  font-size: 18px !important;
  color: #012458 !important;
}

.logout span {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-primary);
}
html[data-theme='dark'] .logout span {
  color: var(--color-white);
}

html[data-theme='dark'] .logout {
  color: var(--color-white);
}

.drawer {
  background-color: #9bdfff;
}

/* Dark theme drawer */
html[data-theme='dark'] .drawer {
  background-color: var(--color-secondary);
}

.mobileToggle {
  display: none;
  position: fixed;
  top: 0.6rem;
  left: 0.6rem;
  font-size: 20px;
  background: #002a59;
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 6px;
  z-index: 1001;
  cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
  .mobileToggle {
    display: block;
  }
}

@media (min-width: 769px) {
  .sidebar {
    display: block;
  }
}
