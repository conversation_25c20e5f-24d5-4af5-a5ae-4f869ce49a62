'use client';

import { But<PERSON>, Checkbox, Divider, Typography, message } from 'antd';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { IoDocumentLockSharp } from 'react-icons/io5';
import { RiDeleteBinFill } from 'react-icons/ri';
import { TfiDownload } from 'react-icons/tfi';
import { Endpoints } from '../../config/endpoints';
import { ApiResponse, getApiData } from '../../helpers/ApiHelper';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useTranslation } from '../../hooks/useTranslation';
import { downloadZip } from '../../utils/CommonFunctions';
import VerificationModal from '../modals/VerificationModal';
import { useNotification } from '../Notification/NotificationContext';
import styles from './DeleteIdea.module.css';

const { Title, Text } = Typography;

interface FileData {
  _id: string;
  file_url: string;
  file_name: string;
  file_type: string;
  file_size: string;
  status: string;
}

interface FolderFormData {
  name: string;
  mobile_number: string;
  color: string;
  description: string;
  country_code?: string;
  id?: string;
  files?: FileData[];
  status?: string;
  zip_url?: string | undefined;
  _id?: string;
}

const DeleteIdeaScreen = () => {
  const { t } = useTranslation();
  const router = useRouter();

  const params = useParams();
  const deleteId = params.id as string;

  const [folderData, setFolderData] = useState<FolderFormData | null>(null);
  const [downloading, setDownloading] = useState(false);
  const [deleteLoader, setDeleteLoader] = useState(false);
  const [sealDownload, setSealDownload] = useState(false);

  const [isDelete, setIsDelete] = useState(false);

  const [openVerificationModal, setOpenVerificationModal] = useState(false);

  const notification = useNotification();

  useEffect(() => {
    if (deleteId) {
      getFolderData(deleteId);
    }
  }, [deleteId]);

  const downloadSeal = () => {
    console.log('downloadSeal');
    setSealDownload(true);
    setTimeout(() => {
      setSealDownload(false);
    }, 2000);
  };

  const [checked, setChecked] = useState<boolean>(false);

  const handleDelete = async () => {
    setDeleteLoader(true);
    try {
      const deleteData = {
        _id: deleteId,
      };

      const res = await getApiData<{ _id: string }, ApiResponse>({
        url: Endpoints.deleteFolder,
        method: 'POST',
        data: deleteData,
      });

      if (res && res.status === true) {
        notification.success({
          message: res?.message || 'Folder deleted successfully',
        });
        router.push('/my-ideas');
        setIsDelete(false);
      } else {
        notification.error({
          message: res?.message || 'Error deleting folder',
        });
        setDeleteLoader(false);
        setIsDelete(false);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'An unknown error occurred';
      notification.error({
        message: errorMessage || 'Error deleting folder',
      });
      console.error('Error deleting folder:', error);
      setDeleteLoader(false);
      setIsDelete(false);
    }
  };

  const handleCancel = () => {
    router.push('/my-ideas');
  };

  const xs = useMediaQuery('(max-width: 600px)');

  const getFolderData = async (id: string) => {
    try {
      const res = await getApiData<FolderFormData, ApiResponse>({
        url: `${Endpoints.getFolderById}/${id}`,
        method: 'GET',
      });
      if (res && res.status === true && res.data) {
        setFolderData(res.data);
      } else {
        console.log(res?.message);
        message.error('Failed to load folder data');
      }
    } catch (error) {
      console.error('Error fetching folder data:', error);
      message.error('Error fetching folder data');
    }
  };

  return (
    <div className={styles.container}>
      <Title level={1} className={styles.pageTitle}>
        {folderData?.name}
      </Title>

      <div className={styles.uploadParent}>
        <div className={styles.uploadContainer}>
          <div className={styles.iconBox}>
            <TfiDownload className={styles.icon} />
          </div>
          <div className={styles.label}>{t('deleteIdea.download_ideas')}</div>
          <Button
            type='link'
            onClick={() => setOpenVerificationModal(true)}
            loading={downloading}
            disabled={!folderData?.files || folderData.files.length === 0}
            className={styles.downloadButton}
          >
            {folderData?.files &&
              folderData.files.length > 0 &&
              (downloading ? 'Downloading...' : t('deleteIdea.download'))}
          </Button>
        </div>
        {folderData?.status === '3' && (
          <div className={styles.sealContainer}>
            <div className={styles.sealIconBox}>
              <IoDocumentLockSharp className={styles.icon} />
            </div>
            <div className={styles.label}>{t('deleteIdea.seal_receipt')}</div>
            <Button
              type='link'
              onClick={downloadSeal}
              loading={sealDownload}
              disabled={!folderData?.files || folderData.files.length === 0}
              className={styles.sealDownloadButton}
            >
              {sealDownload ? 'Downloading...' : t('deleteIdea.download')}
            </Button>
          </div>
        )}
      </div>

      <Divider
        style={{
          margin: xs ? '20px 0px' : '40px 0px',
        }}
      />
      <div className={styles.contentContainer}>
        <div className={styles.deleteContainer}>
          <RiDeleteBinFill className={styles.deleteIcon} />
        </div>
        <div className={styles.deleteTextContainer}>
          <Text className={styles.deleteText}>
            {t('deleteIdea.deleteText1')}
          </Text>
        </div>
        <div className={styles.deleteTextContainer}>
          <Checkbox
            checked={checked}
            onChange={e => setChecked(e.target.checked)}
            className={styles.checkbox}
          />
          <Text
            className={styles.deleteText}
            onClick={() => setChecked(!checked)}
            style={{ cursor: 'pointer' }}
          >
            {t('deleteIdea.deleteText2')}
          </Text>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          gap: '40px',
          marginTop: '30px',
        }}
      >
        <Button type='primary' style={{ width: '70%' }} onClick={handleCancel}>
          {t('deleteIdea.cancel')}
        </Button>
        <Button
          type='default'
          style={{ width: '70%', backgroundColor: '#C70000', color: '#fff' }}
          onClick={() => {
            setIsDelete(true);
            setOpenVerificationModal(true);
          }}
          disabled={!checked}
          loading={deleteLoader}
        >
          {t('deleteIdea.delete')}
        </Button>
      </div>
      <VerificationModal
        visible={openVerificationModal}
        onClose={() => {
          setOpenVerificationModal(false);
          setIsDelete(false);
        }}
        onSuccess={async () => {
          if (isDelete) {
            handleDelete();
          } else {
            if (folderData?._id && folderData?.name) {
              setDownloading(true);
              try {
                await downloadZip(
                  folderData?._id || '',
                  folderData?.name || ''
                );
              } catch (error) {
                console.error('Download failed:', error);
              } finally {
                setDownloading(false);
              }
            }
          }
        }}
      />
    </div>
  );
};

export default DeleteIdeaScreen;
