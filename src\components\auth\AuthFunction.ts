import { Endpoints } from '@/config/endpoints';
import { getApiData } from '@/helpers/ApiHelper';
import { User } from '@/store/slices/authSlice';
import { notification } from 'antd';
import type { NotificationInstance } from 'antd/es/notification/interface';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';
export interface LoginResponse {
  status?: boolean;
  message?: string;
  data?: User;
  token?: string;
  OTP?: string;
}

interface ValuesInstance {
  token: string;
  type: string;
  role: string;
  full_name_obj?: object;
}

export const handleSocialLogin = async (values: ValuesInstance) => {
  // const dispatch = store.dispatch;
  // dispatch(loginStart());
  try {
    const response = await getApiData<typeof values, LoginResponse>({
      url: Endpoints.socialLogin,
      method: 'POST',
      data:
        values.type === 'apple'
          ? {
              token: values.token,
              type: values.type,
              role: values.role,
              full_name_obj: values.full_name_obj,
            }
          : {
              token: values.token,
              type: values.type,
              role: values.role,
            },
    });

    if (response && response.status === true && response.data) {
      // dispatch(loginSuccess(response.data));
      if (response.token) {
        localStorage.setItem('token', response?.token);
      }
      return response;
    } else {
      notification.error({
        message: 'Login/Signup Failed',
        description: response?.message || 'Please try again later.',
      });
      // dispatch(loginFailure(response?.message || 'Invalid credentials'));
    }
  } catch (e) {
    notification.error({
      message: 'Login Failed',
      description: e?.message || 'Please try again later.',
    });
    // dispatch(loginFailure(e?.message || 'Login failed. Please try again.'));
  }
};

export const handleRoute = (
  response: LoginResponse,
  router: AppRouterInstance,
  from?: string,
  notification?: NotificationInstance
) => {
  const data = response?.data || {};
  console.log('data', data?.is2FAEnabled === '1', data?.is2FAEnabled);

  if (data?.isEmailVerified === true) {
    if (data?.is2FAEnabled === '-1') {
      router.replace(
        `/two-factor-authentication?email=${encodeURIComponent(
          data?.email || ''
        )}`
      );
    } else if (String(data?.is2FAEnabled) === '1' && from !== '2fa') {
      router.replace(
        `/verify-otp?email=${encodeURIComponent(data?.email || '')}&from=2FA`
      );
    } else if (!data?.last_screen) {
      router.replace('/steps?step=0');
    } else if (data?.last_screen === 'screen_1_tell_about_us') {
      router.replace('/steps?step=1');
    } else if (data?.last_screen === 'screen_2_goals') {
      router.replace('/steps?step=2');
    } else if (data?.last_screen === 'screen_3_interest') {
      router.replace('/steps?step=3');
    } else if (data?.last_screen === 'screen_4_here_about_us') {
      router.replace('/terms');
    } else if (
      data?.last_screen === 'completed' ||
      data?.is2FAEnabled === '0'
    ) {
      router.replace('/dashboard');
    }
  } else {
    if (notification) {
      notification.success({
        message: 'OTP Sent',
        description: `Your OTP is ${response?.OTP}`,
      });
    }
    router.push(
      `/verify-otp?email=${encodeURIComponent(data?.email || '')}&from=login`
    );
  }
};
