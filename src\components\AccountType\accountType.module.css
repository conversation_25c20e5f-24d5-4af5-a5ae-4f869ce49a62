.container {
  min-height: 90vh;
  background-color: var(--color-background);
  padding: 0;
  margin: 0;
  align-items: center;
  justify-content: center;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
}
.sectionHeader {
  display: flex;
  justify-content: space-between;
}

.sectionTitle {
  text-align: center;
  font-weight: 700;
  margin-bottom: 32px !important;
  color: #000;
  font-size: 24px;
}
.foldersSpinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.loadingText {
  font-size: 16px;
  color: var(--color-text-primary);
  margin-top: 16px;
  padding-left: 10px;
}

.header {
  padding: 20px 0px;
}

.backButton {
  padding: 0;
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
}

.backButton:hover {
  color: var(--color-text-primary);
}

.content {
  max-width: 70vw;
  margin: 0 auto;
  padding: 0 24px 24px;
}

.title {
  text-align: center;
  font-weight: 700;
  margin: 0 0 8px 0 !important;
  color: #000;
  font-size: 28px;
}

.subtitle {
  text-align: center;
  display: block;
  margin-bottom: 32px;
  font-size: 16px;
  line-height: 1.5;
  color: var(--color-text-primary);
}

.accountInfoCard {
  background-color: var(--color-card);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.infoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 16px;
  border-bottom: 1px solid #b5bcc2;
}

.infoRow:last-child {
  border-bottom: none;
}

.infoRow .ant-typography {
  margin: 0;
}

.mainSection {
  align-items: stretch;
}

.usageSection {
  padding: 8px;
  height: 100%;
  max-width: 190px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 14px;
  background-color: var(--color-card);
  border-radius: 12px;
}

.usageLabel {
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 600;
  display: block;
  margin-bottom: 8px;
  text-align: center;
}

.usageProgress {
  background-color: rgb(192 193 194);
  border-radius: 6px;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
  margin-top: 8px;
  width: 60px;
  align-self: center;
}

.percentageText {
  position: absolute;
  top: 6px;
  font-size: 14px;
  font-weight: bold;
  color: #000;
  z-index: 2;
}

.fill {
  background-color: #22c55e;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 3px;
  border-radius: 0 0 6px 6px;
}

.usedText {
  color: #fff;
  font-weight: bold;
  font-size: 9px;
}

.plansSection {
  height: 100%;
  background: transparent;
  border: none;
  border-radius: 0;
}

.planCard {
  border-radius: 8px;
  background: var(--color-card) !important;
  text-align: center;
  height: 100%;
  /* border: 1px solid #e5e7eb; */
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.planCard:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px #22c55e;
  /* border-color: #22c55e !important; */
}

.highlightCard {
  background-color: #bafbbc !important;
  /* border-color: #22c55e !important; */
}

:global(html[data-theme='dark']) .highlightCard {
  background-color: #bafbbc !important;
}
:global(html[data-theme='light']) .highlightCard {
  background-color: #bafbbc !important;
}
.planTitleHightlight,
.priceHightlight {
  color: #000 !important;
}

.planTitle {
  font-weight: 600;
  font-size: 16px;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 6px;
}

.price {
  font-size: 24px;
  font-weight: 700;
  margin: 6px 0 3px 0 !important;
  color: #000;
}

.perMonth {
  font-size: 12px;
  font-weight: 400;
  color: var(--color-text-primary);
}
.perMonthHightlighted,
.planDescriptionHightlighted {
  color: #000 !important;
}

.planDescription {
  font-size: 12px;
  color: var(--color-text-primary);
  display: block;
  margin-top: 6px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .content {
    padding: 0 20px 20px;
  }

  .usageSection {
    max-width: 130px;
  }

  .planTitle {
    font-size: 14px;
  }

  .price {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 16px 20px 0;
  }

  .content {
    padding: 0 16px 16px;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 14px;
    margin-bottom: 24px;
  }

  .accountInfoCard {
    padding: 20px;
    margin-bottom: 32px;
  }

  .infoRow {
    font-size: 14px;
    padding: 6px 0;
  }

  .sectionTitle {
    font-size: 20px;
    margin-bottom: 24px !important;
  }

  .usageSection {
    padding: 6px;
    max-width: 120px;
    margin-bottom: 24px;
    height: 150px;
  }

  .percentageText {
    font-size: 12px;
  }

  .planCard {
    margin-bottom: 16px;
  }

  .planTitle {
    font-size: 14px;
  }

  .price {
    font-size: 18px;
  }

  .planDescription {
    font-size: 11px;
  }
}

@media (max-width: 576px) {
  .header {
    padding: 12px 16px 0;
  }

  .content {
    padding: 0 12px 12px;
  }

  .title {
    font-size: 20px;
  }

  .subtitle {
    font-size: 13px;
    margin-bottom: 20px;
  }

  .accountInfoCard {
    padding: 16px;
    margin-bottom: 24px;
  }

  .infoRow {
    font-size: 13px;
    padding: 4px 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .sectionTitle {
    font-size: 18px;
    margin-bottom: 20px !important;
  }

  .usageSection {
    padding: 5px;
    max-width: 100px;
    height: 150px;
  }

  .percentageText {
    font-size: 11px;
  }

  .usedText {
    font-size: 8px;
  }

  .planTitle {
    font-size: 12px;
  }

  .price {
    font-size: 16px;
  }

  .perMonth {
    font-size: 10px;
  }

  .planDescription {
    font-size: 10px;
  }
  .content {
    max-width: 100vw;
  }
}

@media (max-width: 480px) {
  .usageSection {
    max-width: 90px;
    padding: 4px;
    height: 150px;
  }

  .percentageText {
    font-size: 10px;
  }

  .planCard {
    padding: 10px !important;
  }

  .planTitle {
    font-size: 11px;
  }

  .price {
    font-size: 14px;
  }

  .planDescription {
    font-size: 9px;
  }
  .backButton {
    padding: 20px 0px;
  }
}
