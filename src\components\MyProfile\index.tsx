'use client';
import { useNotification } from '@/components/Notification/NotificationContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import {
  ArrowLeftOutlined,
  EditOutlined,
  InfoCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Tooltip,
  Typography,
  Upload,
} from 'antd';
import { UploadChangeParam, UploadFile } from 'antd/es/upload';
import dayjs from 'dayjs';
import { PhoneNumberUtil } from 'google-libphonenumber';
import { isEmpty } from 'lodash-es';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import { Endpoints } from '../../config/endpoints';
import siteConfig from '../../config/site.config';
import { getApiData, uploadFile } from '../../helpers/ApiHelper';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { updateUser } from '../../store/slices/authSlice';
import { getToken } from '../../utils/CommonFunctions';
import { authStyles } from '../auth/AuthWrapper';
import VerificationModal from '../modals/VerificationModal';
import styles from './profile.module.css';
const { Title } = Typography;

export default function MyProfile() {
  const [form] = Form.useForm();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(s => s.auth);

  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();
  const [avatarUrl, setAvatarUrl] = useState(user?.profile_pic || ''); // default avatar
  const [phoneData, setPhoneData] = useState({
    phone: '',
    country: '',
    countryCode: '',
  });
  const router = useRouter();
  const notification = useNotification();

  const [openVerificationModal, setOpenVerificationModal] = useState(false);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [isSocialLogin, setIsSocialLogin] = useState(false);
  //Change avatar
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleAvatarChange = (info: UploadChangeParam<UploadFile>) => {
    const file = info.file.originFileObj as File;
    if (file) {
      setSelectedFile(file);
      const newUrl = URL.createObjectURL(file);
      setAvatarUrl(newUrl); // For preview only
    }
  };

  // Load saved credentials on component mount
  useEffect(() => {
    try {
      if (!user) {
        return;
      }
      if (user?.country_code) {
        const phoneUtil = PhoneNumberUtil.getInstance();
        const cleanedCode = user.country_code?.replace('+', '');
        if (cleanedCode) {
          const regionCode = phoneUtil.getRegionCodeForCountryCode(
            Number(cleanedCode)
          );
          const fullPhone = `${user.country_code}${user.mobile_number}`;
          const obj = {
            phone: fullPhone,
            country: regionCode.toLowerCase(),
            countryCode: user?.country_code || '',
          };
          setPhoneData(obj);
          if (!isEmpty(user?.birth_date)) {
            form.setFieldsValue({
              name: user?.fullname,
              email: user?.email,
              mobile_number: fullPhone,
              birthday: dayjs(user?.birth_date, 'DD/MM/YYYY'),
            });
          } else {
            form.setFieldsValue({
              name: user?.fullname,
              email: user?.email,
              mobile_number: fullPhone,
            });
          }

          setIsSocialLogin(!user.social_platform ? false : true);
          setTimeout(() => {
            setIsFormChanged(false);
          }, 500);
        }
      } else {
        form.setFieldsValue({
          name: user?.fullname,
          email: user?.email,
          birthday: dayjs(user?.birth_date, 'DD/MM/YYYY'),
        });
        setIsSocialLogin(!user.social_platform ? false : true);
        setTimeout(() => {
          setIsFormChanged(false);
        }, 500);
      }

      setAvatarUrl(user?.profile_pic || '');
    } catch (error) {
      console.log('🚀 ~ MyProfile ~ error:', error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form]);

  interface changePasswordResponse {
    status: boolean;
    message: string;
    data: object;
  }

  const handleSubmit = async (values: {
    name: string;
    email: string;
    birthday: string;
  }) => {
    let formattedBirthDate = values.birthday;
    if (values?.birthday) {
      const date = new Date(values.birthday);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = String(date.getFullYear());
      formattedBirthDate = `${day}/${month}/${year}`;
    }

    if (!selectedFile) {
      updateProfile(values);
      return;
    }

    setLoading(true);
    const token = getToken();
    uploadFile({
      url: `${siteConfig.apiUrl}${Endpoints.updateProfile}`,
      file: selectedFile,
      fieldName: 'profile_pic',
      headers: {
        Authorization: `Bearer ${token}`, // ✅ fix: send token here
      },
      additionalData: {
        fullname: values.name,
        email: values.email,
        country_code: phoneData?.countryCode,
        mobile_number: phoneData?.phone,
        birth_date: formattedBirthDate,
      },
      onProgress: percent => {
        console.log('Upload progress:', percent);
      },
      onSuccess: response => {
        if (response && response.status === true && response.data) {
          if (response.data) {
            dispatch(
              updateUser({ user: response.data, isAuthenticated: true })
            );
          }
          notification.success({
            message: t('myProfile.profile_updated_success'),
            description: t('myProfile.profile_updated_description'),
          });
          router.replace('/settings');
        } else {
          notification.error({
            message: t('myProfile.profile_update_failed'),
            description:
              response?.message ||
              t('myProfile.profile_update_failed_description'),
          });
        }
        setLoading(false);
      },
      onError: error => {
        notification.error({
          message: t('myProfile.profile_update_failed'),
          description:
            typeof error === 'object' && error !== null
              ? error.message
              : t('myProfile.network_error'),
        });
        setLoading(false);
      },
    });
  };

  const updateProfile = async (values: {
    name: string;
    email: string;
    birthday: string;
  }) => {
    let formattedBirthDate = values.birthday;
    if (values?.birthday) {
      const date = new Date(values.birthday);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = String(date.getFullYear());
      formattedBirthDate = `${day}/${month}/${year}`;
    }
    setLoading(true);
    try {
      setLoading(true);
      const response = await getApiData<
        {
          fullname: string;
          email: string;
          country_code: string;
          mobile_number: string;
          birth_date: string;
        },
        changePasswordResponse
      >({
        url: `${siteConfig.apiUrl}${Endpoints.updateProfile}`,
        method: 'POST',
        data: {
          fullname: values.name,
          email: values.email,
          country_code: phoneData?.countryCode,
          mobile_number: phoneData?.phone,
          birth_date: formattedBirthDate,
        },
        customUrl: true,
        headers: {},
        formData: true,
      });
      if (response && response.status === true) {
        if (response.data) {
          dispatch(updateUser({ user: response.data, isAuthenticated: true }));
        }
        notification.success({
          message: t('myProfile.profile_updated_success'),
          description: t('myProfile.profile_updated_description'),
        });
        router.replace('/settings');
        setLoading(false);
      } else {
        notification.error({
          message: t('myProfile.profile_update_failed'),
          description:
            response?.message ||
            t('myProfile.profile_update_failed_description'),
        });
        setLoading(false);
      }
    } catch (error) {
      notification.error({
        message: t('myProfile.profile_update_failed'),
        description: error?.message || t('myProfile.network_error'),
      });
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div style={{ maxWidth: 800, width: '100%' }}>
        <div className={styles.header}>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.replace('/settings');
            }}
          >
            {t('common.back')}
          </Button>
        </div>
        <Title level={2} className={styles.title}>
          {t('myProfile.title')}
        </Title>

        {/* Avatar with Edit Icon */}
        <div
          style={{
            textAlign: 'center',
            marginBottom: 32,
            position: 'relative',
          }}
        >
          <Avatar
            size={120}
            src={avatarUrl}
            icon={!avatarUrl ? <UserOutlined /> : <></>}
          />

          <Upload
            showUploadList={false}
            onChange={handleAvatarChange}
            accept='image/*'
            beforeUpload={file => {
              const isImage =
                file.type === 'image/jpeg' ||
                file.type === 'image/png' ||
                file.type === 'image/webp';
              if (!isImage) {
                notification.error({
                  message: t('myProfile.profile_update_failed'),
                  description: t('myProfile.valid_format'),
                });
              }
              return isImage || Upload.LIST_IGNORE;
            }}
          >
            <Button
              shape='circle'
              icon={<EditOutlined />}
              style={{
                position: 'absolute',
                bottom: 0,
                right: 'calc(50% - 50px)', // center align the edit button over image
                transform: 'translateX(50%)',
                background: '#1890ff',
                color: '#fff',
                border: 'none',
              }}
            />
          </Upload>
        </div>

        {/* Profile Form */}
        <Form
          layout='vertical'
          form={form}
          onFinish={values => {
            if (isFormChanged) {
              setOpenVerificationModal(true);
            } else {
              handleSubmit(values);
            }
          }}
          onValuesChange={() => {
            setIsFormChanged(true);
          }}
        >
          <Row gutter={16}>
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                label={t('myProfile.name_label')}
                name='name'
                className={authStyles.formItem}
                rules={[{ required: true }]}
              >
                <Input
                  placeholder={t('myProfile.name_placeholder')}
                  className={authStyles.input}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                label={
                  <span>
                    {t('myProfile.email_label')}
                    {isSocialLogin && (
                      <Tooltip
                        title={t('myProfile.not_allowed_to_change_email')}
                      >
                        <InfoCircleOutlined
                          style={{
                            marginLeft: 6,
                            color: '#faad14',
                            cursor: 'pointer',
                          }}
                        />
                      </Tooltip>
                    )}
                  </span>
                }
                name='email'
                className={authStyles.formItem}
                rules={[{ required: true }]}
              >
                <Input
                  type='email'
                  placeholder={t('myProfile.email_placeholder')}
                  className={authStyles.input}
                  disabled={isSocialLogin}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                label={t('signup.mobile_number')}
                name='mobile_number'
                required={false}
                rules={[
                  {
                    required: true,
                    message: t('signup.required_mobile_number'),
                  },
                  {
                    validator: (_: unknown, value: string) => {
                      // Don't validate if value is empty or undefined
                      if (!value || value.trim() === '') {
                        return Promise.resolve();
                      }
                      try {
                        const phoneUtil = PhoneNumberUtil.getInstance();
                        // Use the full phone number with country code for validation
                        const parsed = phoneUtil.parseAndKeepRawInput(value);
                        if (phoneUtil.isValidNumber(parsed)) {
                          return Promise.resolve();
                        }
                      } catch (e) {
                        console.log('🚀 ~ e:', e);
                        // ignore parse errors, don't show error for invalid format during typing
                        return Promise.resolve();
                      }
                      return Promise.reject(t('myProfile.enter_valid_phone'));
                    },
                  },
                ]}
                className={authStyles.formItem}
              >
                <PhoneInput
                  defaultCountry={phoneData.country || 'us'}
                  value={phoneData.phone}
                  onChange={(phone, meta) => {
                    // Get country calling code
                    const phoneUtil = PhoneNumberUtil.getInstance();
                    try {
                      const parsed = phoneUtil.parseAndKeepRawInput(phone);
                      const countryCode = parsed.getCountryCode();
                      const nationalNumber =
                        parsed?.getNationalNumber()?.toString() ?? '';

                      setPhoneData({
                        phone: nationalNumber,
                        country: String(meta.country),
                        countryCode: `+${countryCode}`,
                      });
                      form.setFieldValue('mobile_number', phone);
                    } catch (e) {
                      console.log('🚀 ~ e:', e);
                      // Fallback: manually extract
                      const countryCallingCode =
                        phone.match(/^\+(\d{1,3})/)?.[1];
                      const nationalNumber = phone.replace(/^\+\d{1,3}/, '');

                      setPhoneData({
                        phone: nationalNumber,
                        country: String(meta.country),
                        countryCode: `+${countryCallingCode}`,
                      });
                      form.setFieldValue('mobile_number', phone);
                    }
                  }}
                  placeholder={t('signup.mobile_number_placeholder')}
                  className={authStyles.input}
                  disableDialCodeAndPrefix={true}
                  inputStyle={{
                    width: '100%',
                    height: isMobile ? 42 : 50,
                    borderTopRightRadius: '10px',
                    borderBottomRightRadius: '10px',
                    border: 'none',
                    padding: '0 12px',
                    background: theme === 'dark' ? '#06164a' : '#F5F5F5',
                    color: theme === 'dark' ? '#fff' : '#000',
                    fontSize: isMobile ? '14px' : '16px',
                    fontFamily: 'var(--font-poppins)',
                    transition: 'all 0.2s ease',
                  }}
                  countrySelectorStyleProps={{
                    buttonStyle: {
                      height: '100%',
                      borderTopLeftRadius: '10px',
                      borderBottomLeftRadius: '10px',
                      border: 'none',
                      borderRight: 'none',
                      background: theme === 'dark' ? '#06164a' : '#F5F5F5',
                      color: theme === 'dark' ? '#fff' : '#000',
                      transition: 'all 0.2s ease',
                      paddingLeft: '10px',
                    },
                  }}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={24} md={12}>
              <Form.Item
                name='birthday'
                label={t('tell_about.birthday')}
                rules={[{ required: true }]}
                className={authStyles.formItem}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  format='DD/MM/YYYY'
                  inputReadOnly
                  placeholder={t('tell_about.enter_birthday')}
                  disabledDate={current =>
                    current && current > dayjs().endOf('day')
                  }
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ textAlign: 'center', marginTop: 24 }}>
            <Button
              type='primary'
              htmlType='submit'
              loading={loading}
              className={authStyles.primaryButton}
            >
              {t('myProfile.save_changes')}
            </Button>
          </Form.Item>
        </Form>
      </div>
      <VerificationModal
        visible={openVerificationModal}
        onClose={() => {
          setOpenVerificationModal(false);
        }}
        onSuccess={async () => {
          const values = form.getFieldsValue();
          handleSubmit(values);
        }}
      />
    </div>
  );
}
