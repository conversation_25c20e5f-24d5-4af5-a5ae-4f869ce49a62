.container {
  width: 100%;
}

.title {
  font-size: 24px !important;
  font-weight: 500 !important;
  font-family: var(--font-radio-canada) !important;
  color: #000000 !important;
}

:global(html[data-theme='dark']) .title {
  color: #fff !important;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.searchInput {
  flex: 1;
  min-width: 200px;
  font-family: var(--font-poppins) !important;
  background-color: #f5f5f5 !important;
}

:global(html[data-theme='dark']) .searchInput {
  background-color: #06164a !important;
  color: #fff !important;
}
:global(html[data-theme='dark']) .roleSelect {
  background-color: #06164a !important;
  border-radius: 10px;
}

.roleSelect {
  min-width: 150px;
  font-family: var(--font-poppins) !important;
  background-color: #ffffff !important;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.userText {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

.userName {
  font-weight: 500 !important;
  font-size: 18px !important;
  font-family: var(--font-radio-canada) !important;
  color: #000000 !important;
}

.count {
  font-weight: 400 !important;
  font-size: 16px !important;
  font-family: var(--font-poppins) !important;
  color: #000000 !important;
}

:global(html[data-theme='dark']) .userName {
  color: #fff !important;
}

:global(html[data-theme='dark']) .userEmail {
  color: #edeff1 !important;
}

:global(html[data-theme='dark']) .role {
  color: #edeff1 !important;
}
:global(html[data-theme='dark']) .remove {
  color: #fff !important;
}
.userEmail {
  font-weight: 400 !important;
  font-size: 14px !important;
  font-family: var(--font-poppins) !important;
  color: #555555 !important;

  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove {
  color: #000000;
  cursor: pointer;
  font-family: var(--font-poppins) !important;
  font-size: 16px !important;
}

/* .table {
  margin-top: 20px;
}

.table :global(.ant-table-tbody > tr > td) {
  border-bottom: none !important;
}

.table :global(.ant-table-thead > tr > th) {
  background-color: transparent !important;
  border-bottom: 1px solid #000000;
}

:global(html[data-theme='dark']) .table :global(.ant-table-thead > tr > th) {
  border-bottom: 1px solid #edeff1 !important;
} */

:global(html[data-theme='dark']) .titleText {
  color: #fff !important;
}

:global(html[data-theme='dark']) .text {
  color: #fff !important;
}

:global(html[data-theme='dark']) .count {
  color: #edeff1 !important;
}

.tableHeader {
  font-size: 18px;
  font-weight: 400;
  color: #000000;
  font-family: var(--font-poppins) !important;
}

:global(html[data-theme='dark']) .tableHeader {
  color: #fff !important;
}

.role {
  font-weight: 500 !important;
  font-size: 14px !important;
  font-family: var(--font-poppins) !important;
  color: #000000 !important;
}

.avatar {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50%;
  object-fit: cover;
}

.titleText {
  font-size: 16px !important;
  text-align: center !important;
  font-family: var(--font-poppins) !important;
  font-weight: 500 !important;
  color: #000000 !important;
}

.text {
  font-size: 18px !important;
  text-align: center !important;
  font-family: var(--font-poppins) !important;
  font-weight: 500 !important;
  color: #000000 !important;
}

@media (max-width: 1200px) {
  .tableHeader {
    font-size: 16px !important;
  }
}

@media (max-width: 900px) {
  .searchInput {
    padding: 2px 20px !important;
  }
  .userName {
    font-size: 16px !important;
  }
  .avatar {
    width: 50px !important;
    height: 50px !important;
  }

  .userEmail {
    font-size: 13px !important;
  }

  .title {
    font-size: 22px !important;
  }

  .tableHeader {
    font-size: 16px !important;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .actions {
    flex-direction: column;
    align-items: stretch;
  }

  .searchInput,
  .roleSelect,
  .inviteBtn {
    width: 100% !important;
  }

  .userEmail {
    font-size: 10px;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .avatar {
    width: 35px !important;
    height: 35px !important;
  }
  .userText {
    margin-left: 5px !important;
  }
  .title {
    font-size: 20px !important;
  }
  .remove {
    font-size: 14px !important;
  }
  .userInfo {
    gap: 5px;
  }

  .titleText {
    font-size: 14px !important;
  }

  .text {
    font-size: 16px !important;
  }
}
