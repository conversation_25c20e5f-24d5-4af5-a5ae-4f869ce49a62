import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Language = 'en' | 'es';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type TranslationData = Record<string, any>;

interface LanguageState {
  currentLanguage: Language;
  translations: Record<string, TranslationData>;
  loading: boolean;
}

const initialState: LanguageState = {
  currentLanguage: 'en',
  translations: {},
  loading: false,
};

const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<Language>) => {
      state.currentLanguage = action.payload;
    },
    setTranslations: (
      state,
      action: PayloadAction<Record<string, TranslationData>>
    ) => {
      state.translations = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
});

export const { setLanguage, setTranslations, setLoading } =
  languageSlice.actions;
export default languageSlice.reducer;
