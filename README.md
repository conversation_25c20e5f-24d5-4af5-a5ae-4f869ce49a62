# SEAL MY IDEA

This is a [Next.js](https://nextjs.org) project with Redux Toolkit, Redux Persist, and a beautiful login system using styled-components.

## Features

- ✅ **Next.js 15** with App Router
- ✅ **Redux Toolkit** for state management
- ✅ **Redux Persist** for state persistence across browser sessions
- ✅ **TypeScript** for type safety
- ✅ **ESLint** with strict rules for code quality
- ✅ **Prettier** for consistent code formatting
- ✅ **Error Boundary** with Redux reset functionality
- ✅ **Jest Testing** with comprehensive test setup
- ✅ **Ant Design** integration with custom theming
- ✅ **Git Hooks** for automatic code quality checks
- ✅ **Dark/Light Mode** with system preference detection
- ✅ **Multi-language Support** with English and Spanish translations
- ✅ **Interactive Charts** using Chart.js and react-chartjs-2
- ✅ **Sidebar Navigation** with collapsible menu and logout option

## Getting Started

First, install dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Development Scripts

```bash
# Development
npm run dev              # Start development server with Turbopack

# Building
npm run build           # Build for production
npm run start           # Start production server

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Run ESLint with auto-fix
npm run format          # Format code with Prettier
npm run format:check    # Check code formatting
npm run type-check      # Run TypeScript type checking

# Testing
npm run test            # Run Jest tests
npm run test:watch      # Run Jest in watch mode

# All-in-one check
npm run check-all       # Run type-check, lint, format-check, and test
```

## Login Credentials

For testing purposes, use these credentials:

- **Username:** `admin`
- **Password:** `password`

## Ant Design Integration

The project includes Ant Design with:

- **Custom theme** matching the app's color scheme
- **Professional components** ready to use
- **Example page** showing form components and styling
- **Seamless integration** with styled-components

To see Ant Design in action:

1. Login to the dashboard
2. Click "Show Ant Design Demo"
3. Explore the form components and styling

## Dark Mode Support

The application includes a comprehensive dark mode system:

- **Theme Toggle**: Click the sun/moon icon to switch themes
- **System Preference**: Automatically detects your system theme
- **Persistent**: Remembers your theme choice across sessions
- **Smooth Transitions**: Animated theme switching
- **Comprehensive**: Covers all components and custom styles

## Theme System

All themes are centrally managed in `src/theme/antdTheme.ts`:

- **Organized Colors**: Consistent color palette
- **Component Themes**: Ant Design component customization
- **Styled Components**: Shared theme for styled-components
- **Easy Customization**: Modify colors and styles in one place

## SealMyIdea Dashboard

The application features a professional dashboard that exactly matches the SealMyIdea design:

### Features:

- **Sidebar Navigation**: Collapsible sidebar with SEALMYIDEA branding
- **User Management Badge**: Red "D" notification badge on User Management
- **Overview Cards**: Statistics for Active Client Folders, Documents Uploaded, Sealed Documents, and Team Members
- **Client Folder Table**: Sortable table with client data and delete actions
- **Interactive Charts**:
  - Line chart for Document Uploads By Team
  - Bar chart for Sealed Documents Per Client
  - Doughnut chart for Team Contributions
- **Appearance Settings**: Modal with language and theme selection
- **Logout Option**: Located at the bottom of the sidebar

### Color Scheme:

- **Primary**: Sky blue gradient (`#87ceeb` to `#4682b4`)
- **Background**: Light blue (`#f0f8ff`)
- **Cards**: White with subtle shadows
- **Charts**: Blue color palette matching the design

## Multi-language Support

The application includes comprehensive internationalization:

### Languages:

- **English** (`en.json`)
- **Spanish** (`es.json`)

### Features:

- **Dynamic Loading**: Translations loaded on demand
- **Redux Integration**: Language state managed with Redux
- **Persistent Storage**: Language preference saved across sessions
- **Easy Extension**: Add new languages by creating JSON files

### Usage:

```typescript
import { useTranslation } from '@/hooks/useTranslation';

const { t } = useTranslation();
const welcomeText = t('dashboard.welcomeAgency'); // "Welcome Agency 👋"
```

## Project Structure

```
src/
├── app/                    # Next.js App Router
├── components/
│   ├── auth/              # Authentication components
│   ├── dashboard/         # Dashboard components
│   ├── providers/         # Redux Provider
│   └── ui/               # UI components
└── store/
    ├── slices/           # Redux slices
    ├── hooks.ts          # Typed Redux hooks
    └── index.ts          # Store configuration
```

## Redux Store

The Redux store is configured with:

- **Auth Slice**: Manages user authentication state
- **Redux Persist**: Automatically saves auth state to localStorage
- **TypeScript**: Fully typed store and hooks

## State Management

The app uses Redux Toolkit for state management with the following features:

- User authentication state
- Loading states
- Error handling
- Persistent login sessions

## Error Handling

The application includes comprehensive error handling:

- **Error Boundary**: Catches React component errors
- **Redux Reset**: Option to clear all Redux state and restart
- **Global Error Handler**: Catches unhandled promise rejections
- **Development Error Tester**: Test error boundary in development mode

To test the error boundary:

1. Login to the dashboard
2. Click the "Test Error Boundary" button (only visible in development)
3. See the error page with options to recover

## Code Quality

The project enforces high code quality standards:

- **ESLint**: Strict linting rules for TypeScript and React
- **Prettier**: Consistent code formatting
- **TypeScript**: Full type safety
- **Jest**: Unit testing for Redux logic
- **VS Code Integration**: Automatic formatting and linting on save

## Styling

The project uses styled-components for styling with:

- Gradient backgrounds
- Smooth animations
- Responsive design
- Modern UI components

## Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs)
- [Redux Toolkit](https://redux-toolkit.js.org/)
- [Redux Persist](https://github.com/rt2zz/redux-persist)
- [Styled Components](https://styled-components.com/)
