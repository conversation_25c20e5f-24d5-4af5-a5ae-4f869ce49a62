/* Loading Screen Styles - Responsive and Centered */

.loadingScreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--loading-bg, #f5f7fa);
  z-index: 9999;
  transition: background-color 0.3s ease;
  overflow: hidden;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  text-align: center;
  padding: 20px;
  min-width: 120px;
}

.loadingText {
  color: var(--loading-text, #1a1a1a);
  font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
  white-space: nowrap;
}

/* Dark mode support */
:global(html[data-theme='dark']) .loadingScreen {
  --loading-bg: #0f1419;
  --loading-text: #ffffff;
}

:global(body[data-theme='dark']) .loadingScreen {
  --loading-bg: #0f1419;
  --loading-text: #ffffff;
}

/* Tablet styles */
@media (max-width: 1024px) {
  .loadingContent {
    padding: 18px;
  }

  .loadingText {
    font-size: 15px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .loadingContent {
    padding: 16px;
    gap: 14px;
  }

  .loadingText {
    font-size: 14px;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .loadingContent {
    padding: 12px;
    gap: 12px;
  }

  .loadingText {
    font-size: 13px;
  }
}

/* Landscape orientation on mobile */
@media (orientation: landscape) and (max-height: 500px) {
  .loadingContent {
    gap: 10px;
    padding: 10px;
  }

  .loadingText {
    font-size: 12px;
  }
}

/* Very small screens */
@media (max-width: 320px) {
  .loadingContent {
    padding: 8px;
    gap: 8px;
  }

  .loadingText {
    font-size: 12px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .loadingText {
    font-weight: 400;
  }
}

/* Ensure proper centering on all devices */
@supports (display: grid) {
  .loadingScreen {
    display: grid;
    place-items: center;
  }
}

/* Fallback for older browsers */
@supports not (display: grid) {
  .loadingScreen {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
