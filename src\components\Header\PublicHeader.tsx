import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch } from '@/store/hooks';
import { Language, setLanguage } from '@/store/slices/languageSlice';
import { Col, Row, Select, Space } from 'antd';
import { usePathname } from 'next/navigation';
import { useCallback } from 'react';
import ThemeToggle from '../ui/ThemeToggle';

const { Option } = Select;

export default function PublicHeader() {
  const { currentLanguage } = useTranslation();
  const dispatch = useAppDispatch();

  const pathname = usePathname();
  const hideHeaderRoutes = ['/verify-certificate'];
  const shouldHideHeader = hideHeaderRoutes.some(route =>
    pathname.startsWith(route)
  );

  const handleLanguageChange = useCallback(
    (language: Language) => {
      dispatch(setLanguage(language));
    },
    [dispatch]
  );
  return (
    <Space
      direction='vertical'
      style={{ width: '100%', position: 'fixed', top: 0, zIndex: 1000 }}
      size='large'
    >
      <Row justify='end' style={{ gap: '16px' }}>
        {!shouldHideHeader && (
          <Col md={4}>
            <Select
              value={currentLanguage}
              onChange={handleLanguageChange}
              style={{ width: '100%', marginTop: 8 }}
            >
              <Option value='en'>🇺🇸 English</Option>
              <Option value='es'>🇪🇸 Español</Option>
            </Select>
          </Col>
        )}

        <Col
          md={2}
          style={{
            marginTop: '7px',
          }}
        >
          <ThemeToggle size='large' />
        </Col>
      </Row>
    </Space>
  );
}
