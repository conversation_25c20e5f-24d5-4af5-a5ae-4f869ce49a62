'use client';

import { useAppSelector } from '@/store/hooks';
import { CheckOutlined } from '@ant-design/icons';
import { Form, FormInstance, Row } from 'antd';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Endpoints } from '../../config/endpoints';
import siteConfig from '../../config/site.config';
import { getApiData } from '../../helpers/ApiHelper';
import styles from './SelectGoal.module.css';

type Goal = {
  _id: string;
  goal_name: string;
  goal_image: string;
  status: string;
};

type Props = {
  form: FormInstance;
};

export default function StepTwo({ form }: Props) {
  const [goals, setGoals] = useState<Goal[]>([]);
  const { user } = useAppSelector(state => state.auth);
  const selectedGoals = Form.useWatch('goals', form) || [];

  useEffect(() => {
    fetchGoals();
  }, []);

  const fetchGoals = async () => {
    try {
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      const response = await getApiData<{}, { status: boolean; data: Goal[] }>({
        url: `${siteConfig.apiUrl}${Endpoints.goalList}`,
        method: 'GET',
        customUrl: true,
      });

      if (response && response.status === true && response.data) {
        setGoals(response.data);
      }
    } catch (error) {
      console.log('Error fetching goals:', error);
    } finally {
    }
  };

  useEffect(() => {
    if (user?.goals && user.goals.length > 0) {
      form.setFieldValue('goals', user.goals);
    }
  }, [user, form]);

  const toggleGoal = (goalId: string) => {
    const updated = selectedGoals.includes(goalId)
      ? selectedGoals.filter((g: string) => g !== goalId)
      : [...selectedGoals, goalId];

    form.setFieldValue('goals', updated);
  };

  return (
    <div className={styles.container}>
      <Form.Item name='goals' hidden />
      <div className={styles.goalList}>
        {goals.map(goal => {
          const isSelected = selectedGoals.includes(goal._id);
          return (
            <div
              key={goal._id}
              className={`${styles.goalItem} ${isSelected ? styles.selected : ''}`}
              onClick={() => toggleGoal(goal._id)}
            >
              <Row className={styles.goalCardRow}>
                <div className={styles.iconWrap}>
                  <Image
                    src={`${goal.goal_image}`}
                    alt={goal.goal_name}
                    width={32}
                    height={32}
                  />
                </div>
                <span className={styles.label}>{goal.goal_name}</span>
              </Row>
              {isSelected && (
                <div className={styles.checkIcon}>
                  <CheckOutlined />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
