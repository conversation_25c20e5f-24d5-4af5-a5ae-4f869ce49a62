'use client';

import Subscription from '@/components/Subscription';
import { getUserData } from '@/utils/CommonFunctions';
import { useEffect } from 'react';

/**
 * Subcription Settings page component
 *
 * @page SubscriptionSettingPage
 * @description Page for managing Subscription Setting Page
 * @returns {JSX.Element} The Appearance Subscription Setting page component
 *
 * @example
 * // This page is automatically rendered when user navigates to /settings/subsciption
 * // It displays the subcription details
 */
const SubscriptionSettingPage = () => {
  useEffect(() => {
    getUserData();
  }, []);

  return (
    <Subscription
      plan={null}
      modalData={{ visible: false, plan: null, isCurrentPlan: true }}
    />
  );
};

export default SubscriptionSettingPage;
