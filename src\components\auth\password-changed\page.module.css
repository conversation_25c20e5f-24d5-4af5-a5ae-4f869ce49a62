.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  display: flex;
  justify-content: center;
  padding: 20px;
  position: relative;
}

:global(html[data-theme='dark']) .container,
:global(body[data-theme='dark']) .container {
  background: #012458;
}

:global(html[data-theme='dark']) .formTitle {
  color: #fff !important;
}

:global(html[data-theme='dark']) .formSubtitle {
  color: #edeff1 !important;
}
:global(html[data-theme='dark']) .supportText {
  color: #edeff1 !important;
}

.content {
  width: 700px;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}

/* Logo Section */
.logoSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.logoImage {
  margin-bottom: 24px;
  justify-content: center;
  align-items: center;
  display: flex;
  align-self: center;
  text-align: center;
}

.logoText {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.logoMain {
  font-size: 24px;
  font-weight: 700;
  color: #e74c3c;
  letter-spacing: 2px;
  font-family: var(--font-radio-canada), sans-serif;
}

.logoSub {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* Icon Section */
.iconSection {
  position: relative;
  justify-content: center;
}

.decorativeElements {
  position: absolute;
  width: 200px;
  height: 200px;
}

.decorativeElements::before,
.decorativeElements::after,
.element1,
.element2,
.element3,
.element4 {
  position: absolute;
  content: '';
  border-radius: 50%;
}

.element1 {
  width: 8px;
  height: 8px;
  background: #27ae60;
  top: 20px;
  left: 30px;
  animation: float 3s ease-in-out infinite;
}

.element2 {
  width: 6px;
  height: 6px;
  background: #2ecc71;
  top: 40px;
  right: 25px;
  animation: float 3s ease-in-out infinite 0.5s;
}

.element3 {
  width: 10px;
  height: 10px;
  background: #58d68d;
  bottom: 30px;
  left: 20px;
  animation: float 3s ease-in-out infinite 1s;
}

.element4 {
  width: 7px;
  height: 7px;
  background: #82e0aa;
  bottom: 50px;
  right: 30px;
  animation: float 3s ease-in-out infinite 1.5s;
}

.successIcon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(39, 174, 96, 0.3);
  border: 4px solid rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 2;
}

/* Text Section */
.textSection {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 700px;
}

.formTitle {
  text-align: center !important;
  margin-bottom: 4px !important;
  color: #000000 !important;
  font-weight: 600 !important;
  font-size: 46px !important;
  font-family: var(--font-radio-canada);
  margin-top: 10px;
}

.formSubtitle {
  display: block;
  text-align: center;
  margin-bottom: 10px;
  color: #555555 !important;
  font-size: 18px;
  line-height: 1.4;
  font-family: var(--font-poppins);
  font-weight: 300;
  margin-top: 0px;
}

.title {
  font-size: 28px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  margin: 0 !important;
  line-height: 1.3 !important;
}

.description {
  font-size: 16px !important;
  color: #7f8c8d !important;
  line-height: 1.5 !important;
}

/* Button Section */
.buttonSection {
  width: 100%;
  max-width: 450px;
}

.loginButton {
  width: 100% !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  font-family: var(--poppins);
}

.loginButton:hover {
  background: var(--color-primaryHover) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(1, 36, 88, 0.3) !important;
}

.supportText {
  color: #7f8c8d !important;
  font-size: 14px !important;
  cursor: pointer;
  transition: color 0.3s ease;
  text-decoration: underline;
}

.supportText:hover {
  color: var(--color-primary) !important;
}

/* Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive Design */

/* Tablet */
/* @media (max-width: 1024px) {
  .content {
    gap: 28px;
    max-width: 420px;
  }

  .logoMain {
    font-size: 22px;
  }

  .title {
    font-size: 26px !important;
  }

  .successIcon {
    width: 75px;
    height: 75px;
  }
} */

/* Mobile Large */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
    min-height: 100vh;
  }

  .content {
    gap: 32px;
    justify-content: center;
    min-height: calc(100vh - 32px);
  }

  .formTitle {
    font-size: 40px !important;
  }

  .formSubtitle {
    font-size: 16px !important;
  }

  .logoSection {
    margin-bottom: 0;
  }

  .logoMain {
    font-size: 20px;
  }

  .logoSub {
    font-size: 11px;
  }

  .iconSection {
    margin: 20px 0;
  }

  .title {
    font-size: 24px !important;
    line-height: 1.2 !important;
  }

  .description {
    font-size: 15px !important;
    margin-top: 8px;
  }

  .successIcon {
    width: 70px;
    height: 70px;
  }

  .decorativeElements {
    width: 160px;
    height: 160px;
  }

  .buttonSection {
    margin-top: auto;
    margin-bottom: 0px;
    width: 100%;
  }

  .loginButton {
    height: 48px !important;
    font-size: 16px !important;
    border-radius: 12px !important;
  }

  .supportSection {
    margin-top: -10px;
    margin-bottom: 20px;
  }
}

/* Mobile Medium */
@media (max-width: 900px) {
  .container {
    padding: 20px;
  }

  .formTitle {
    font-size: 32px !important;
  }

  .content {
    gap: 28px;
    max-width: 340px;
    min-height: calc(100vh - 32px);
  }

  .formSubtitle {
    font-size: 12px !important;
    margin-top: 5px;
  }

  .logoMain {
    font-size: 18px;
  }

  .logoSub {
    font-size: 10px;
  }

  .title {
    font-size: 22px !important;
  }

  .description {
    font-size: 14px !important;
  }

  .successIcon {
    width: 65px;
    height: 65px;
  }

  .decorativeElements {
    width: 140px;
    height: 140px;
  }

  .element1,
  .element2,
  .element3,
  .element4 {
    width: 6px;
    height: 6px;
  }

  .loginButton {
    height: 48px !important;
    font-size: 16px !important;
  }

  .supportText {
    font-size: 14px !important;
  }
}

@media (max-width: 600px) {
  .formTitle {
    font-size: 24px !important;
  }
}

/* Very small screens */
@media (max-width: 320px) {
  .content {
    gap: 16px;
    max-width: 260px;
  }

  .formTitle {
    font-size: 20px !important;
  }

  .logoMain {
    font-size: 14px;
  }

  .title {
    font-size: 18px !important;
  }

  .successIcon {
    width: 55px;
    height: 55px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .container {
    background: #f0f0f0;
  }

  .title {
    color: #000000 !important;
  }

  .description {
    color: #333333 !important;
  }

  .loginButton {
    border: 2px solid #000000 !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .element1,
  .element2,
  .element3,
  .element4 {
    animation: none;
  }

  .loginButton:hover {
    transform: none !important;
  }
}
