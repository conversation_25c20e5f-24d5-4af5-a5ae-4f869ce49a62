.container {
  width: 100%;
  /* overflow: hidden; */
}

.documentsContainer {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.documents {
  font-size: 16px;
  color: #555555;
  font-family: var(--font-poppins);
  font-weight: 400;
  white-space: nowrap;
  flex-shrink: 0;
}

.documentsDivider {
  flex: 1;
  min-width: 0; /* Allows the flex item to shrink below its default minimum width */
  margin: 0;
}
