import { <PERSON><PERSON>, <PERSON>, Typography } from 'antd';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Endpoints } from '../../config/endpoints';
import { ApiResponse, getApiData } from '../../helpers/ApiHelper';
import useMediaQuery from '../../hooks/useMediaQuery';
import { handleDownload } from '../../utils/CommonFunctions';
import { useNotification } from '../Notification/NotificationContext';
import styles from './SealedCertificate.module.css';

interface FolderFormData {
  folder_id: string;
  want_qr: boolean;
}

interface data {
  html: string;
  pdf: string;
  folder_name: string;
}

const SealedCertificate = () => {
  const params = useParams();
  const id = params.id as string;

  const { Title } = Typography;

  const notification = useNotification();

  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(false);
  const [downloading, setDownloading] = useState<boolean>(false);

  const [certificateData, setCertificateData] = useState<data>({
    html: '',
    pdf: '',
    folder_name: '',
  });

  useEffect(() => {
    if (id) {
      getSealedCertificate(id);
    }
  }, [id]);

  const getSealedCertificate = async (id: string) => {
    try {
      setLoading(true);
      const res = await getApiData<FolderFormData, ApiResponse>({
        url: `${Endpoints.sealedDocumentCertificate}`,
        method: 'POST',
        data: {
          folder_id: id,
          want_qr: true,
        },
      });
      if (res && res.status === true && res.data) {
        setCertificateData(res.data);
        setLoading(false);
      } else {
        setLoading(false);
        notification.error({
          message: res?.message || 'Error fetching sealed certificate',
        });
      }
    } catch (error) {
      console.error('Error fetching folder data:', error);
      setLoading(false);
      notification.error({
        message: 'Error fetching sealed certificate',
      });
    }
  };

  const sm = useMediaQuery('(max-width: 600px)');
  const md = useMediaQuery('(max-width: 900px)');

  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '90vh',
        }}
      >
        <Spin
          size='large'
          style={{ display: 'flex', justifyContent: 'center', marginTop: 50 }}
        />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.titleContainer}>
        <Title level={2} className={styles.pageTitle}>
          {certificateData?.folder_name}
        </Title>
      </div>
      <iframe
        srcDoc={certificateData?.html}
        style={{
          width: sm ? '90vw' : md ? '80vw' : '50vw',
          height: '100vh',
          border: 'none',
          backgroundColor: 'white',
        }}
        title='Big Certificate'
      />
      <div className={styles.buttons}>
        <Button
          type='primary'
          loading={downloading}
          onClick={async () => {
            if (certificateData?.pdf && !downloading) {
              try {
                setDownloading(true);
                await handleDownload(certificateData.pdf);
              } catch (error) {
                console.error('Download failed:', error);
                notification.error({
                  message: 'Download Failed',
                  description:
                    'Failed to download the certificate. Please try again.',
                });
              } finally {
                setDownloading(false);
              }
            }
          }}
          style={{
            width: sm ? '100%' : 'auto',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
          }}
        >
          Download Certificate
        </Button>
        <Button
          type='primary'
          onClick={() => {
            router.push('/dashboard');
          }}
          style={{
            width: sm ? '100%' : 'auto',
          }}
        >
          Back To Dashboard
        </Button>
      </div>
    </div>
  );
};

export default SealedCertificate;
