'use client';
import { ArrowLeftOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Modal,
  Pagination,
  Row,
  Select,
  Spin,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { HiLightBulb } from 'react-icons/hi';
import { Endpoints } from '../../config/endpoints';
import { images } from '../../config/images';
import { ApiResponse, getApiData } from '../../helpers/ApiHelper';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useTranslation } from '../../hooks/useTranslation';
import { getStatusText, StatusCode } from '../../utils/CommonFunctions';
import { useNotification } from '../Notification/NotificationContext';
import UserIdeaCard from './UserIdeaCard';
import styles from './UserManagementDetails.module.css';

const { Option } = Select;

const { Text, Title } = Typography;

interface UserData {
  _id: string;
  fullname: string;
  email: string;
  profile_pic: string;
  status: string;
  invited_user_status: string;
  shared_member_id: string;
}

interface folder {
  _id: string;
  user_id: string;
  name: string;
  color: string;
  status: StatusCode;
  createdAt: number;
  folder_access_role: string;
  shared_member_id: string;
}
interface Response extends ApiResponse {
  invited_user: UserData;
  pagination: paginationData;
  folder_data: folder[];
}

interface paginationData {
  totalCount: number | null;
  pageSize: number;
  totalPage: number | null;
  currentPage: number;
  isMore: boolean;
}

const UserManagementDetails = () => {
  const { t } = useTranslation();

  const router = useRouter();

  const notification = useNotification();

  const searchParams = useSearchParams();
  const userId = searchParams.get('id') || '';

  const [folderHistoryList, setFolderHistoryList] = useState<folder[]>([]);

  const [loading, setLoading] = useState(false);

  const [userData, setUserData] = useState<UserData>();

  const [pagination, setPagination] = useState<paginationData>({
    currentPage: 1,
    isMore: false,
    pageSize: 10,
    totalCount: null,
    totalPage: null,
  });

  const [revokeLoader, setRevokeLoader] = useState(false);
  const [openConfirmModal, setOpenConfirmModal] = useState(false);
  const [revokeData, setRevokeData] = useState({
    folderId: '',
    sharedMemberId: '',
  });

  useEffect(() => {
    if (userId) {
      getFolderHistoryList(userId, 1, true);
    }
  }, [userId]);

  const lg = useMediaQuery('(max-width: 1200px)');
  const sm = useMediaQuery('(max-width: 600px)');

  const getFolderHistoryList = async (
    id: string,
    page?: number,
    load?: boolean
  ) => {
    if (load) {
      setLoading(true);
    }
    try {
      const res = await getApiData<
        { page?: number; invited_user_id: string },
        Response
      >({
        url: Endpoints.sharingFolderHistory,
        method: 'POST',
        data: {
          page: page || 1,
          invited_user_id: id,
        },
      });
      if (res && res.status === true) {
        if (res.invited_user) {
          setUserData(res?.invited_user);
        }
        if (Array.isArray(res.folder_data)) {
          setFolderHistoryList(res?.folder_data);
        }
        if (res.pagination) {
          setPagination({
            currentPage: res.pagination.currentPage ?? 1,
            isMore: res.pagination.isMore ?? false,
            pageSize: res.pagination.pageSize ?? 10,
            totalCount: res.pagination.totalCount ?? null,
            totalPage: res.pagination.totalPage ?? null,
          });
        }
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.log('Error fetching user data:', error);
      setLoading(false);
    }
  };

  const changeFolderAccessRole = async (
    accessRole: string,
    folderId: string
  ) => {
    try {
      const res = await getApiData<
        { access_role: string; invited_user_id: string; folder_id: string },
        ApiResponse
      >({
        url: Endpoints.changeFolderAccessRole,
        method: 'POST',
        data: {
          access_role: accessRole,
          invited_user_id: userId,
          folder_id: folderId,
        },
      });
      if (res && res.status === true) {
        notification.success({
          message: 'Success',
          description: res?.message || 'User access role changed successfully',
        });
        getFolderHistoryList(userId || '', pagination.currentPage, false);
      } else {
        notification.error({
          message: 'Error',
          description: res?.message || 'Something went wrong',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error?.message || 'Something went wrong',
      });
    }
  };

  const revokeAccess = async () => {
    setRevokeLoader(true);
    try {
      const res = await getApiData<
        {
          shared_member_id: string;
          folder_id: string;
          remove_user_id: string;
        },
        ApiResponse
      >({
        url: Endpoints.revokeAccess,
        method: 'POST',
        data: {
          shared_member_id: revokeData.sharedMemberId,
          folder_id: revokeData.folderId,
          remove_user_id: userData?._id || '',
        },
      });
      if (res && res.status === true) {
        notification.success({
          message: 'Success',
          description: res?.message || 'User access revoked successfully',
        });
        setRevokeLoader(false);
        setOpenConfirmModal(false);
        setRevokeData({
          folderId: '',
          sharedMemberId: '',
        });
        getFolderHistoryList(userId || '', pagination.currentPage, false);
      } else {
        notification.error({
          message: 'Error',
          description: res?.message || 'Something went wrong',
        });
        setRevokeLoader(false);
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error?.message || 'Something went wrong',
      });
      setRevokeLoader(false);
    }
  };

  const getStatus = (status: string) => {
    let color = '';
    if (status === 'active') {
      color = '#02CD27';
    } else if (status === 'pending_invite') {
      color = '#0059FF';
    } else if (status === 'deactivated') {
      color = '#D50000';
    }

    let tagBgColor = '';

    if (status === 'active') {
      tagBgColor = '#02CD272E';
    } else if (status === 'pending_invite') {
      tagBgColor = '#0059FF24';
    } else if (status === 'deactivated') {
      tagBgColor = '#FF12122E';
    }

    return (
      <Tag
        color={tagBgColor}
        style={{
          padding: sm ? '2px 15px' : '3px 20px',
          borderRadius: '8px',
        }}
      >
        <Text
          style={{
            color: color,
            fontWeight: '400',
            fontSize: sm ? '12px' : '15px',
          }}
        >
          {status === 'active'
            ? 'Active'
            : status === 'pending_invite'
              ? 'Pending Invite'
              : 'Deactivated'}
        </Text>
      </Tag>
    );
  };

  return loading ? (
    <div
      style={{
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Spin size='large' />
    </div>
  ) : (
    <div className={styles.container}>
      {sm && (
        <div>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.back();
            }}
          >
            {t('common.back_to_user')}
          </Button>
        </div>
      )}

      <Row gutter={[5, 0]}>
        <Col xl={16} lg={18} md={18} sm={18} xs={24}>
          <div className={styles.userInfo}>
            <Image
              src={userData?.profile_pic || images.userPlaceholder}
              alt='user'
              width={40}
              height={40}
              className={styles.avatar}
            />
            <div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  width: '100%',
                }}
              >
                <Title className={styles.userName}>{userData?.fullname}</Title>
                {getStatus(userData?.invited_user_status || '')}
              </div>
              <Text
                className={styles.userEmail}
                style={{ display: 'block', maxWidth: '100%' }}
              >
                {userData?.email}
              </Text>
            </div>
          </div>
        </Col>
        {sm ? null : (
          <Col
            xl={8}
            lg={6}
            md={6}
            sm={6}
            xs={24}
            style={{
              textAlign: 'end',
            }}
          >
            <Button
              type='primary'
              onClick={() => {
                router.back();
              }}
            >
              {t('common.back_to_user')}
            </Button>
          </Col>
        )}
      </Row>

      {/* Column Headers - Only show for large screens */}
      {!lg && folderHistoryList.length > 0 && (
        <div className={styles.columnHeaders}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '5px',
            }}
          >
            <div style={{ width: '65px' }} />
          </div>
          <div className={styles.headerRow}>
            <div className={styles.headerDate}>
              <Text className={styles.columnTitle}>Date</Text>
            </div>
            <div className={styles.headerFolder}>
              <Text className={styles.columnTitle}>Folder Name</Text>
            </div>
            <div className={styles.headerStatus}>
              <Text className={styles.columnTitle}>Status</Text>
            </div>
            <div className={styles.headerRole}>
              <Text className={styles.columnTitle}>User Role</Text>
            </div>
          </div>
          <div style={{ width: '80px' }} /> {/* Space for revoke button */}
        </div>
      )}

      <div className={styles.timeline}>
        {folderHistoryList.map(item => (
          <div key={item._id} className={styles.timelineItem}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              <Image
                src={images.indicator}
                alt='indicator'
                width={15}
                height={15}
                style={{
                  marginLeft: sm ? '-15px' : '0px',
                }}
              />
              {sm ? null : (
                <div
                  className={`${styles.iconWrapper} ${styles[item.color]}`}
                  style={{
                    backgroundColor: item.color,
                  }}
                >
                  <HiLightBulb className={styles.icon} />
                </div>
              )}
            </div>
            {lg ? (
              <UserIdeaCard
                user={item}
                changeFolderAccessRole={changeFolderAccessRole}
                revokeClick={() => {
                  setRevokeData({
                    folderId: item._id,
                    sharedMemberId: item.shared_member_id,
                  });
                  setOpenConfirmModal(true);
                }}
              />
            ) : (
              <>
                <div className={styles.content}>
                  <div className={styles.row}>
                    <div
                      style={{
                        width: '15%',
                      }}
                    >
                      <span className={styles.date}>
                        {' '}
                        {dayjs(item.createdAt).format('DD-MM-YYYY')}
                      </span>
                    </div>
                    <div
                      style={{
                        width: '40%',
                        textAlign: 'left',
                      }}
                    >
                      <span className={styles.folder}>{item.name}</span>
                    </div>
                    <div
                      style={{
                        width: '10%',
                        textAlign: 'center',
                      }}
                    >
                      <Tag
                        className={`${styles.statusTag} ${styles[`statusTag${item.status === '2' ? 'InReview' : getStatusText(item.status)}`]}`}
                      >
                        {getStatusText(item.status)}
                      </Tag>
                    </div>
                    <div
                      style={{
                        width: '25%',
                        textAlign: 'center',
                      }}
                    >
                      <Select
                        value={item.folder_access_role}
                        className={styles.roleSelectDrp}
                        onSelect={value => {
                          if (item.folder_access_role !== value) {
                            changeFolderAccessRole(value, item._id);
                          }
                        }}
                      >
                        <Option value='admin'>Admin</Option>
                        <Option value='legal_user'>Legal User</Option>
                        <Option value='user'>User</Option>
                      </Select>
                    </div>
                  </div>
                </div>
                <Button
                  type='default'
                  style={{
                    backgroundColor: '#C70000',
                    color: '#fff',
                    height: '40px',
                  }}
                  onClick={() => {
                    setRevokeData({
                      folderId: item._id,
                      sharedMemberId: item.shared_member_id,
                    });
                    setOpenConfirmModal(true);
                  }}
                >
                  {t('common.revoke')}
                </Button>
              </>
            )}
          </div>
        ))}
      </div>

      {pagination?.totalPage && pagination?.totalPage > 1 ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: '40px',
          }}
        >
          <Pagination
            current={pagination.currentPage}
            pageSize={pagination.pageSize}
            total={pagination.totalCount || 0}
            onChange={p => {
              setPagination(prev => ({
                ...prev,
                currentPage: p,
              }));
              getFolderHistoryList(userId || '', p, true);
            }}
            showSizeChanger={false}
          />
        </div>
      ) : null}

      <Modal
        open={openConfirmModal}
        onCancel={() => {
          setOpenConfirmModal(false);
          setRevokeData({
            folderId: '',
            sharedMemberId: '',
          });
        }}
        footer={null}
        centered
        width={500}
        closable={true}
      >
        <Row
          gutter={[16, 16]}
          style={{
            padding: sm ? '10px' : '20px',
          }}
        >
          <Col
            xs={24}
            style={{
              textAlign: 'center',
            }}
          >
            <div
              style={{
                marginTop: '15px',
              }}
            >
              <Text className={styles.text}>
                {t('userManagementDetails.confirmRemoveAccessUser')}
              </Text>
            </div>
          </Col>

          <Col
            xs={24}
            style={{
              textAlign: 'center',
              marginTop: '10px',
            }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={12}>
                <Button
                  type='default'
                  onClick={() => {
                    setOpenConfirmModal(false);
                    setRevokeData({
                      folderId: '',
                      sharedMemberId: '',
                    });
                  }}
                  style={{
                    width: '100%',
                  }}
                >
                  {t('userManagementDetails.cancel')}
                </Button>
              </Col>
              <Col xs={12}>
                <Button
                  type='primary'
                  onClick={() => {
                    revokeAccess();
                  }}
                  style={{
                    width: '100%',
                  }}
                  loading={revokeLoader}
                >
                  {t('userManagementDetails.revoke')}
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Modal>
    </div>
  );
};

export default UserManagementDetails;
