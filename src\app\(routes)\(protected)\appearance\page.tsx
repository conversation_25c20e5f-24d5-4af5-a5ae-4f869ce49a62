'use client';

import AppearanceSettings from '@/components/settings/AppearanceSettings';

/**
 * Appearance Settings page component
 *
 * @page AppearanceSettingsPage
 * @description Page for managing theme, branding, and appearance settings
 * @returns {JSX.Element} The Appearance Settings page component
 *
 */
const AppearanceSettingsPage = () => {
  return <AppearanceSettings />;
};

export default AppearanceSettingsPage;
