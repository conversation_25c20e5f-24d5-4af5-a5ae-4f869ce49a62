'use client';

import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { Button, Card, Divider, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Text, Paragraph } = Typography;

interface PWADebugInfo {
  isInstalled: boolean;
  hasServiceWorker: boolean;
  hasManifest: boolean;
  isOnline: boolean;
  installPromptAvailable: boolean;
  userAgent: string;
  displayMode: string;
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWADebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<PWADebugInfo | null>(null);
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);

  useEffect(() => {
    const checkPWAStatus = () => {
      const info: PWADebugInfo = {
        isInstalled:
          window.matchMedia('(display-mode: standalone)').matches ||
          (window.navigator as { standalone?: boolean }).standalone === true,
        hasServiceWorker: 'serviceWorker' in navigator,
        hasManifest: !!document.querySelector('link[rel="manifest"]'),
        isOnline: navigator.onLine,
        installPromptAvailable: !!deferredPrompt,
        userAgent: navigator.userAgent,
        displayMode: window.matchMedia('(display-mode: standalone)').matches
          ? 'standalone'
          : 'browser',
      };
      setDebugInfo(info);
    };

    checkPWAStatus();

    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      checkPWAStatus();
    };

    const handleAppInstalled = () => {
      setDeferredPrompt(null);
      checkPWAStatus();
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', checkPWAStatus);
    window.addEventListener('offline', checkPWAStatus);

    return () => {
      window.removeEventListener(
        'beforeinstallprompt',
        handleBeforeInstallPrompt
      );
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', checkPWAStatus);
      window.removeEventListener('offline', checkPWAStatus);
    };
  }, [deferredPrompt]);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        await deferredPrompt.prompt();
        await deferredPrompt.userChoice;
        // Install prompt completed
        setDeferredPrompt(null);
      } catch {
        // Install prompt error handled
      }
    }
  };

  const StatusIcon: React.FC<{ status: boolean }> = ({ status }) =>
    status ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    );

  if (!debugInfo) {
    return <div>Loading PWA debug info...</div>;
  }

  return (
    <Card
      title='PWA Debug Information'
      style={{ margin: '20px', maxWidth: '600px' }}
    >
      <Space direction='vertical' style={{ width: '100%' }}>
        <div>
          <Title level={4}>PWA Status</Title>
          <Space direction='vertical'>
            <div>
              <StatusIcon status={debugInfo.isInstalled} />
              <Text style={{ marginLeft: 8 }}>
                App Installed: {debugInfo.isInstalled ? 'Yes' : 'No'}
              </Text>
            </div>
            <div>
              <StatusIcon status={debugInfo.hasServiceWorker} />
              <Text style={{ marginLeft: 8 }}>
                Service Worker Support:{' '}
                {debugInfo.hasServiceWorker ? 'Yes' : 'No'}
              </Text>
            </div>
            <div>
              <StatusIcon status={debugInfo.hasManifest} />
              <Text style={{ marginLeft: 8 }}>
                Manifest Found: {debugInfo.hasManifest ? 'Yes' : 'No'}
              </Text>
            </div>
            <div>
              <StatusIcon status={debugInfo.isOnline} />
              <Text style={{ marginLeft: 8 }}>
                Online Status: {debugInfo.isOnline ? 'Online' : 'Offline'}
              </Text>
            </div>
            <div>
              <StatusIcon status={debugInfo.installPromptAvailable} />
              <Text style={{ marginLeft: 8 }}>
                Install Prompt Available:{' '}
                {debugInfo.installPromptAvailable ? 'Yes' : 'No'}
              </Text>
            </div>
          </Space>
        </div>

        <Divider />

        <div>
          <Title level={4}>Environment Info</Title>
          <Paragraph>
            <Text strong>Display Mode:</Text> {debugInfo.displayMode}
          </Paragraph>
          <Paragraph>
            <Text strong>User Agent:</Text> {debugInfo.userAgent}
          </Paragraph>
        </div>

        <Divider />

        <div>
          <Title level={4}>Actions</Title>
          <Space>
            {debugInfo.installPromptAvailable && (
              <Button type='primary' onClick={handleInstallClick}>
                Trigger Install Prompt
              </Button>
            )}
            <Button
              onClick={() => window.location.reload()}
              icon={<InfoCircleOutlined />}
            >
              Refresh Status
            </Button>
          </Space>
        </div>

        <Divider />

        <div>
          <Title level={4}>Installation Instructions</Title>
          <Paragraph>
            <Text strong>Chrome/Edge:</Text> Look for the install icon in the
            address bar or use the three-dot menu → &quot;Install Seal my idea
            Trust Layer&quot;
          </Paragraph>
          <Paragraph>
            <Text strong>Firefox:</Text> Use the three-line menu → &quot;Install
            this site as an app&quot;
          </Paragraph>
          <Paragraph>
            <Text strong>Safari (iOS):</Text> Tap the share button → &quot;Add
            to Home Screen&quot;
          </Paragraph>
          <Paragraph>
            <Text strong>Chrome (Android):</Text> Tap the three-dot menu →
            &quot;Add to Home screen&quot;
          </Paragraph>
        </div>
      </Space>
    </Card>
  );
};

export default PWADebug;
