'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { colors } from '@/theme/antdTheme';
import React from 'react';
import styles from './StepIndicator.module.css';

interface Props {
  currentStep: number; // 0-indexed: 0 to 3
  totalSteps?: number;
  style?: React.CSSProperties;
}

const ProgressStepBar: React.FC<Props> = ({
  currentStep,
  totalSteps = 4,
  style = {},
}) => {
  const { theme } = useTheme();
  return (
    <div className={styles.wrapper} style={style}>
      {[...Array(totalSteps)].map((_, index) => (
        <div
          key={index}
          className={`${styles.segment} ${index <= currentStep ? styles.active : ''}`}
          style={{
            backgroundColor:
              index <= currentStep
                ? colors[theme].stepActive
                : colors[theme].stepInActive,
          }}
        />
      ))}
    </div>
  );
};

export default ProgressStepBar;
