'use client';

import AuthWrapper from '@/components/auth/AuthWrapper';
import { images } from '@/config/images';
import { useTheme } from '@/contexts/ThemeContext';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { Typography } from 'antd';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import styles from './inbox.module.css';

const { Title, Text } = Typography;

const CheckYourInboxPage: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // const searchParams = useSearchParams();
  // const email = searchParams.get('email') || ''; // For future use

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');

  return (
    <AuthWrapper>
      <div className={styles.logoSection}>
        <Image
          src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
          alt='Swiss Trust Layer'
          className={styles.logoImage}
          width={180}
          height={180}
          style={{
            width: xs ? '175px' : sm ? '190px' : '220px',
            height: 'auto',
          }}
        />
      </div>
      <div className={styles.content}>
        <Image
          src={images.mailLogo}
          alt='mail'
          className={styles.logoImage}
          width={180}
          height={180}
          style={{ width: '180px', height: 'auto', marginBottom: '10px' }}
        />
        <Title level={2} className={styles.formTitle}>
          {t('checkInbox.title')}{' '}
        </Title>
        <div
          style={{
            display: 'flex',
          }}
        >
          <Text className={styles.formSubtitle}>
            {t('checkInbox.subtitle')}{' '}
            <Link href='*' className={styles.signupLink}>
              <EMAIL>
            </Link>
            <br />
            {t('checkInbox.subtitle1')}
          </Text>
        </div>
        <Link href='*' className={styles.signupLink}>
          {t('checkInbox.resend')}
        </Link>
      </div>
    </AuthWrapper>
  );
};

export default CheckYourInboxPage;
