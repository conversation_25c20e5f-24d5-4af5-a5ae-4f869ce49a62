'use client';

import { Card, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import React from 'react';

import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { colors } from '@/theme/antdTheme';
import { AiOutlinePlus } from 'react-icons/ai';
import styles from './AddNewUserCard.module.css';

const { Text } = Typography;

/**
 * Props for CreateNewFolderCard component
 *
 * @interface CreateNewFolderCardProps
 * @property {function} onClick - Optional callback when card is clicked
 */
interface CreateNewFolderCardProps {
  onClick?: () => void;
}

/**
 * CreateNewFolderCard component for creating new folders
 *
 * @component CreateNewFolderCard
 * @param {CreateNewFolderCardProps} props - Component props
 * @returns {JSX.Element} Rendered create new folder card component
 *
 * @example
 * <CreateNewFolderCard onClick={() => router.push('/my-ideas/create-folder')} />
 *
 * @description
 * This component displays a dashed border card with a plus icon that allows users
 * to create new folders. It includes hover effects and navigates to the create folder page.
 */
const CreateNewFolderCard: React.FC<CreateNewFolderCardProps> = ({
  onClick,
}) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { theme } = useTheme();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      router.push('/my-ideas/create-folder');
    }
  };

  return (
    <Card className={styles.styledCard} onClick={handleClick}>
      <div className={styles.plusIcon}>
        <AiOutlinePlus
          style={{
            color: theme === 'dark' ? colors.white : colors.white,
            fontSize: 50,
          }}
        />
      </div>
      <Text className={styles.createText}>{t('users.createNewUser')}</Text>
      <Text className={styles.memberText}>{t('users.member')}</Text>
    </Card>
  );
};

export default CreateNewFolderCard;
