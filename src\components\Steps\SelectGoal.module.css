.container {
  width: 100%;
  margin: 0 auto;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.heading {
  font-size: 24px;
  font-weight: 600;
}

.goalList {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 32px;
}

.goalItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--color-selectable-item);
  padding: 16px;
  border-radius: 8px;
  cursor: pointer !important;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}
:global(html[data-theme='dark']) .goalItem {
  background-color: #f5f5f5;
}

:global(html[data-theme='dark']) .selected {
  background-color: var(--color-secondary);
}

.goalItem:hover {
  box-shadow: 0 0 8px var(--color-secondary);
  transition: all 0.3s ease;
}

.goalCardRow {
  gap: 16px;
  display: flex;
  align-items: center;
}

.selected {
  background-color: var(--color-secondary);
}

.goalText {
  font-size: 16px;
  font-weight: 500;
}

.icon {
  font-size: 20px;
}

.footer {
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
}
.label {
  color: #012458;
  font-family: var(--font-poppins) !important;
  font-size: 16px;
}
.checkIcon {
  background-color: #0cd857;
  border-radius: 50%;
  padding: 5px 8px;
  font-size: 10px;
  color: white;
  font-weight: bold;
}

.unCheckIcon {
  background-color: #fff;
  border-radius: 50%;
  padding: 5px 8px;
  font-size: 10px;
  color: #ffff;
  font-weight: bold;
  box-shadow: 0px 9px 14px 0px rgba(0, 0, 0, 0.14);
}
@media screen and (max-width: 480px) {
  .goalItem {
    flex-direction: row;
    align-items: center;
    gap: 8px;
    padding: 10px;
  }

  .goalCardRow {
    gap: 12px;
    display: flex;
    align-items: center;
  }

  .footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .button {
    width: 100%;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .skipButton {
    align-self: flex-end;
  }

  .label {
    font-size: 14px !important;
  }
}
