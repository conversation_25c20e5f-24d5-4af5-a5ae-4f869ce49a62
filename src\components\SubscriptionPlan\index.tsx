import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Col, Row, Spin } from 'antd';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useNotification } from '../Notification/NotificationContext';
import PlanCard from './PlanCard';
import styles from './subscriptionPlan.module.css';

interface Plans {
  title: string;
  amount: number;
  description: string;
  image: string;
  price_id: string;
  product_id: string;
  is_active: boolean;
  features: string[];
  storage_limit_mb: number;
  api_call_credit: number;
  price_monthly: number;
  type: string;
  _id: string;
  quantity: number;
  from: string;
  created_by: string;
  createdAt: Date;
  updatedAt: Date;
}

const SubscriptionPlans = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const notification = useNotification();
  const [plans, setPlans] = useState<Plans[]>([]);
  const [loader, setLoader] = useState('');
  const { user } = useAppSelector(state => state.auth);
  const [activeIndex, setActiveIndex] = useState<number | null>(0);
  console.log('user ==>', user);

  const generateLink = async (plan: Plans) => {
    setLoader(plan?._id);
    try {
      const res = await getApiData<{ plan_id: string }, ApiResponse>({
        url: Endpoints.getSubscriptionLink,
        method: 'POST',
        data: {
          plan_id: plan._id,
        },
      });
      if (res && res.status === true && res.data) {
        if (res.data?.payment_url) {
          // router.push(res.data.payment_url); // ✅ Works for internal routes
          window.location.href = res.data.payment_url;
        }
      } else {
        notification.error({
          message: res?.message || 'Error generating link',
        });
      }
      setLoader('');
    } catch (error) {
      setLoader('');
      notification.error({
        message: 'Error generating link',
      });
    }
  };

  const getPlans = async () => {
    try {
      const res = await getApiData<{ type: string }, ApiResponse>({
        url: `${Endpoints.getSealPlans}?type=subscription`,
        method: 'GET',
      });
      if (res && res.status === true && res.data) {
        console.log('res', res);
        setPlans(res.data);
      } else {
        notification.error({
          message: res?.message || 'Error fetching plans',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Error fetching plans',
      });
    }
  };

  useEffect(() => {
    getPlans();
    return () => {};
  }, []);

  return (
    <div className={styles.main}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.replace('/settings');
            }}
          >
            {t('common.back')}
          </Button>
        </div>
        <h2 className={styles.heading}>{t('subscriptionPlan.title')}</h2>
        <Row gutter={[24, 24]} justify='center' className={styles.parent}>
          {plans?.map((plan, index) => (
            <Col key={index} xs={24} sm={24} md={12} lg={12} xl={12}>
              <PlanCard
                title={plan.title}
                price={plan.price_monthly}
                perMonthLabel={t('subscriptionPlan.per_month')}
                description={plan.storage_limit_mb}
                isActive={activeIndex === index}
                onClick={() => {
                  if (loader) {
                    return;
                  }
                  generateLink(plan);
                }}
                titleRight={
                  loader === plan._id ? <Spin size='small' /> : <div />
                }
              />
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

export default SubscriptionPlans;
