'use client';

import { BellOutlined } from '@ant-design/icons';
import { notification, Typography } from 'antd';
import { onMessage } from 'firebase/messaging';
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
} from 'react';
import {
  getFirebaseMessaging,
  isMessagingInitialized,
} from '../../notifications/firebase';
import { colors } from '../../theme/antdTheme';

const { Title, Text } = Typography;

interface NotificationPayload {
  notification?: {
    title?: string;
    body?: string;
    icon?: string;
    image?: string;
  };
  data?: {
    [key: string]: string;
  };
}

interface NotificationContextType {
  sendNotification: (payload: NotificationPayload) => void;
  showInAppNotification: (
    title: string,
    body: string,
    type?: 'success' | 'info' | 'warning' | 'error'
  ) => void;
  isFirebaseReady: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const NotificationFirebaseProvider: React.FC<{
  children: ReactNode;
}> = ({ children }) => {
  const [api, contextHolder] = notification.useNotification();
  const [isFirebaseReady, setIsFirebaseReady] = React.useState(false);

  // Check Firebase messaging status
  useEffect(() => {
    const checkFirebaseStatus = () => {
      const ready = isMessagingInitialized();
      setIsFirebaseReady(ready);
      console.log('🔍 Firebase messaging ready:', ready);
    };

    checkFirebaseStatus();

    // Check periodically until Firebase is ready
    const interval = setInterval(() => {
      if (!isFirebaseReady) {
        checkFirebaseStatus();
      } else {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isFirebaseReady]);

  const playNotificationSound = useCallback(() => {
    try {
      const audio = new Audio('/notification.mp3');
      audio.volume = 0.5; // Set volume to 50%
      audio.play().catch(err => {
        console.warn('🔇 Unable to play notification sound:', err);
      });
    } catch (error) {
      console.warn('🔇 Audio not supported:', error);
    }
  }, []);

  const showInAppNotification = useCallback(
    (
      title: string,
      body: string,
      type: 'success' | 'info' | 'warning' | 'error' = 'info'
    ) => {
      playNotificationSound();

      const notificationConfig = {
        message: (
          <Title
            level={4}
            style={{
              color: colors.primary,
              fontSize: 18,
              fontWeight: 'bold',
              margin: 0,
            }}
          >
            {title}
          </Title>
        ),
        description: (
          <Text
            style={{
              color: colors.light.textSecondary,
              fontSize: 14,
              lineHeight: 1.4,
            }}
          >
            {body}
          </Text>
        ),
        icon: <BellOutlined style={{ color: colors.primary, fontSize: 24 }} />,
        placement: 'topRight' as const,
        duration: 6,
      };

      switch (type) {
        case 'success':
          api.success(notificationConfig);
          break;
        case 'warning':
          api.warning(notificationConfig);
          break;
        case 'error':
          api.error(notificationConfig);
          break;
        default:
          api.info(notificationConfig);
      }
    },
    [api, playNotificationSound]
  );

  const sendNotification = useCallback(
    (payload: NotificationPayload) => {
      const title = payload?.notification?.title || 'Notification';
      const body = payload?.notification?.body || '';

      console.log('📱 Showing in-app notification:', { title, body });
      showInAppNotification(title, body, 'info');
    },
    [showInAppNotification]
  );
  // Set up Firebase messaging listener
  useEffect(() => {
    if (!isFirebaseReady) {
      console.log('⏳ Waiting for Firebase messaging to be ready...');
      return;
    }

    const setupMessagingListener = async () => {
      const messagingInstance = getFirebaseMessaging();
      if (!messagingInstance) {
        console.log('❌ Messaging instance not available');
        return;
      }

      console.log('🔗 Setting up Firebase messaging listener...');

      const unsubscribe = onMessage(messagingInstance, payload => {
        console.log('📩 Foreground Firebase message received:', payload);
        sendNotification(payload as NotificationPayload);
      });

      return unsubscribe;
    };

    let unsubscribe: (() => void) | undefined;
    setupMessagingListener().then(unsub => {
      unsubscribe = unsub;
    });

    // Listen for service worker messages
    const handleServiceWorkerMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'FCM_MESSAGE') {
        console.log(
          '📩 Service worker FCM message received:',
          event.data.payload
        );
        sendNotification(event.data.payload as NotificationPayload);
      }
    };

    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener(
        'message',
        handleServiceWorkerMessage
      );
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener(
          'message',
          handleServiceWorkerMessage
        );
      }
    };
  }, [isFirebaseReady, sendNotification]);

  const contextValue: NotificationContextType = {
    sendNotification,
    showInAppNotification,
    isFirebaseReady,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {contextHolder}
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      'useNotificationContext must be used within NotificationFirebaseProvider'
    );
  }
  return context;
};
