export const Endpoints = {
  signup: '/seal-my-idea/v1/signup',
  verifyOtp: '/seal-my-idea/v1/verify-otp',
  resendOtp: '/seal-my-idea/v1/resend-otp',
  forgotPassword: '/seal-my-idea/v1/forgot-password',
  changePassword: '/seal-my-idea/v1/change-password',
  login: '/seal-my-idea/v1/login',
  updateUserDetails: '/seal-my-idea/v1/update-user-details',
  goalList: '/seal-my-idea/v1/goal/all',
  interestList: '/seal-my-idea/v1/interest/all',
  skipScreen: '/seal-my-idea/v1/skip-screen',
  socialLogin: '/seal-my-idea/v1/social-signup',
  enable2FA: '/seal-my-idea/v1/enable-2fa',
  verify2FA: '/seal-my-idea/v1/verify-2fa',
  login2FA: '/seal-my-idea/v1/login-2fa',
  updateProfile: '/seal-my-idea/v1/users-settings/update-user-profile',
  verify2FAOtp: '/seal-my-idea/v1/verify-2fa-otp',
  activityLogs: '/seal-my-idea/v1/activity-log',
  updateNotificationPermission:
    '/seal-my-idea/v1/update-notification-permission',
  notificationList: '/seal-my-idea/v1/notifications/list',
  deleteSingleNotification:
    '/seal-my-idea/v1/notifications/delete-notification/',
  deleteAllNotification:
    '/seal-my-idea/v1/notifications/delete-all-notifications',
  readNotification: '/seal-my-idea/v1/notifications/read-unread',

  // Folders
  createFolder: '/seal-my-idea/v1/folder/create',
  getFolderList: '/seal-my-idea/v1/folder/list',
  deleteFolder: '/seal-my-idea/v1/folder/delete',
  updateFolder: '/seal-my-idea/v1/folder/update',
  fileSuccess: '/seal-my-idea/v1/update-chunk-upload-info',
  getFolderById: '/seal-my-idea/v1/folder/get-folder',
  saveFolder: '/seal-my-idea/v1/folder/saved-folder',
  inviteMember: '/seal-my-idea/v1/shared-members/invite-members',
  joinInvite: '/seal-my-idea/v1/shared-members/accept-invite',
  inviteList: '/seal-my-idea/v1/shared-members/folder-members-list',
  removeUserAccess: '/seal-my-idea/v1/shared-members/remove-member-sharing',
  deleteFile: '/seal-my-idea/v1/folder/delete-single-folder-file',

  // Upload
  getSasToken: '/seal-my-idea/v1/get-sas-url',
  userAccountDetails: '/seal-my-idea/v1/users-settings/get-user-account/',
  updateCurrentPassword: '/seal-my-idea/v1/users-settings/change-user-password',
  verifyCertificate: '/seal-my-idea/v1/folder/sealed-document-certificate',

  // Stripe
  createCheckoutSession: '/seal-my-idea/v1/stripe/generate-stripe-link',
  checkPaymentStatus: '/seal-my-idea/v1/stripe/check-payment-status',
  getPaymentDetails: '/seal-my-idea/v1/stripe/get-sesion-data-by-session-id',

  getSealPlans: '/seal-my-idea/v1/stripe/get-plan-list',
  downloadReceipt: '/seal-my-idea/v1/stripe/download-invoice',

  // SUBSCRIPTIONS
  getSubscriptionLink: '/seal-my-idea/v1/stripe/generate-subscription-link',
  resumeOrCancelSubscription:
    '/seal-my-idea/v1/stripe/resume-or-cancel-subscription',
  upgradeSubscription: '/seal-my-idea/v1/stripe/upgrade-plan',

  // Seal apis
  sealFolder: '/seal-my-idea/v1/folder/seal',
  downloadFile: '/seal-my-idea/v1/folder/download-zip',
  sealedDocumentCertificate:
    '/seal-my-idea/v1/folder/sealed-document-certificate',
  saveFcmToken: '/seal-my-idea/v1/save-fcm-token',

  transactionHistory: '/seal-my-idea/v1/stripe/get-transaction-list',

  // user data
  userData: '/seal-my-idea/v1/get-user-data',

  // user management
  usersList: '/seal-my-idea/v1/shared-members/all-folder-members-list',
  removeUserFromList:
    '/seal-my-idea/v1/shared-members/remove-member-all-folder',
  sharingFolderHistory:
    '/seal-my-idea/v1/shared-members/sharing-folder-history',
  changeFolderAccessRole: '/seal-my-idea/v1/shared-members/access-role-change',
  revokeAccess: '/seal-my-idea/v1/shared-members/remove-member-sharing',
  folderList: '/seal-my-idea/v1/shared-members/sharing-folder-list',

  // Dashboard Data
  dashboardData: '/seal-my-idea/v1/user-dashboard-data',
};
