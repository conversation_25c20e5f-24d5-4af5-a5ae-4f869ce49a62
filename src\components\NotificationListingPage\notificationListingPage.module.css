.container {
  /* max-width: 700px; */
  margin: auto;
  padding: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  row-gap: 8px;
}
.title {
  font-size: 25px;
}
.card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  margin-bottom: 10px;
  background: #fff;
  transition: transform 0.3s ease;
  width: 100%;
}

.icon {
  margin-right: 22px;
  font-size: 31px;
}
.parent {
  width: 100%;
}

.content {
  flex: 1;
  color: black;
}

.content p {
  margin: 0;
  font-size: 13px;
  color: black;
  padding: 10px 0px;
}
.content strong {
  padding: 10px 0px;
}
.notificationHeader {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap; /* allows wrapping */
  margin-bottom: 12px;
  margin-left: auto;
  justify-content: flex-end;
}

.notificationHeader > div {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch {
  background-color: var(--color-secondary) !important;
}
.switchDisable {
  background-color: #8f8a8a !important;
}
.time {
  font-size: 12px;
  color: #555;
  margin-left: 8px;
  white-space: nowrap;
}

.seal_process_sucess {
  background-color: #d4f8d4;
}

.seal_process_failed,
.remove_member_for_idea {
  background-color: #f8d4d4;
}

.subscription_added {
  background-color: #fff2cc;
}
.invite_members_for_idea,
.payment_sent,
.done_accept_members_for_idea {
  background-color: #d4ecf8;
}

.deleteAllBtn {
  color: white !important;
  background: #dc4446;
  padding: 20px !important;
  font-size: 13px;
}
.deleteAction {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(232, 64, 64);
  color: white;
  height: 90%;
  margin-left: 10px;
}

.unreadDot {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 10px;
  height: 10px;
  background-color: #1890ff; /* Ant Design primary blue */
  border-radius: 50%;
}
.empty {
  text-align: center;
  margin-top: 160px;
  font-weight: bold;
  font-size: 20px;
  color: var(--color-text-primary) !important;
  flex-direction: column !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.emptyBell {
  font-size: 60px !important;
  color: var(--color-text-primary) !important;
}

@media (max-width: 576px) {
  .title {
    font-size: 20px;
  }
  .header {
    gap: 8px;
  }
  .notificationHeader {
    width: 100%;
    justify-content: space-between;
  }
  .notificationHeader > div span {
    font-size: 12px;
  }
  .deleteAllBtn {
    font-size: 10px;
    padding: 0px 5px !important;
  }
  .content strong {
    font-size: 12px;
  }
  .content p {
    font-size: 11px;
  }
  .emptyBell {
    font-size: 30px !important;
  }
  .empty {
    font-size: 14px;
  }
}
