/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setLoading, setTranslations } from '@/store/slices/languageSlice';
import { useEffect } from 'react';

interface TranslationProviderProps {
  children: React.ReactNode;
}

const TranslationProvider: React.FC<TranslationProviderProps> = ({
  children,
}) => {
  const dispatch = useAppDispatch();
  const { currentLanguage, translations } = useAppSelector(
    state => state.language
  );

  useEffect(() => {
    const initializeTranslations = async () => {
      // Load default language if not already loaded
      if (!translations[currentLanguage]) {
        dispatch(setLoading(true));
        try {
          const response = await fetch(`/locales/${currentLanguage}.json`);
          if (!response.ok) {
            throw new Error(`Failed to fetch translations: ${response.status}`);
          }
          const data = await response.json();

          dispatch(
            setTranslations({
              ...translations,
              [currentLanguage]: data,
            })
          );
        } catch (error) {
          console.error('Failed to initialize translations:', error);
        } finally {
          dispatch(setLoading(false));
        }
      }
    };

    initializeTranslations();
  }, [currentLanguage, dispatch]); // Avoid infinite re-renders by not including translations

  return <>{children}</>;
};

export default TranslationProvider;
