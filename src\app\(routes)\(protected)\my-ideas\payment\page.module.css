.container {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.light {
  color: var(--color-text-primary);
}

.dark {
  color: var(--color-text-primary);
}

.card {
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  max-width: 400px;
  width: 100%;
  background-color: inherit;
}

.title {
  font-size: 1.5rem;
  margin-top: 1rem;
}

.subtitle {
  font-size: 1rem;
  opacity: 0.8;
}

.loader {
  width: 48px;
  height: 48px;
  border: 4px solid transparent;
  border-top: 4px solid currentColor;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
