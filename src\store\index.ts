/**
 * @fileoverview Redux store configuration with persistence for SealMyIdea application
 * @module store/index
 * @description Central Redux store setup with redux-persist for state persistence
 * across browser sessions and SSR compatibility.
 */

import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import authSlice from './slices/authSlice';
import languageReducer from './slices/languageSlice';

/**
 * Creates a no-op storage implementation for server-side rendering
 * This prevents hydration mismatches when redux-persist runs on the server
 *
 * @function createNoopStorage
 * @returns {Object} Storage-like object with promise-based methods
 */
const createNoopStorage = () => {
  return {
    getItem(): Promise<null> {
      return Promise.resolve(null);
    },
    setItem(_key: string, value: unknown): Promise<unknown> {
      return Promise.resolve(value);
    },
    removeItem(): Promise<void> {
      return Promise.resolve();
    },
  };
};

/**
 * Storage implementation that switches between localStorage (client) and no-op (server)
 * @constant {Object}
 */
const storage =
  typeof window !== 'undefined'
    ? // eslint-disable-next-line @typescript-eslint/no-require-imports
      require('redux-persist/lib/storage').default
    : createNoopStorage();

/**
 * Redux-persist configuration object
 * @constant {Object}
 * @property {string} key - Storage key prefix
 * @property {Object} storage - Storage implementation
 * @property {string[]} whitelist - Slices to persist
 */
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'language'], // Persist auth and language slices
};

/**
 * Root reducer combining all application slices
 * @constant {Function}
 */
const rootReducer = combineReducers({
  auth: authSlice,
  language: languageReducer,
});

/**
 * Persisted reducer with redux-persist wrapper
 * @constant {Function}
 */
const persistedReducer = persistReducer(persistConfig, rootReducer);

/**
 * Configured Redux store with persistence and middleware
 * @constant {Object}
 */
export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

/**
 * Redux-persist persistor for managing rehydration
 * @constant {Object}
 */
export const persistor = persistStore(store);

/**
 * Root state type derived from store
 * @typedef {ReturnType<typeof store.getState>} RootState
 */
export type RootState = ReturnType<typeof store.getState>;

/**
 * App dispatch type derived from store
 * @typedef {typeof store.dispatch} AppDispatch
 */
export type AppDispatch = typeof store.dispatch;
