.content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 79vh;
}

.logoSection {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.formTitle {
  text-align: center !important;
  margin-bottom: 4px !important;
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 46px !important;
  font-family: var(--font-radio-canada);
  margin-top: 10px;
}

.formSubtitle {
  display: block;
  text-align: center;
  margin-bottom: 10px;
  color: var(--subtitle) !important;
  font-size: 18px;
  line-height: 1.4;
  font-family: var(--font-poppins);
  font-weight: 300;
  margin-top: 0px;
}
.mailIcon {
  font-size: 143px;
  color: #8edafe;
}
.signupLink {
  font-weight: 500;
  color: var(--button-primary);
}

:global(html[data-theme='dark']) .signupLink {
  color: var(--button-bg);
}

@media (max-width: 768px) {
  .formTitle {
    font-size: 40px !important;
  }

  .formSubtitle {
    font-size: 16px !important;
  }
}

@media (max-width: 480px) {
  .formTitle {
    font-size: 24px !important;
  }
  .formSubtitle {
    font-size: 12px !important;
    margin-top: 5px;
  }
  .mailIcon {
    height: 100px;
    margin-bottom: 20px;
  }
  .content {
    justify-content: flex-start;
    flex-direction: column;
    margin-top: 50px;
  }
}

@media (max-width: 320px) {
  .formTitle {
    font-size: 20px !important;
  }
}

@media (orientation: landscape) and (max-height: 600px) {
  .logoSection {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 16px;
  }

  .formTitle {
    font-size: 22px !important;
  }
  .formSubtitle {
    margin-bottom: 16px;
  }
}
