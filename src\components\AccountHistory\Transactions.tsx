'use client';

import { Endpoints } from '@/config/endpoints';
import { getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import {
  ArrowLeftOutlined,
  CopyOutlined,
  DownloadOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Button, Empty, Input, Row, Spin, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { downloadFromInternalApi } from '../../utils/CommonFunctions';
import { useNotification } from '../Notification/NotificationContext';
import ResponsiveTable from '../ResponsiveTable/ResponsiveTable';
import styles from './accountHistory.module.css';

const { Title, Text } = Typography;

interface HistoryItem {
  date: string;
  action: string;
  status: string;
  by_user: string;
  payment_link: {
    type: string;
    amount: number;
    currency: string;
  };
  invoice_url: string;
  _id: string;
  createdAt: string;
}

interface ActivityLogParams {
  page: number;
  search?: string;
}

interface ApiPagination {
  totalCount: number;
  pageSize: number;
  totalPage: number;
  currentPage: number;
  isMore: boolean;
}

interface ActivityLogApiRecord {
  action: string;
  description: string;
  date: string;
  by_user: string;
  pagination: ApiPagination;
  items: HistoryItem[];
}

interface ActivityLogResponse {
  status: boolean;
  data: ActivityLogApiRecord;
  pagination: ApiPagination;
}

interface ApiError {
  status: false;
  message: string;
}

export default function Transactions() {
  const router = useRouter();
  const { t } = useTranslation();
  const notification = useNotification();

  const [data, setData] = useState<HistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [downloadLoader, setDownloadLoader] = useState('');

  const handleCopy = async (transactionId: string) => {
    await navigator.clipboard.writeText(transactionId);
    notification.success({
      message: 'Copied',
      description: 'Transaction ID copied to clipboard',
    });
  };

  const fetchData = async (pageNum: number, searchVal?: string) => {
    setLoading(true);
    try {
      const payload: ActivityLogParams = { page: pageNum };
      if (searchVal && searchVal.trim() !== '') {
        payload.search = searchVal.trim();
      }

      const res = await getApiData<
        ActivityLogParams,
        ActivityLogResponse | ApiError
      >({
        url: Endpoints.transactionHistory,
        method: 'GET',
        data: payload,
      });

      if (res && 'data' in res && res.status) {
        setData(res.data?.items);
        setTotal(res?.data?.pagination?.totalCount || 0);
        setPageSize(res?.data?.pagination?.pageSize || 10);
      } else {
        setData([]);
        setTotal(0);
      }
    } catch (err) {
      console.error('❌ Error fetching account history:', err);
      notification.error({
        message: 'Error',
        description: 'Failed to fetch transaction history',
      });
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // Fetch when page/search changes
  useEffect(() => {
    fetchData(page, search);
  }, [page, search]);

  const columns = [
    {
      title: t('accountHistory.date_time'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 250,
      sorter: (a: HistoryItem, b: HistoryItem) => {
        return dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix();
      },
      render: (val: string) => dayjs(val).format('MMM DD, YYYY hh:mm A'),
    },
    {
      title: t('receipt.amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (val: string) => <Text>${val}</Text>,
    },
    {
      title: t('accountHistory.action'),
      dataIndex: 'type',
      key: 'type',
      render: (val: string, data: HistoryItem) => (
        <Text>{data?.payment_link?.type}</Text>
      ),
    },
    {
      title: t('draftIdea.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'succeeded' ? 'green' : 'red'}>{status}</Tag>
      ),
    },
    {
      title: t('common.transaction_id'),
      dataIndex: 'stripe_transaction_id',
      key: 'stripe_transaction_id',
      render: (val: string) => (
        <Row>
          <Text
            onClick={() => {
              handleCopy(val);
            }}
          >
            {val || '-'}
          </Text>
          {val && (
            <CopyOutlined
              onClick={() => {
                handleCopy(val);
              }}
              style={{ cursor: 'pointer' }}
            />
          )}
        </Row>
      ),
    },
    {
      title: t('common.invoice'),
      dataIndex: 'by_user',
      key: 'by_user',
      width: 100,
      render: (_: string, record: HistoryItem) => (
        <Text
          className={styles.link}
          onClick={async () => {
            if (downloadLoader === record._id) {
              return;
            }
            setDownloadLoader(record._id);
            const res = await downloadFromInternalApi(record.invoice_url);
            if (!res?.status) {
              notification.error({
                message: res?.message || 'Error downloading invoice',
              });
            }
            setDownloadLoader('');
          }}
        >
          {downloadLoader === record._id ? (
            <Spin size='small' />
          ) : (
            <DownloadOutlined />
          )}
        </Text>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Button
        type='link'
        icon={<ArrowLeftOutlined />}
        className={styles.backButton}
        onClick={() => router.back()}
      >
        {t('common.back')}
      </Button>

      <Title level={2} className={styles.heading}>
        {t('transactions.history')}
      </Title>
      <Text className={styles.subheading}>
        {t('transactions.track_all_history')}
      </Text>

      <div className={styles.searchWrapper}>
        <Input
          placeholder={`${t('transactions.search')}...`}
          prefix={<SearchOutlined />}
          className={styles.searchInput}
          value={search}
          onChange={e => {
            setSearch(e.target.value);
            setPage(1);
          }}
          allowClear
        />
      </div>

      {/* <Table
        rowKey='_id'
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{
          current: page,
          pageSize,
          total,
          onChange: p => setPage(p),
          showSizeChanger: false,
        }}
        locale={{
          emptyText: (
            <Empty description={t('transactions.no_transactions_found')} />
          ),
        }}
      /> */}
      <ResponsiveTable
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{
          current: page,
          pageSize,
          total,
          onChange: p => setPage(p),
          showSizeChanger: false,
        }}
        locale={{
          emptyText: (
            <Empty description={t('transactions.no_transactions_found')} />
          ),
        }}
      />
    </div>
  );
}
