{"name": "SealMyIdea", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "startAzure": "node_modules/next/dist/bin/next start -p 80", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "check-all": "npm run type-check && npm run lint && npm run format:check && npm run test", "prepare": "husky", "docs": "jsdoc -c jsdoc.config.json", "docs:serve": "npm run docs && npx http-server docs -p 8080 -o", "docs:clean": "<PERSON><PERSON><PERSON> docs"}, "dependencies": {"@ant-design/nextjs-registry": "^1.1.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@types/styled-components": "^5.1.34", "antd": "^5.26.6", "axios": "^1.11.0", "chart.js": "^4.5.0", "dayjs": "^1.11.13", "firebase": "^12.1.0", "google-libphonenumber": "^3.2.42", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "lottie-react": "^2.4.1", "next": "15.4.2", "next-pwa": "^5.6.0", "react": "19.1.0", "react-apple-signin-auth": "^1.1.2", "react-chartjs-2": "^5.3.0", "react-device-detect": "^2.2.3", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-international-phone": "^4.6.0", "react-linkedin-login-oauth2": "^2.0.1", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-swipeable-list": "^1.10.0", "redux-persist": "^6.0.0", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/google-libphonenumber": "^7.4.30", "@types/jest": "^30.0.0", "@types/lodash-es": "^4.17.12", "@types/minimatch": "^5.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "better-docs": "^2.7.3", "clean-jsdoc-theme": "^4.3.0", "eslint": "^9", "eslint-config-next": "15.4.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "http-server": "^14.1.1", "husky": "^9.1.7", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jsdoc": "^4.0.4", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "rimraf": "^6.0.1", "sharp": "^0.34.3", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}