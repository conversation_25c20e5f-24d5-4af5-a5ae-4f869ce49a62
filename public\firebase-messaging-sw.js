// Firebase Messaging Service Worker
// This service worker handles background push notifications when the app is not in focus

console.log('🔧 Firebase Messaging Service Worker loading...');

// Import Firebase scripts
importScripts(
  'https://www.gstatic.com/firebasejs/10.13.2/firebase-app-compat.js'
);
importScripts(
  'https://www.gstatic.com/firebasejs/10.13.2/firebase-messaging-compat.js'
);

// Firebase configuration
const firebaseConfig = {
  apiKey: 'AIzaSyDnQuITJw2uVkofT4O95omZviFTriflLlU',
  authDomain: 'swiss-trust-layer.firebaseapp.com',
  projectId: 'swiss-trust-layer',
  storageBucket: 'swiss-trust-layer.firebasestorage.app',
  messagingSenderId: 815385848837,
  appId: '1:815385848837:web:ba65ccff720f8e0950c06c',
  measurementId: 'G-P9PT3507MY',
};

// Initialize Firebase
try {
  firebase.initializeApp(firebaseConfig);
  console.log('✅ Firebase initialized in service worker');
} catch (error) {
  console.error('❌ Error initializing Firebase in service worker:', error);
}

// Get messaging instance
let messaging;
try {
  messaging = firebase.messaging();
  console.log('✅ Firebase messaging instance created');
} catch (error) {
  console.error('❌ Error creating messaging instance:', error);
}
// Handle background messages (when app is not in focus)
if (messaging) {
  messaging.onBackgroundMessage(payload => {
    console.log('📩 Background message received:', payload);

    try {
      const notificationTitle = payload?.notification?.title || 'Seal My Idea';
      const notificationBody =
        payload?.notification?.body || 'You have a new notification';
      const notificationIcon =
        payload?.notification?.icon || '/icons/icon-192x192.png';
      const notificationImage = payload?.notification?.image;
      const notificationTag = payload?.data?.tag || 'default';
      const notificationUrl = payload?.data?.url || '/dashboard';

      const notificationOptions = {
        body: notificationBody,
        icon: notificationIcon,
        image: notificationImage,
        tag: notificationTag,
        badge: '/icons/icon-72x72.png',
        vibrate: [200, 100, 200],
        requireInteraction: true,
        actions: [
          {
            action: 'open',
            title: 'Open App',
            icon: '/icons/icon-72x72.png',
          },
          {
            action: 'close',
            title: 'Close',
            icon: '/icons/icon-72x72.png',
          },
        ],
        data: {
          url: notificationUrl,
          timestamp: Date.now(),
          ...payload.data,
        },
      };

      // Show the notification
      self.registration.showNotification(
        notificationTitle,
        notificationOptions
      );

      // Play notification sound (if supported)
      try {
        const audio = new Audio('/notification.mp3');
        audio.play().catch(err => {
          console.warn('🔇 Unable to play notification sound:', err);
        });
      } catch (audioError) {
        console.warn('🔇 Audio not supported in service worker');
      }

      // Send message to main thread
      self.clients
        .matchAll({ includeUncontrolled: true, type: 'window' })
        .then(clients => {
          clients.forEach(client => {
            client.postMessage({
              type: 'FCM_MESSAGE',
              payload: payload,
            });
          });
        });
    } catch (error) {
      console.error('❌ Error handling background message:', error);
    }
  });
} else {
  console.warn('⚠️ Messaging instance not available for background messages');
}

// Test function for background notifications
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'TEST_BACKGROUND_NOTIFICATION') {
    console.log('🧪 Testing background notification...');

    const testNotification = {
      title: 'Test Background Notification',
      body: 'This is a test to verify background notifications are working.',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      tag: 'test',
      data: {
        url: '/dashboard',
        timestamp: Date.now(),
      },
    };

    self.registration.showNotification(testNotification.title, {
      body: testNotification.body,
      icon: testNotification.icon,
      badge: testNotification.badge,
      tag: testNotification.tag,
      data: testNotification.data,
      requireInteraction: true,
    });
  }
});

// Handle notification click events
self.addEventListener('notificationclick', event => {
  console.log('🖱️ Notification clicked:', event.notification);

  event.notification.close();

  const action = event.action;
  const notificationData = event.notification.data;
  const url = notificationData?.url || '/dashboard';

  if (action === 'close') {
    return;
  }

  // Open or focus the app
  event.waitUntil(
    self.clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then(clients => {
        // Check if there's already a window/tab open with the target URL
        for (const client of clients) {
          if (client.url.includes(url) && 'focus' in client) {
            return client.focus();
          }
        }

        // If no window/tab is open, open a new one
        if (self.clients.openWindow) {
          return self.clients.openWindow(url);
        }
      })
  );
});

// Handle notification close events
self.addEventListener('notificationclose', event => {
  console.log('🔔 Notification closed:', event.notification);

  // Track notification close analytics if needed
  const notificationData = event.notification.data;
  if (notificationData?.trackClose) {
    // Send analytics event
    console.log('📊 Tracking notification close');
  }
});
