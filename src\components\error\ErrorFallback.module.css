/* @keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(10px);
  }
} */

/* @keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} */

.card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  /* box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2); */
  width: 100%;
  max-width: 500px;
  text-align: center;
  margin: auto;
  animation: fadeIn 0.6s ease-out;
}

.icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: shake 0.5s ease-in-out;
}

.title {
  color: #e74c3c;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.message {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.primary,
.secondary,
.danger {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.secondary {
  background: #f8f9fa;
  color: #495057;
  border: 2px solid #dee2e6;
}

.secondary:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(231, 76, 60, 0.3);
}

.danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.details {
  margin-top: 2rem;
  text-align: left;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.details summary {
  cursor: pointer;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.details pre {
  background: #343a40;
  color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}
