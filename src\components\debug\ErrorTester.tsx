'use client';

import React, { useState } from 'react';
import styled from 'styled-components';

const TestContainer = styled.div`
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
`;

const TestButton = styled.button`
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: #c0392b;
    transform: translateY(-2px);
  }
`;

const ErrorTester: React.FC = () => {
  const [shouldThrow, setShouldThrow] = useState(false);

  const throwError = (): void => {
    setShouldThrow(true);
  };

  if (shouldThrow) {
    throw new Error('Test error thrown by ErrorTester component');
  }

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <TestContainer>
      <TestButton onClick={throwError}>Test Error Boundary</TestButton>
    </TestContainer>
  );
};

export default ErrorTester;
