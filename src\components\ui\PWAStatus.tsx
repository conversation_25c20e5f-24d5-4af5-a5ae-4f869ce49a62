'use client';

import {
  CheckCircleOutlined,
  ReloadOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import { Button, message, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Text } = Typography;

const PWAStatus: React.FC = () => {
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  useEffect(() => {
    // Check if app is installed
    const checkInstallStatus = () => {
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
      } else if (
        (window.navigator as { standalone?: boolean }).standalone === true
      ) {
        setIsInstalled(true);
      }
    };

    // Check online status
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    // Check for service worker updates
    const checkForUpdates = () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          setUpdateAvailable(true);
        });
      }
    };

    checkInstallStatus();
    updateOnlineStatus();
    checkForUpdates();

    // Listen for online/offline events
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Seal my idea',
          text: 'Check out Seal my idea - Secure Authentication Platform',
          url: window.location.origin,
        });
      } catch {
        // User cancelled sharing or error occurred
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(window.location.origin);
        message.success('Link copied to clipboard!');
      } catch {
        message.error('Failed to copy link');
      }
    }
  };

  const handleUpdate = () => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then(registration => {
        if (registration?.waiting) {
          registration.waiting.postMessage({ type: 'SKIP_WAITING' });
          window.location.reload();
        }
      });
    }
  };

  // Only show status if app is installed
  if (!isInstalled) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        alignItems: 'flex-end',
      }}
    >
      {/* Online/Offline Status */}
      <div
        style={{
          background: isOnline ? '#52c41a' : '#ff4d4f',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '12px',
          fontSize: '12px',
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
        }}
      >
        <div
          style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            background: 'white',
          }}
        />
        <Text style={{ color: 'white', fontSize: '12px', margin: 0 }}>
          {isOnline ? 'Online' : 'Offline'}
        </Text>
      </div>

      {/* PWA Installed Status */}
      <div
        style={{
          background: 'var(--card-bg, #ffffff)',
          border: '1px solid var(--border-color, #e9ecef)',
          borderRadius: '8px',
          padding: '8px 12px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        }}
      >
        <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
        <Text style={{ fontSize: '12px', margin: 0 }}>App Installed</Text>
        <Button
          type='text'
          size='small'
          icon={<ShareAltOutlined />}
          onClick={handleShare}
          style={{ padding: '0 4px', height: '20px' }}
        />
      </div>

      {/* Update Available */}
      {updateAvailable && (
        <div
          style={{
            background: '#1890ff',
            color: 'white',
            borderRadius: '8px',
            padding: '8px 12px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <ReloadOutlined style={{ fontSize: '14px' }} />
          <Text style={{ color: 'white', fontSize: '12px', margin: 0 }}>
            Update Available
          </Text>
          <Button
            type='text'
            size='small'
            onClick={handleUpdate}
            style={{
              color: 'white',
              padding: '0 4px',
              height: '20px',
              fontSize: '10px',
            }}
          >
            Update
          </Button>
        </div>
      )}
    </div>
  );
};

export default PWAStatus;
