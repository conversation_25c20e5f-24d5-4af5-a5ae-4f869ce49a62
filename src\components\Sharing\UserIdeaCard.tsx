'use client';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Select, Tag } from 'antd';
import dayjs from 'dayjs';
import { HiLightBulb } from 'react-icons/hi';
import useMediaQuery from '../../hooks/useMediaQuery';
import { getStatusText, StatusCode } from '../../utils/CommonFunctions';
import styles from './UserIdeaCard.module.css';

const { Option } = Select;

interface UserIdeaCardProps {
  user: {
    _id: string;
    user_id: string;
    name: string;
    color: string;
    status: StatusCode;
    createdAt: number;
    folder_access_role: string;
  };
  revokeClick: () => void;
  changeFolderAccessRole?: (accessRole: string, folderId: string) => void;
}

const UserIdeaCard = ({
  user,
  revokeClick,
  changeFolderAccessRole,
}: UserIdeaCardProps) => {
  const sm = useMediaQuery('(max-width: 600px)');

  return (
    <Card
      style={{ borderRadius: 12 }}
      bodyStyle={{ padding: sm ? 12 : 16 }}
      className={styles.card}
    >
      <Row gutter={[16, 6]}>
        {sm && (
          <Col span={24}>
            <div
              className={`${styles.iconWrapper} ${styles[user.color]}`}
              style={{
                backgroundColor: user.color,
                marginBottom: '10px',
              }}
            >
              <HiLightBulb className={styles.icon} />
            </div>
          </Col>
        )}
        <Col xs={24} sm={12} md={12}>
          <span className={styles.propertyTitle}>Folder: </span>
          <span className={styles.folder}>{user.name}</span>
        </Col>
        <Col xs={24} sm={12} md={12}>
          <span className={styles.propertyTitle}>Date: </span>
          <span className={styles.date}>
            {' '}
            {dayjs(user.createdAt).format('DD-MM-YYYY')}
          </span>
        </Col>
        <Col xs={24} sm={12} md={12}>
          <span className={styles.propertyTitle}>Status: </span>
          <Tag
            className={`${styles.statusTag} ${styles[`statusTag${user.status === '2' ? 'InReview' : getStatusText(user.status)}`]}`}
          >
            {getStatusText(user.status)}
          </Tag>
        </Col>
        <Col xs={24} sm={12} md={12}>
          <span className={styles.propertyTitle}>Role: </span>
          <Select
            value={user.folder_access_role}
            className={styles.roleSelectDrp}
            onSelect={value => {
              if (user?.folder_access_role !== value) {
                changeFolderAccessRole?.(value, user._id);
              }
            }}
          >
            <Option value='admin'>Admin</Option>
            <Option value='legal_user'>Legal User</Option>
            <Option value='user'>User</Option>
          </Select>
        </Col>
        <Col
          xs={24}
          style={{
            textAlign: 'end',
            marginTop: '10px',
          }}
        >
          <Button
            type='default'
            style={{
              backgroundColor: '#C70000',
              color: '#fff',
              height: '35px',
            }}
            onClick={() => {
              revokeClick();
            }}
          >
            Revoke
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

export default UserIdeaCard;
