'use client';

import PublicHeader from '@/components/Header/PublicHeader';
import { useTheme } from '@/contexts/ThemeContext';
import { colors } from '@/theme/antdTheme';
import React from 'react';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { theme } = useTheme();

  const isDarkMode = theme === 'dark';

  const containerStyle: React.CSSProperties = {
    backgroundColor: isDarkMode ? colors.primary : colors.light.background,
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
  };

  return (
    <div style={containerStyle}>
      <PublicHeader />
      <div style={{ flex: 1 }}>{children}</div>
    </div>
  );
}
