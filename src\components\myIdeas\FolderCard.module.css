/* FolderCard CSS Module */

.styledCard {
  border-radius: 12px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  height: 200px !important;
  position: relative;
  min-width: 300px;
  box-shadow: 0 8px 24px hsla(0, 0%, 0%, 0.12) !important;
}

html[data-theme='light'] .styledCard {
  background-color: var(--color-white) !important;
}

html[data-theme='dark'] .styledCard {
  background-color: var(--color-idea-card) !important;
  box-shadow: 0;
  border: 0px;
}

.styledCard:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.styledCard :global(.ant-card-body) {
  padding: 16px !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.actionButton {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.actionButton :global(.ant-dropdown-trigger) {
  color: var(--color-text-secondary) !important;
  font-size: 16px !important;
  padding: 4px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.actionButton :global(.ant-dropdown-trigger):hover {
  background-color: var(--color-border) !important;
  color: var(--color-text-primary) !important;
}

.cardContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.folderIcon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.folderIcon .anticon {
  color: white !important;
  font-size: 24px !important;
}

.folderDescription {
  color: var(--color-text-secondary) !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  margin-bottom: 12px !important;

  display: -webkit-box;
  -webkit-line-clamp: 2; /* Max 3 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.date {
  color: var(--color-secondary) !important;
  font-size: 12px !important;
  line-height: 1.4 !important;

  display: -webkit-box;
  -webkit-line-clamp: 3; /* Max 3 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.statusTag {
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border: none !important;
  align-self: flex-start;
}

.statusTagDraft {
  background-color: #faad14 !important;
  color: white !important;
}

.statusTagSealed {
  background-color: #52c41a !important;
  color: white !important;
}

.statusTagInReview {
  background-color: #0059ff !important;
  color: white !important;
}

.statusTagDefault {
  background-color: #d9d9d9 !important;
  color: #666 !important;
}

.editInput {
  border: none;
  outline: none;
  background: transparent;
  font-size: 16px;
  font-weight: 500;
  width: 100%;
  padding: 0;
  margin: 0;
  color: inherit;
  font-family: inherit;
}

.folderName {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  cursor: pointer;
  font-size: 16px !important;
  color: var(--color-text-primary);

  display: -webkit-box;
  -webkit-line-clamp: 1; /* Max 1 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 600px) {
  .folderIcon {
    width: 45px;
    height: 45px;
  }
}
