'use client';

import { Col, Pagination, Row, Spin } from 'antd';

import { useNotification } from '@/components/Notification/NotificationContext';
import { useTranslation } from '@/hooks/useTranslation';
import { isEmpty } from 'lodash-es';
import { useEffect, useState } from 'react';
import { Endpoints } from '../../../config/endpoints';
import { ApiResponse, getApiData } from '../../../helpers/ApiHelper';
import { useAppSelector } from '../../../store/hooks';
import InviteMemberModal from '../../modals/InviteMemberModal';
import { FolderData } from '../../myIdeas';
import AddNewUserCard from '../AddNewUserCard';
import UserCard from '../UserCard/UserCard';
import styles from './UsersList.module.css';

interface PaginationProps {
  currentPage: number;
  isMore: boolean;
  pageSize: number;
  totalCount: number | null;
  totalPage: number | null;
}
interface InviteData {
  email: string;
  fullname: string;
  profile_pic: string;
  access_role: string;
  _id: string | number;
  user_id: string | number;
  owner_id: string | number;
}
interface InviteResponse extends ApiResponse {
  data: InviteData[];
  pagination: {
    currentPage?: number;
    isMore?: boolean;
    pageSize?: number;
    totalCount?: number | null;
    totalPage?: number | null;
  };
}

const UsersList = ({
  folderDetails,
}: {
  folderDetails?: FolderData | null;
}) => {
  const [open, setOpen] = useState(false);
  const [updateData, setUpdateData] = useState({} as InviteData);
  const notification = useNotification();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();
  const [loader, setLoader] = useState(false);
  const { user } = useAppSelector(s => s.auth);
  const [inviteList, setInviteList] = useState<InviteData[]>([]);
  const [removeUserLoader, setRemoveUserLoader] = useState(false);
  const [pagination, setPagination] = useState<PaginationProps>({
    currentPage: 1,
    isMore: false,
    pageSize: 10,
    totalCount: null,
    totalPage: null,
  });
  interface sendInviteProps {
    status: boolean;
    message: string;
    data: object;
  }
  interface dataProps {
    email: string;
    role: string;
  }

  interface RemoveAccessResponse {
    status: boolean;
    message: string;
  }

  // Remove Access
  const removeAccess = async (removeData: InviteData) => {
    setRemoveUserLoader(true);
    try {
      const data = {
        shared_member_id: removeData?._id,
        folder_id: folderDetails?._id,
        remove_user_id: removeData?.user_id,
      };
      const res = await getApiData<
        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        {},
        RemoveAccessResponse
      >({
        url: `${Endpoints.removeUserAccess}`,
        method: 'POST',
        data: data,
      });
      if (res && res.status === true) {
        notification.success({
          message: t('myIdeasTabs.acesssRemoved'),
          description: res?.message || t('myIdeasTabs.acesssRemovedSuccess'),
        });
        setRemoveUserLoader(false);
        setInviteList(prev =>
          prev.filter(n => n.user_id !== data?.remove_user_id)
        );
        getInviteList();
      } else {
        setRemoveUserLoader(false);
        notification.error({
          message: t('error.try_again'),
          description: res?.message || t('error.something_went_wrong'),
        });
      }
    } catch (error) {
      setRemoveUserLoader(false);
      console.error('Error marking notification as read:', error);
    }
  };
  const handleSendInvite = async (data: dataProps) => {
    setLoading(true);
    try {
      setLoading(true);
      const sendData = {
        access_role: data?.role || '',
        email: data?.email || '',
        folder_id: folderDetails?._id || '',
      };
      const response = await getApiData<
        {
          access_role: string;
          email: string;
          folder_id: string;
        },
        sendInviteProps
      >({
        url: Endpoints.inviteMember,
        method: 'POST',
        data: sendData,
      });
      if (response && response.status === true) {
        notification.success({
          message: t('users.invitation_sent_title'),
          description: t('users.invitation_sent_description'),
        });
        getInviteList();
        setOpen(false);
        setLoading(false);
        setUpdateData({} as InviteData);
      } else {
        notification.error({
          message: t('users.invitation_failed_title'),
          description:
            response?.message || t('users.invitation_failed_description'),
        });
        setLoading(false);
      }
    } catch (error) {
      console.log('🚀 ~ handleSendInvite ~ error:', error);
      notification.error({
        message: t('users.invitation_failed_title'),
        description: t('users.invitation_failed_description'),
      });
      setLoading(false);
    }
  };
  const changePermission = async (data: dataProps) => {
    setLoading(true);
    try {
      setLoading(true);

      const sendData = {
        access_role: data?.role || '',
        invited_user_id: updateData?.user_id || '',
        folder_id: folderDetails?._id || '',
      };
      const response = await getApiData<
        {
          access_role: string;
          invited_user_id: string | number;
          folder_id: string;
        },
        sendInviteProps
      >({
        url: Endpoints.changeFolderAccessRole,
        method: 'POST',
        data: sendData,
      });
      if (response && response.status === true) {
        notification.success({
          message: t('users.invitation_sent_title'),
          description: t('users.invitation_sent_description'),
        });
        getInviteList();
        setOpen(false);
        setLoading(false);
        setUpdateData({} as InviteData);
      } else {
        notification.error({
          message: t('users.invitation_failed_title'),
          description:
            response?.message || t('users.invitation_failed_description'),
        });
        setLoading(false);
      }
    } catch (error) {
      console.log('🚀 ~ changePermission ~ error:', error);
      notification.error({
        message: t('users.invitation_failed_title'),
        description: t('users.invitation_failed_description'),
      });
      setLoading(false);
    }
  };

  const getInviteList = async (page?: number) => {
    if (isEmpty(inviteList)) {
      setLoader(true);
    }
    try {
      let apiData = {};
      apiData = {
        page: page || 1,
        folder_id: folderDetails?._id,
      };

      const response = await getApiData<
        {
          page?: number | string;
          folder_id?: string;
        },
        InviteResponse
      >({
        url: Endpoints.inviteList,
        method: 'POST',
        data: apiData,
      });

      if (response && response.status === true && response?.data) {
        setInviteList(response.data);
        const pagination = response.pagination;
        if (response.pagination) {
          setPagination(prev => ({
            ...prev,
            currentPage: pagination?.currentPage ?? prev.currentPage ?? 1, // fallback to prev or 1
            isMore: pagination?.isMore ?? prev.isMore,
            totalPage: pagination?.totalPage ?? prev.totalPage,
            pageSize: pagination?.pageSize ?? prev.pageSize,
            totalCount: pagination?.totalCount ?? prev.totalCount,
          }));
        }
      } else {
        notification.error({
          message: 'Error',
          description: response?.message || t('myIdeas.error'),
        });
      }

      setLoader(false);
    } catch (error) {
      setLoader(false);
      notification.error({
        message: 'Error',
        description: error?.message || t('error'),
      });
      console.error('Error fetching inviteList:', error);
    }
  };
  useEffect(() => {
    getInviteList(1);
    return () => {};
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.container}>
      <Row
        gutter={[16, 32]}
        style={{
          marginTop: '10px',
        }}
      >
        {loader ? (
          <div className={styles.foldersSpinner}>
            <Spin size='default' />
          </div>
        ) : (
          <>
            {inviteList.map((data, index) => {
              return (
                <Col key={index} xl={4} lg={6} md={8} sm={8} xs={12}>
                  <UserCard
                    invitedUser={data}
                    removeUserLoader={removeUserLoader}
                    onCancel={() => {
                      removeAccess(data);
                    }}
                    onEdit={() => {
                      setUpdateData(data);
                      setOpen(true);
                    }}
                    folderAccess={
                      folderDetails?.folder_access_role
                        ? folderDetails?.folder_access_role
                        : '-'
                    }
                  />
                </Col>
              );
            })}
            {(folderDetails?.folder_access_role === 'admin' ||
              folderDetails?.shared_by_owner_id === user?._id) && (
              <Col xl={4} lg={6} md={8} sm={8} xs={12}>
                <AddNewUserCard
                  onClick={() => {
                    setOpen(true);
                  }}
                />
              </Col>
            )}

            <InviteMemberModal
              visible={open}
              onClose={() => {
                setOpen(false);
                setUpdateData({} as InviteData);
              }}
              onSendInvite={data => handleSendInvite(data)}
              changePermission={data => changePermission(data)}
              btnLoader={loading}
              preData={updateData}
              accessRole={folderDetails?.folder_access_role}
            />
          </>
        )}
      </Row>
      {pagination?.totalPage && pagination?.totalPage > 1 ? (
        <div className={styles.loadMoreWrapper}>
          <Pagination
            current={pagination.currentPage}
            pageSize={pagination.pageSize}
            total={pagination.totalCount || 0}
            onChange={p => {
              setPagination(prev => ({
                ...prev,
                currentPage: p,
              }));
              getInviteList(p);
            }}
            showSizeChanger={false}
          />
        </div>
      ) : null}
    </div>
  );
};

export default UsersList;
