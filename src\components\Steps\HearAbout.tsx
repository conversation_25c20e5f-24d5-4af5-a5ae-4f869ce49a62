'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { colors } from '@/theme/antdTheme';
import { Card, Col, Form, FormInstance, Row, Typography } from 'antd';
import Image from 'next/image';
import { useEffect } from 'react';
import { images } from '../../config/images';
import { useTranslation } from '../../hooks/useTranslation';
import styles from './HearAbout.module.css';
const { Text } = Typography;

interface Platform {
  name: string;
  icon: string;
}

interface SocialPlatformSelectorProps {
  form: FormInstance;
}

export default function SocialPlatformSelector({
  form,
}: SocialPlatformSelectorProps) {
  const selected = Form.useWatch('platform', form);
  const { theme } = useTheme();

  useEffect(() => {
    if (!selected) {
      form.setFieldsValue({ platform: 'Youtube' }); // default selection
    }
  }, [form, selected]);

  const handleSelect = (name: string) => {
    form.setFieldsValue({ platform: name });
  };

  const { t } = useTranslation();

  const platforms: Platform[] = [
    {
      name: t('hear_about.youtube'),
      icon: images.youtube,
    },
    { name: t('hear_about.twitter'), icon: images.twitter },
    {
      name: t('hear_about.instagram'),
      icon: images.instagram,
    },
    {
      name: t('hear_about.facebook'),
      icon: images.facebook,
    },
    {
      name: t('hear_about.google'),
      icon: images.google,
    },
    { name: t('hear_about.other'), icon: images.other },
  ];

  return (
    <div className={styles.container}>
      <Form.Item name='platform' hidden />
      <Row gutter={[0, 16]}>
        {platforms.map(platform => (
          <Col span={24} key={platform.name}>
            <Card
              onClick={() => handleSelect(platform.name)}
              style={{
                cursor: 'pointer',
                backgroundColor:
                  theme === 'dark'
                    ? selected === platform.name
                      ? colors.secondary
                      : '#375278'
                    : selected === platform.name
                      ? colors.secondary
                      : colors.cardBackground,
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                boxShadow:
                  selected === platform.name
                    ? '0 4px 12px rgba(0, 0, 0, 0.1)'
                    : '0 1px 3px rgba(0, 0, 0, 0.05)',
                border: '0px',
              }}
              bodyStyle={{
                display: 'flex',
                alignItems: 'center',
                padding: '12px 20px',
              }}
              hoverable
            >
              <div
                style={{
                  marginRight: 16,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <Image
                  src={platform.icon}
                  alt={platform.name}
                  width={30}
                  height={30}
                />
              </div>
              <Text
                className={styles.label}
                style={{
                  color:
                    theme === 'dark'
                      ? selected === platform.name
                        ? colors.primary
                        : '#ffffff'
                      : selected === platform.name
                        ? colors.primary
                        : colors.light.subTitleText,
                }}
              >
                {platform.name}
              </Text>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
}
