.container {
  background-color: var(--dashboard-bg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
}

.topText {
  text-align: center;
  padding: 20px 16px;
  background-color: var(--dashboard-bg);
  border-bottom: 1px solid #e8e8e8;
}

.topLabel {
  font-size: clamp(14px, 2.5vw, 18px);
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.4;
  max-width: 800px;
  margin: 0 auto;
}

.backgroundWrapper {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 80vh;
}

.mainContent {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlayCard {
  width: 100%;
  max-width: 1000px;
  padding: 60px 40px;
}

.leftSection {
  display: flex;
  flex-direction: column;
}

.brand {
  color: #fff !important;
  margin: 0 !important;
  font-size: clamp(1.8rem, 4vw, 3rem) !important;
  font-weight: 700 !important;
  letter-spacing: -0.5px;
}

.subtitle {
  color: #fff !important;
  font-size: clamp(14px, 2vw, 20px);
  font-weight: 500;
  margin: 0;
}

.rightSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.card :global(.ant-card) {
  background: var(--dashboard-bg) !important;
  border: 2px solid transparent !important;
  border-radius: 16px !important;
  padding: 40px 30px !important;
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    box-shadow 0.3s ease;

  cursor: pointer !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  position: relative !important;
  overflow: hidden !important;
}
:global(html[data-theme='dark']) .card {
  background: var(--color-primary) !important;
}
:global(html[data-theme='dark']) .card:hover {
  color: #000;
  background: var(--color-secondary) !important;
}
:global(html[data-theme='light']) .card {
  background: #fff !important;
}
:global(html[data-theme='light']) .card:hover {
  background-color: var(--color-secondary) !important;
  color: #012458;
}

.card:hover {
  background-color: var(--color-secondary) !important;
  color: #012458;
  box-shadow: 0 0 12px var(--color-secondary);
}

.cardContent {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.icon {
  font-size: clamp(32px, 6vw, 56px) !important;
  color: var(--text-primary) !important;
  margin-bottom: 8px !important;
}

.cardTitle {
  color: var(--text-primary) !important;
  font-size: clamp(14px, 2.5vw, 20px) !important;
  font-weight: 600 !important;
  margin: 0 !important;
  line-height: 1.3 !important;
}

.footer {
  text-align: center;
  padding: 20px;
  background-color: var(--dashboard-bg);
  border-top: 1px solid #e8e8e8;
  font-size: clamp(12px, 1.5vw, 16px);
  font-weight: 500;
}
.footerText {
  color: var(--text-primary);
}
.swiss_image {
  padding-bottom: 80px;
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
  .overlayCard {
    padding: 40px 30px;
  }

  .brand {
    font-size: 2.2rem !important;
  }
}

@media screen and (max-width: 768px) {
  .topText {
    padding: 16px 12px;
  }

  .topLabel {
    font-size: 14px;
  }

  .mainContent {
    padding: 20px 16px;
  }

  .overlayCard {
    padding: 30px 20px;
    border-radius: 16px;
  }

  .brand {
    font-size: 1.8rem !important;
  }

  .subtitle {
    font-size: 16px;
  }

  .card {
    padding: 0px 0px !important;
  }

  .icon {
    font-size: 40px !important;
  }

  .cardTitle {
    font-size: 16px !important;
  }

  .rightSection {
    gap: 16px;
  }
  .swiss_image {
    height: 50px !important;
    width: 120px !important;
    padding-bottom: 10px !important;
    padding-bottom: 20px;
  }
}

@media screen and (max-width: 480px) {
  .topText {
    padding: 12px 8px;
  }

  .topLabel {
    font-size: 13px;
  }

  .mainContent {
    padding: 16px 12px;
  }
  .swiss_image {
    height: 50px !important;
    width: 100px !important;
    padding-bottom: 10px !important;
    object-fit: contain;
  }

  .overlayCard {
    padding: 24px 16px;
  }

  .brand {
    font-size: 1.6rem !important;
  }

  .subtitle {
    font-size: 14px;
  }

  .card {
    padding: 0px 0px !important;
  }

  .icon {
    font-size: 36px !important;
  }

  .cardTitle {
    font-size: 15px !important;
  }

  .footer {
    padding: 16px;
    font-size: 13px;
  }
}

/* Large screens */
@media screen and (min-width: 1400px) {
  .overlayCard {
    padding: 80px 60px;
  }

  .brand {
    font-size: 3rem !important;
  }

  .subtitle {
    font-size: 20px;
  }

  .card {
    padding: 50px 40px !important;
  }

  .icon {
    font-size: 56px !important;
  }

  .cardTitle {
    font-size: 20px !important;
  }
}

/* Extra large screens */
@media screen and (min-width: 1920px) {
  .overlayCard {
    padding: 100px 80px;
  }

  .brand {
    font-size: 3.5rem !important;
  }

  .subtitle {
    font-size: 22px;
  }

  .card {
    padding: 60px 50px !important;
  }

  .icon {
    font-size: 64px !important;
  }

  .cardTitle {
    font-size: 22px !important;
  }
}
