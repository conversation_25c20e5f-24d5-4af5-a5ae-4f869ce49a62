'use client';

import React from 'react';

import ProtectedRoute from '@/components/auth/ProtectedRoute';
import SettingsComp from '../../../../components/dashboard/settings';

/**
 * Dashboard page - Protected route that requires authentication
 */
const Settings: React.FC = () => {
  return (
    <ProtectedRoute requireAuth={true}>
      <SettingsComp />
    </ProtectedRoute>
  );
};

export default Settings;
