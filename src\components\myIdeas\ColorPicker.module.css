/* ColorPicker CSS Module */

.colorContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.colorOption {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.colorOption:hover {
  transform: scale(1.1);
  border-color: var(--color-primary);
}

.colorOption::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 10px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.colorOptionSelected {
  border-color: var(--color-primary) !important;
}

.customColorButton {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--color-background);
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customColorButton:hover {
  transform: scale(1.1);
  border-color: var(--color-primary);
}

.customColorButton .anticon {
  color: white !important;
  font-size: 16px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}
.colorPickerWrapper :global(.ant-color-picker-trigger) {
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px !important;
}
