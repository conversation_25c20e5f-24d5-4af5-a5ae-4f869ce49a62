.container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 40px;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: #000000 !important;
  font-family: var(--font-radio-canada) !important;
  text-align: left;
}

.titleContainer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: left;
  width: 100%;
  margin-bottom: 20px;
}

:global(html[data-theme='dark']) .pageTitle {
  color: #fff !important;
}

@media (max-width: 1200px) {
  .pageTitle {
    font-size: 32px !important;
  }
}

@media (max-width: 900px) {
  .pageTitle {
    font-size: 32px !important;
  }
}
@media (max-width: 625px) {
  .container {
    padding: 20px;
  }
}

@media (max-width: 600px) {
  .buttons {
    flex-direction: column;
  }
  .pageTitle {
    font-size: 24px !important;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 22px !important;
  }
}
