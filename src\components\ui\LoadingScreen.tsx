'use client';

import { Spin } from 'antd';
import styles from './LoadingScreen.module.css';

interface LoadingScreenProps {
  text?: string;
}

export default function LoadingScreen({
  text = 'Loading...',
}: LoadingScreenProps) {
  return (
    <div className={styles.loadingScreen}>
      <div className={styles.loadingContent}>
        <Spin size='large' />
        <div className={styles.loadingText}>{text}</div>
      </div>
    </div>
  );
}
