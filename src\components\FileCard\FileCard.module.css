/* FileDisplay.module.css */

.fileCard {
  border-radius: 16px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-width: 280px;
  max-width: 320px;
  overflow: hidden;
  box-shadow: 3.55px 8.88px 46.2px rgba(0, 0, 0, 0.09);
  background-color: #fff;
}

:global(body[data-theme='dark']) .fileCard {
  box-shadow: 'none';
  background-color: #16406f;
  border: none !important;
}

.cardBody {
  padding: 24px;
}

.iconContainer {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  position: relative;
  background-color: #8edafe !important;
}

.fileIcon {
  font-size: 30px;
  color: var(--color-primary) !important;
}

.fileName {
  font-size: 18px !important;
  font-weight: 500;
  color: #000000 !important;
  margin-bottom: 8px;
  word-break: break-word;
  font-family: var(--font-radio-canada);
}

:global(body[data-theme='dark']) .fileName {
  color: #fff !important;
}

.description {
  font-size: 16px;
  color: #555555 !important;
  margin-bottom: 8px;
  line-height: 1.4;
  font-family: var(--font-poppins);
  font-weight: 300;
}

:global(body[data-theme='dark']) .description {
  color: #edeff1 !important;
}

.fileSize {
  font-size: 12px;
  color: #bfbfbf;
  margin-bottom: 16px;
}

.downloadButton {
  color: #1890ff;
  border-color: transparent;
  padding: 0;
  height: auto;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--font-radio-canada);
}

:global(body[data-theme='dark']) .downloadButton {
  color: #8edafe !important;
}

.downloadButton:hover {
  color: #40a9ff;
}

.closeIconDiv {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ff3939;
  border-radius: 100%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.progressBar {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeIcon {
  color: #fff;
  font-size: 16px;
}

/* Demo specific styles */
.demoContainer {
  padding: 32px;
  background-color: #fafafa;
  min-height: 100vh;
}

.demoTitle {
  font-size: 24px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 32px;
}

.cardsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cardsGrid {
    grid-template-columns: 1fr;
  }

  .demoContainer {
    padding: 16px;
  }

  .fileCard {
    max-width: 100%;
  }
}

/* responsive media queries */

@media (max-width: 1200px) {
  .fileName {
    font-size: 16px !important;
  }
  .fileCard {
    min-width: 250px;
  }
  .description {
    font-size: 14px !important;
  }
  .downloadButton {
    font-size: 14px !important;
  }
}

@media (max-width: 600px) {
  .fileName {
    font-size: 14px !important;
  }
  .fileCard {
    min-width: 200px;
  }
  .description {
    font-size: 12px !important;
  }
  .downloadButton {
    font-size: 14px !important;
  }
  .iconContainer {
    width: 50px;
    height: 50px;
  }
  .fileIcon {
    font-size: 24px;
  }
}
