/**
 * @fileoverview Facebook SDK loader utility for social authentication
 * @module utils/loadFbSdk
 */

import siteConfig from '@/config/site.config';

/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * Dynamically loads the Facebook SDK for JavaScript
 * This function ensures the Facebook SDK is loaded only once and provides
 * a promise-based interface for initialization
 *
 * @function loadFacebookSDK
 * @returns {Promise<void>} Promise that resolves when Facebook SDK is loaded and initialized
 * @example
 * // Load Facebook SDK before using Facebook login
 * await loadFacebookSDK();
 * // Now you can use FB.login() and other Facebook API methods
 *
 * @example
 * // With error handling
 * try {
 *   await loadFacebookSDK();
 *   console.log('Facebook SDK loaded successfully');
 * } catch (error) {
 *   console.error('Failed to load Facebook SDK:', error);
 * }
 */
export const loadFacebookSDK = (): Promise<void> => {
  return new Promise(resolve => {
    // Check if Facebook SDK is already loaded
    if (typeof window !== 'undefined' && (window as any).FB) {
      resolve();
      return;
    }

    // Initialize Facebook SDK when it's loaded
    (window as any).fbAsyncInit = function () {
      (window as any).FB.init({
        appId: siteConfig.facebookAppId, // Facebook App ID
        cookie: true, // Enable cookies for server-side authentication
        xfbml: true, // Parse social plugins on the page
        version: 'v23.0', // Facebook API version
      });
      resolve();
    };

    // Create and append Facebook SDK script
    const script = document.createElement('script');
    script.src = 'https://connect.facebook.net/en_US/sdk.js';
    script.async = true;
    document.body.appendChild(script);
  });
};
