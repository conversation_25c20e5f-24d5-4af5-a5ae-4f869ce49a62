'use client';

import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider } from 'antd';
import React from 'react';

import { useTheme } from '@/contexts/ThemeContext';
import { darkTheme, lightTheme } from '@/theme/antdTheme';

interface AntdProviderProps {
  children: React.ReactNode;
}

const AntdProvider: React.FC<AntdProviderProps> = ({ children }) => {
  const { theme } = useTheme();
  const currentTheme = theme === 'dark' ? darkTheme : lightTheme;

  // Theme-specific layout configurations
  const getLayoutConfig = (isDark: boolean) => ({
    Layout: {
      ...currentTheme.components?.Layout,
      headerBg: isDark ? '#1f1f1f' : '#ffffff',
      headerHeight: 50,
      headerPadding: '0 24px',
      siderBg: isDark ? '#0f1419' : '#1e3a5f', // Darker for dark mode
      bodyBg: isDark ? '#141414' : '#f0f8ff',
      footerBg: isDark ? '#1f1f1f' : '#ffffff',
      triggerBg: isDark ? '#0f1419' : '#1e3a5f',
      triggerColor: '#ffffff',
    },
    Menu: {
      ...currentTheme.components?.Menu,
      itemBg: 'transparent',
      itemColor: '#ffffff',
      itemHoverBg: 'rgba(255, 255, 255, 0.1)',
      itemHoverColor: '#ffffff',
      itemSelectedBg: 'rgba(255, 255, 255, 0.25)',
      itemSelectedColor: '#ffffff',
      itemActiveBg: 'rgba(255, 255, 255, 0.15)',
      subMenuItemBg: 'transparent',
      popupBg: isDark ? '#0f1419' : '#1e3a5f',
      darkItemBg: 'transparent',
      darkItemColor: '#ffffff',
      darkItemHoverBg: 'rgba(255, 255, 255, 0.1)',
      darkItemSelectedBg: 'rgba(255, 255, 255, 0.25)',
    },
    Card: {
      ...currentTheme.components?.Card,
      headerBg: isDark ? '#1f1f1f' : '#ffffff',
      actionsBg: isDark ? '#1f1f1f' : '#ffffff',
    },
    Table: {
      ...currentTheme.components?.Table,
      headerBg: isDark ? '#262626' : '#fafafa',
      rowHoverBg: isDark ? '#262626' : '#f5f5f5',
    },
  });

  // Deep merge with theme-specific overrides
  const enhancedTheme = {
    ...currentTheme,
    components: {
      ...currentTheme.components,
      ...getLayoutConfig(theme === 'dark'),
    },
  };

  return (
    <AntdRegistry>
      <ConfigProvider theme={enhancedTheme}>{children}</ConfigProvider>
    </AntdRegistry>
  );
};

export default AntdProvider;
