.container {
  background-color: #ffff;
  max-width: 700px;
}

:global(html[data-theme='dark']) .container {
  background-color: var(--button-primary);
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: #000000 !important;
  font-family: var(--font-radio-canada) !important;
}

:global(html[data-theme='dark']) .pageTitle {
  color: #ffff !important;
}

.uploadParent {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
}

.uploadContainer {
  width: 360px;
  height: 170px;
  background-color: #d7fff1 !important;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.sealContainer {
  width: 360px;
  height: 170px;
  background-color: #1bf8ab !important;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.iconBox {
  width: 57px;
  height: 57px;
  background-color: #00f5a0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sealIconBox {
  width: 57px;
  height: 57px;
  background-color: #d8240c;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 30px;
  height: 30px;
  color: #fff;
}
.label {
  font-size: 18px !important;
  font-weight: 500 !important;
  color: #000000 !important;
  font-family: var(--font-radio-canada) !important;
}

:global(html[data-theme='dark']) .label {
  color: #ffff !important;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.deleteContainer {
  width: 70px;
  height: 70px;
  background-color: #c70000 !important;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

:global(html[data-theme='dark']) .deleteContainer {
  background-color: #8edafe !important;
}
.deleteIcon {
  font-size: 35px;
  color: #fff;
}

:global(html[data-theme='dark']) .deleteIcon {
  color: #012458 !important;
}
.deleteTextContainer {
  margin-top: 20px;
}
.deleteText {
  font-size: 18px !important;
  font-weight: 300 !important;
  color: #555555 !important;
  font-family: var(--font-poppins) !important;
}
:global(html[data-theme='dark']) .deleteText {
  color: #edeff1 !important;
}

.downloadButton {
  color: #1bf8ab !important;
  border-color: transparent;
  padding: 0;
  height: auto;
  font-size: 12px;
  font-weight: 500;
  font-family: var(--font-radio-canada);
  text-decoration: underline;
  margin-top: -5px;
}
.sealDownloadButton {
  color: #d8240c !important;
  border-color: transparent;
  padding: 0;
  height: auto;
  font-size: 12px;
  font-weight: 500;
  font-family: var(--font-radio-canada);
  text-decoration: underline;
  margin-top: -5px;
}

.checkbox {
  margin-right: 10px;
}

:global(html[data-theme='dark']) .uploadContainer {
  background-color: #16406f !important;
}
:global(html[data-theme='dark']) .sealContainer {
  background-color: #452744 !important;
}

@media (max-width: 1200px) {
  .container {
    max-width: 100%;
  }
  .pageTitle {
    font-size: 38px !important;
  }

  .uploadContainer {
    width: 250px;
    height: 150px;
  }
  .sealContainer {
    width: 250px;
    height: 150px;
  }

  .iconBox {
    width: 50px;
    height: 50px;
  }

  .sealIconBox {
    width: 50px;
    height: 50px;
  }

  .icon {
    width: 30px;
    height: 30px;
    color: #fff;
  }

  .deleteContainer {
    width: 65px;
    height: 65px;
  }

  .deleteIcon {
    font-size: 32px;
  }

  .label {
    font-size: 16px !important;
  }

  .deleteText {
    font-size: 16px !important;
  }
}

@media (max-width: 900px) {
  .container {
    padding: 20px;
  }
  .pageTitle {
    font-size: 32px !important;
  }
  .uploadContainer {
    width: 200px;
    height: 150px;
  }

  .sealContainer {
    width: 200px;
    height: 150px;
  }

  .iconBox {
    width: 50px;
    height: 50px;
  }

  .icon {
    width: 30px;
    height: 30px;
    color: #fff;
  }

  .deleteContainer {
    width: 65px;
    height: 65px;
  }

  .deleteIcon {
    font-size: 32px;
  }

  .label {
    font-size: 16px !important;
  }

  .deleteText {
    font-size: 16px !important;
  }
}

@media (max-width: 768px) {
  .uploadParent {
    justify-content: center;
  }

  .uploadContainer {
    width: 50%;
    height: 150px;
  }

  .sealContainer {
    width: 50%;
    height: 150px;
  }
}

@media (max-width: 600px) {
  .contentContainer {
    align-items: center;
  }
  .pageTitle {
    font-size: 24px !important;
  }

  .uploadContainer {
    width: 50%;
    height: 125px;
  }

  .sealContainer {
    width: 50%;
    height: 125px;
  }

  .iconBox {
    width: 40px;
    height: 40px;
  }

  .sealIconBox {
    width: 40px;
    height: 40px;
  }

  .deleteText {
    font-size: 14px !important;
  }

  .icon {
    width: 25px;
    height: 25px;
    color: #fff;
  }

  .deleteContainer {
    width: 50px;
    height: 50px;
  }

  .deleteIcon {
    font-size: 26px;
  }

  .label {
    font-size: 16px !important;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 22px !important;
  }
}
