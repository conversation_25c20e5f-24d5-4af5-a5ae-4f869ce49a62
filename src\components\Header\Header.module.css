.dashboardHeader {
  height: 88px !important;
  background-color: var(--color-background);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  border-bottom: 1px solid var(--color-border);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.headerLeft {
  display: flex;
  flex-direction: column;
}

.welcomeTitle {
  margin: 0;
  font-weight: 600;
  font-size: 18px;
  color: var(--color-text-primary) !important;
}

.welcomeSubtitle {
  margin-top: 4px;
  font-size: 14px;
  color: var(--color-text-secondary) !important;
}

.headerRight {
  display: flex;
  gap: 16px;
  align-items: center;
}
.user {
  transition: all 0.3s ease;
  border-radius: 10px;
  background-color: #edeff1;
  height: 45px;
  width: 50px;
  color: #333 !important;
  cursor: pointer !important;
}

:global(html[data-theme='dark']) .user {
  background-color: rgb(77, 102, 138) !important;
  color: #fff !important;
}
:global(html[data-theme='dark']) .user:hover {
  color: #fff !important;
  border: 1px solid rgb(255, 255, 255);
}
:global(html[data-theme='light']) .user:hover {
  color: #333 !important;
  border: 1px solid #333;
}
