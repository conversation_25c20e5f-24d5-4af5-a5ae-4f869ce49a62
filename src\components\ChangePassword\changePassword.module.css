.container {
  max-width: 50vw;
  margin: 0 auto;
  padding: 24px 16px;
  background-color: var(--color-background);
}

.header {
  padding: 10px 0px 0px;
}

.backButton {
  padding: 0px 0px 20px 0px;
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
}

.backButton:hover {
  color: var(--color-text-primary);
}

.backIcon {
  font-size: 18px;
  cursor: pointer;
  color: #333;
}

.title {
  margin: 0;
  font-weight: 600;
  color: #111827;
  padding: 0px 0px 20px 0px;
  text-align: center;
}

.form {
  display: flex;
  flex-direction: column;
}

.input {
  border-radius: 12px;
  padding: 10px;
  height: 44px;
}

@media screen and (max-width: 480px) {
  .container {
    max-width: 100vw;
  }
}
