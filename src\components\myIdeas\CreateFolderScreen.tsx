'use client';

import { FolderAddOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Typography } from 'antd';
import { PhoneNumberUtil } from 'google-libphonenumber';
import { useParams, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { useTheme } from '../../contexts/ThemeContext';
import useMediaQuery from '../../hooks/useMediaQuery';
import { colors } from '../../theme/antdTheme';
import { useNotification } from '../Notification/NotificationContext';
import PhoneNumberInput from '../PhoneNumberInput/PhoneNumberInput';
import ColorPicker from './ColorPicker';
import styles from './CreateFolderScreen.module.css';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * Form data interface for folder creation
 *
 * @interface FolderFormData
 * @property {string} name - Folder name
 * @property {string} phoneNumber - Associated phone number
 * @property {string} color - Selected color for the folder
 * @property {string} description - Folder description
 */
interface FolderFormData {
  name: string;
  mobile_number?: string;
  color: string;
  description: string;
  country_code?: string;
  _id?: string;
}

/**
 * CreateFolderScreen component for creating and editing folders
 *
 * @component CreateFolderScreen
 * @returns {JSX.Element} Rendered create folder screen component
 *
 * @example
 * <CreateFolderScreen />
 *
 * @description
 * This component provides a form interface for users to:
 * - Create new idea folders with name, phone number, color, and description
 * - Edit existing folders (when accessed with edit query parameter)
 * - Select colors from predefined options or custom color picker
 * - Validate form inputs before submission
 * - Navigate back to My Ideas screen on cancel or successful creation
 */
const CreateFolderScreen: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const notification = useNotification();
  const [form] = Form.useForm();
  const [selectedColor, setSelectedColor] = useState(colors.secondary);
  const [loading, setLoading] = useState(false);
  const [folderName, setFolderName] = useState<string>('');

  const params = useParams();
  const editId = params.id as string;

  const isEditing = !!editId;

  const { theme } = useTheme();

  const [phoneData, setPhoneData] = useState({
    phone: '',
    country: '',
    countryCode: '',
    fullNumber: '',
  });

  useEffect(() => {
    if (isEditing) {
      getFolderData(editId);
    }
  }, [isEditing, form]);

  const handleSubmit = async (values: FolderFormData) => {
    setLoading(true);

    try {
      const folderData = {
        name: values.name,
        mobile_number: phoneData?.phone,
        color: selectedColor,
        description: values.description,
        country_code: phoneData?.countryCode,
      };

      const res = await getApiData<FolderFormData, ApiResponse>({
        url: Endpoints.createFolder,
        method: 'POST',
        data: folderData,
      });

      if (res && res.status === true) {
        notification.success({
          message: res?.message || 'Folder created successfully',
        });
        // Navigate back to My Ideas screen
        router.push('/my-ideas');
      } else {
        notification.error({
          message: res?.message || 'Error creating folder',
        });
        setLoading(false);
      }
    } catch (error) {
      notification.error({
        message: error.message || 'Error creating folder',
      });
      console.error('Error saving folder:', error);
      setLoading(false);
    }
  };

  const handleEditSubmit = async (values: FolderFormData) => {
    setLoading(true);

    try {
      const folderData: FolderFormData = {
        _id: editId,
        name: values.name,
        color: selectedColor,
        description: values.description,
      };

      // Only include phone data if phone number exists
      if (phoneData?.phone) {
        folderData.mobile_number = phoneData.phone;
        folderData.country_code = phoneData.countryCode;
      }

      const res = await getApiData<FolderFormData, ApiResponse>({
        url: Endpoints.updateFolder,
        method: 'POST',
        data: folderData,
      });

      if (res && res.status === true) {
        notification.success({
          message: res?.message || 'Folder updated successfully',
        });
        router.push('/my-ideas');
      } else {
        notification.error({
          message: res?.message || 'Error updating folder',
        });
        setLoading(false);
      }
    } catch (error) {
      notification.error({
        message: error.message || 'Error updating folder',
      });
      console.error('Error saving folder:', error);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/my-ideas');
  };

  const getFolderData = async (id: string) => {
    try {
      const res = await getApiData<FolderFormData, ApiResponse>({
        url: `${Endpoints.getFolderById}/${id}`,
        method: 'GET',
      });
      if (res && res.status === true && res.data) {
        form.setFieldsValue({
          name: res.data.name,
          mobile_number: res.data.mobile_number,
          description: res.data.description,
        });
        setFolderName(res.data.name);
        setSelectedColor(res.data.color);

        const getCountryFromCode = (
          countryCode: string,
          phoneNumber: string
        ) => {
          try {
            const phoneUtil = PhoneNumberUtil.getInstance();
            const fullNumber = `${countryCode}${phoneNumber}`;
            const parsed = phoneUtil.parseAndKeepRawInput(fullNumber);
            const regionCode = phoneUtil.getRegionCodeForNumber(parsed);
            return regionCode ? regionCode.toLowerCase() : 'us';
          } catch (error) {
            console.error('Error getting country from code:', error);
            return 'us';
          }
        };

        const mappedCountry = getCountryFromCode(
          res.data.country_code,
          res.data.mobile_number
        );

        // Set the full international number for the PhoneInput component
        const fullPhoneNumber = `${res.data.country_code}${res.data.mobile_number}`;

        setPhoneData({
          phone: res.data.mobile_number,
          country: mappedCountry,
          countryCode: res.data.country_code,
          fullNumber: fullPhoneNumber, // Add full number for the input value
        });
      }
    } catch (error) {
      console.error('Error fetching folder data:', error);
    }
  };

  const sm = useMediaQuery('(max-width: 625px)');
  const md = useMediaQuery('(max-width: 900px)');

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.folderIcon}>
          <FolderAddOutlined
            style={{
              fontSize: sm ? '20px' : md ? '40px' : '50px',
            }}
          />
        </div>
        <Title level={1} className={styles.pageTitle}>
          {isEditing ? folderName : t('createFolder.title')}
        </Title>
      </div>

      <div className={styles.formContainer}>
        <Form
          form={form}
          layout='vertical'
          onFinish={isEditing ? handleEditSubmit : handleSubmit}
          requiredMark={false}
        >
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <div className={styles.formSection}>
                <Form.Item
                  name='name'
                  label={
                    <Text className={styles.sectionLabel}>
                      {t('createFolder.fields.name')}
                    </Text>
                  }
                  rules={[
                    {
                      required: true,
                      message: t('createFolder.validation.nameRequired'),
                    },
                  ]}
                >
                  <Input
                    placeholder={t('createFolder.fields.namePlaceholder')}
                  />
                </Form.Item>
              </div>
            </Col>

            <Col xs={24} md={12}>
              <div className={styles.formSection}>
                <PhoneNumberInput
                  defaultCountry={phoneData?.country}
                  value={phoneData?.fullNumber || phoneData?.phone}
                  onChange={data => {
                    setPhoneData({
                      phone: data.phone,
                      country: data.country,
                      countryCode: data.countryCode,
                      fullNumber: data.fullNumber,
                    });
                  }}
                  theme={theme}
                  form={form}
                  t={t}
                  authStyles={styles}
                />
              </div>
            </Col>
          </Row>

          <div className={styles.formSection}>
            <Text className={styles.sectionLabel}>
              {t('createFolder.fields.colors')}
            </Text>
            <ColorPicker value={selectedColor} onChange={setSelectedColor} />
          </div>

          <div className={styles.formSection}>
            <Form.Item
              name='description'
              label={
                <Text className={styles.sectionLabel}>
                  {t('createFolder.fields.description')}
                </Text>
              }
            >
              <TextArea
                placeholder={t('createFolder.fields.descriptionPlaceholder')}
                rows={4}
                maxLength={400}
              />
            </Form.Item>
          </div>

          <div className={styles.actionButtons}>
            <Button
              type='primary'
              htmlType='submit'
              loading={loading}
              className={styles.createButton}
            >
              {isEditing ? t('common.save') : t('createFolder.actions.create')}
            </Button>

            <Button onClick={handleCancel} className={styles.cancelButton}>
              {t('createFolder.actions.cancel')}
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default CreateFolderScreen;
