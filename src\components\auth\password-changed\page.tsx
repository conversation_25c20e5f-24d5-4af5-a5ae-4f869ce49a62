'use client';

import { Button, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import React from 'react';

import PublicRoute from '@/components/auth/PublicRoute';
import { images } from '@/config/images';
import { useTheme } from '@/contexts/ThemeContext';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import Lottie from 'lottie-react';
import Image from 'next/image';
import Link from 'next/link';
import { isMobile, isTablet } from 'react-device-detect';
import animationData from '../../../../public/lottie/passwordChangedSuccess.json';
import styles from './page.module.css';

const { Title, Text } = Typography;

const PasswordChangedPage: React.FC = () => {
  const router = useRouter();
  const { theme } = useTheme();

  const { t } = useTranslation();

  const handleLoginClick = () => {
    router.push('/login');
  };

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');

  return (
    <PublicRoute>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Logo */}
          <div className={styles.logoSection}>
            <Image
              src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
              alt='Swiss Trust Layer'
              className={styles.logoImage}
              width={180}
              height={180}
              style={{
                width: xs ? '175px' : sm ? '190px' : '220px',
                height: 'auto',
              }}
            />
          </div>

          {/* Success Icon with decorative elements */}
          <div className={styles.iconSection}>
            <Lottie
              animationData={animationData}
              loop={true}
              style={{
                width: isMobile ? '144px' : isTablet ? '186px' : '196px',
                height: isMobile ? '144px' : isTablet ? '186px' : '196px',
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -40%)',
                marginTop: !isMobile && !isTablet ? '50px' : '20px',
              }}
            />
          </div>

          {/* Title and Description */}
          <div
            className={styles.textSection}
            style={{
              marginTop: !isMobile && !isTablet ? '150px' : '70px',
            }}
          >
            <Title level={2} className={styles.formTitle}>
              {t('passwordChanged.title')}
            </Title>
            <Text className={styles.formSubtitle}>
              {t('passwordChanged.subtitle1')}
            </Text>
          </div>

          {/* Login Button */}
          <div className={styles.buttonSection}>
            <Button
              type='primary'
              onClick={handleLoginClick}
              className={styles.loginButton}
            >
              {t('passwordChanged.login')}
            </Button>
          </div>

          {/* Support Link */}
          <div className={styles.supportSection}>
            <Link
              href='mailto:<EMAIL>'
              className={styles.supportText}
            >
              {' '}
              {t('passwordChanged.need_support')}?
            </Link>
          </div>
        </div>
      </div>
    </PublicRoute>
  );
};

export default PasswordChangedPage;
