.container {
  width: 100%;
  margin: auto;
}

.heading {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
}

.formItem {
  margin-bottom: 25px !important;
}

.formItem :global(.ant-form-item-label) {
  padding-bottom: 4px !important;
}

.formItem :global(.ant-form-item-label > label) {
  color: var(--text-primary) !important;
  font-weight: 400;
  font-size: 16px;
}

.formItem :global(.ant-form-item-explain-error) {
  font-size: 12px;
}

.select {
  height: 50px;
}

@media (max-width: 1200px) {
  .formItem :global(.ant-form-item-label > label) {
    font-size: 16px !important;
  }
  .select {
    height: 45px;
  }
}
@media (max-width: 900px) {
  .formItem :global(.ant-form-item-label > label) {
    font-size: 14px !important;
  }
  .select {
    height: 45px;
  }
}
@media (max-width: 600px) {
  .formItem :global(.ant-form-item-label > label) {
    font-size: 14px;
  }
  .select {
    height: 42px;
  }
}
