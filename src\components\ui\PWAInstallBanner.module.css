/* PWA Install Banner Styles */

.installBanner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background: linear-gradient(135deg, #012458, rgb(77, 102, 138));
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease-out;
}

.bannerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  max-width: 1200px;
  margin: 0 auto;
  gap: 12px;
}

.bannerIcon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.bannerText {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0;
}

.bannerTitle {
  color: white !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

.bannerDescription {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 12px !important;
  margin: 0 !important;
  line-height: 1.3 !important;
}

.bannerActions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.installButton {
  background: white !important;
  border-color: white !important;
  color: #012458 !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  height: 28px !important;
  padding: 0 12px !important;
  font-weight: 500 !important;
}

.installButton:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.9) !important;
  color: #012458 !important;
}

.dismissButton {
  color: rgba(255, 255, 255, 0.8) !important;
  border: none !important;
  background: transparent !important;
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.dismissButton:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

/* Animations */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .bannerContent {
    padding: 6px 12px;
    gap: 8px;
  }

  .bannerIcon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .bannerTitle {
    font-size: 13px !important;
  }

  .bannerDescription {
    font-size: 11px !important;
  }

  .installButton {
    font-size: 11px !important;
    height: 26px !important;
    padding: 0 10px !important;
  }

  .dismissButton {
    width: 26px !important;
    height: 26px !important;
  }
}

@media (max-width: 480px) {
  .bannerContent {
    padding: 4px 8px;
    gap: 6px;
  }

  .bannerIcon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .bannerText {
    gap: 1px;
  }

  .bannerTitle {
    font-size: 12px !important;
  }

  .bannerDescription {
    font-size: 10px !important;
  }

  .installButton {
    font-size: 10px !important;
    height: 24px !important;
    padding: 0 8px !important;
  }

  .dismissButton {
    width: 24px !important;
    height: 24px !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .installBanner {
    border-bottom: 2px solid white;
  }

  .installButton {
    border-width: 2px !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .installBanner {
    animation: none;
  }
}

/* Dark theme adjustments */
:global(html[data-theme='dark']) .installBanner {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  border-bottom: 1px solid #434343;
}

:global(html[data-theme='dark']) .bannerIcon {
  background: rgba(255, 255, 255, 0.1);
}

:global(html[data-theme='dark']) .installButton {
  background: #012458 !important;
  border-color: #012458 !important;
  color: white !important;
}

:global(html[data-theme='dark']) .installButton:hover {
  background: #012458 !important;
  border-color: #012458 !important;
}
