'use client';

import { Layout } from 'antd';
import React from 'react';

import HeaderCmp from '@/components/Header/Header';
import NotificationManager from '@/components/NotificationManager/NotificationManager';
import Sidebar from '@/components/Sidebar/Sidebar';
import { usePathname } from 'next/navigation';
import styles from './layout.module.css';

const { Content } = Layout;

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  // Define routes where HeaderCmp should be hidden
  const hideHeaderRoutes = [
    '/my-ideas',
    '/my-ideas/create-folder',
    '/settings',
    '/notification-permission',
  ];
  const hideSideBarRoutes = ['/settings', '/notification-permission'];
  const shouldHideHeader = hideHeaderRoutes.some(route =>
    pathname.startsWith(route)
  );

  const shouldHideSideBar = hideSideBarRoutes.some(route =>
    pathname.startsWith(route)
  );
  return (
    <Layout className={styles.rootLayout}>
      {!shouldHideSideBar && <Sidebar />}
      <Layout className={styles.contentLayout}>
        {!shouldHideHeader && <HeaderCmp />}
        <Content className={styles.dashboardContent}>
          {/* Notification Manager - uses browser default permission dialog */}
          <NotificationManager autoPrompt={true} promptDelay={2000} />
          {children}
        </Content>
      </Layout>
    </Layout>
  );
}
