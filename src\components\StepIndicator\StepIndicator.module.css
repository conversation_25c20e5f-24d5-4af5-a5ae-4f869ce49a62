.wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin: 24px auto;
  height: 10px;
}

.segment {
  flex: 1;
  height: 10px;
  background-color: #e5e5e5;
  border-radius: 5px;
  max-width: 130px;
  min-width: 10px;
  transition: background-color 0.3s ease;
}

.segment.active {
  background-color: var(
    --color-primary
  ); /* Deep navy like in your screenshot */
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin: 16px 0;
  flex-wrap: nowrap;
}

.step {
  width: 20px;
  height: 10px;
  background-color: #efefef;
  border-radius: 50%;
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .step {
    width: 8px;
    height: 8px;
  }

  .step.active {
    width: 12px;
    height: 12px;
  }

  .container {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .container {
    gap: 6px;
  }

  .step {
    width: 6px;
    height: 6px;
  }

  .step.active {
    width: 10px;
    height: 10px;
  }
}
