'use-client';
import { Modal } from 'antd';
import { AccountModalProps, Plans, Prices } from '..';
import Subscription from '../../Subscription';

interface Props {
  visible: boolean;
  plan: Prices | null;
  upgrade?: boolean;
  duration?: boolean;
  modalData: AccountModalProps;
  onCancel: () => void;
  planDetails: Plans;
}

export default function SubscriptionModal({
  visible,
  plan,
  upgrade,
  duration,
  modalData,
  planDetails,
  onCancel,
}: Props) {
  return (
    <Modal
      open={visible}
      footer={null}
      onCancel={onCancel}
      centered
      width={700}
      maskClosable={false}
      closable={true}
      keyboard={false}
    >
      <Subscription
        plan={plan}
        planDetails={planDetails}
        upgrade={upgrade}
        duration={duration}
        modalData={modalData}
        onCancel={onCancel}
      />
    </Modal>
  );
}
