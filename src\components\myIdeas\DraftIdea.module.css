.container {
  width: 100%;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
}

.textArea {
  background-color: #edeff1;
}

.formLabel {
  font-size: 24px;
  color: #000000;
  font-weight: 500;
  font-family: var(--font-radio-canada);
  line-height: 16px;
}

.docDataContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px;
  background-color: #edeff1;
  border-radius: 13px;
}

.docData {
  display: flex;
  justify-content: space-between;
}

.docDataLabel {
  font-size: 16px;
  color: #555555;
  font-weight: 400;
  font-family: var(--font-radio-canada);
  line-height: 16px;
}

.docDataText {
  font-size: 16px;
  color: #555555;
  font-weight: 400;
  font-family: var(--font-poppins);
  line-height: 18px;
}

.promptText {
  font-size: 18px;
  color: #555555;
  font-weight: 300;
  font-family: var(--font-poppins);
  line-height: 18px;
}

.btnParent {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.buttonGroup {
  display: flex;
  justify-content: center;
  flex-direction: row;
  gap: 12px;
  width: 450px;
}

/* .fileCardRow {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
} */

.uploadContainer {
  /* background-color: #d7fff1 !important; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  border-radius: 16px;
  overflow: hidden;
  height: 100%;
  width: 100%;
  /* box-shadow: 3.55px 8.88px 46.2px rgba(0, 0, 0, 0.09); */
}

.iconBox {
  width: 90px;
  height: 90px;
  background-color: #00f5a0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 50px;
  height: 50px;
  color: #fff;
}

.label {
  font-size: 20px;
  font-weight: 500;
  color: #000;
  font-family: var(--font-radio-canada);
}

.tab {
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer !important;
}

.activeTab {
  padding: 10px 20px;
  background-color: #012458;
  border-radius: 12px;
  cursor: pointer !important;
}

.text {
  color: #555555;
  font-family: var(--font-poppins);
  font-weight: 300;
  font-size: 14px;
}

.activeText {
  color: #ffff;
  font-family: var(--font-poppins);
  font-weight: 300;
  font-size: 14px;
}

.tabContainer {
  display: flex;
  gap: 10px;
}

/* View More/Less Button Styles */
.viewMoreContainer {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  width: 100%;
}

.viewMoreButton {
  font-size: 18px !important;
  font-weight: 500 !important;
  color: #012458 !important;
  font-family: var(--font-radio-canada) !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
}

/* Dark Theme Styles */
:global(html[data-theme='dark']) .docDataContainer {
  background-color: #16406f;
}

:global(html[data-theme='dark']) .pageTitle {
  color: #fff !important;
}

:global(html[data-theme='dark']) .label {
  color: #fff !important;
}

:global(html[data-theme='dark']) .textArea {
  background-color: #06164a;
}

:global(html[data-theme='dark']) .formLabel {
  color: #fff !important;
}

:global(html[data-theme='dark']) .promptText {
  color: #fff !important;
}

:global(html[data-theme='dark']) .docDataLabel {
  color: #edeff1 !important;
}

:global(html[data-theme='dark']) .docDataText {
  color: #edeff1 !important;
}

:global(html[data-theme='dark']) .viewMoreButton {
  color: #ffff !important;
}

.viewMoreButton:hover {
  text-decoration: underline !important;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .pageTitle {
    font-size: 32px !important;
  }
  .formLabel {
    font-size: 18px !important;
  }

  .docDataLabel {
    font-size: 16px;
  }

  .docDataText {
    font-size: 16px;
  }

  /* .uploadContainer {
    width: 400px;
    height: 200px;
  } */

  .iconBox {
    width: 70px;
    height: 70px;
  }

  .icon {
    width: 40px;
    height: 40px;
    color: #fff;
  }

  .promptText {
    font-size: 16px;
  }
}

@media (max-width: 900px) {
  .pageTitle {
    font-size: 32px !important;
  }
  .label {
    font-size: 16px;
  }
  .formLabel {
    font-size: 16px !important;
  }
  .description {
    font-size: 12px !important;
  }

  .iconBox {
    width: 50px;
    height: 50px;
  }

  .icon {
    width: 30px;
    height: 30px;
    color: #fff;
  }

  .docDataContainer {
    margin-top: -15px;
  }

  .viewMoreButton {
    font-size: 14px !important;
  }
}

@media (max-width: 600px) {
  .pageTitle {
    font-size: 24px !important;
  }
  .formLabel {
    font-size: 16px !important;
  }

  .docDataLabel {
    font-size: 14px;
  }

  .docDataText {
    font-size: 14px;
  }

  /* .uploadContainer {
    width: 250px;
    height: 125px;
  } */

  .iconBox {
    width: 40px;
    height: 40px;
  }

  .icon {
    width: 25px;
    height: 25px;
    color: #fff;
  }

  .promptText {
    font-size: 14px;
    margin-top: -20px;
  }

  .docDataContainer {
    gap: 10px;
    padding: 15px;
    margin-top: -15px;
  }

  .viewMoreButton {
    font-size: 12px !important;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 22px !important;
  }
  .docDataLabel {
    font-size: 13px !important;
  }
  .docDataText {
    font-size: 13px !important;
  }

  .docDataContainer {
    gap: 5px;
    margin-top: -15px;
  }
}

.foldersSpinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dragger .ant-upload-drag .ant-upload {
  background: transparent !important;
  padding: 0px;
  border-width: 0px;
}
