.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 1rem;
  text-align: center;
  background-color: var(--dashboard-bg);
  justify-content: center;
  height: 90vh;
  min-height: 90vh;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
}

.subtitle {
  font-size: 1rem;
  color: var(--text-primary);
  max-width: 500px;
  margin-bottom: 2rem;
}

.illustration {
  margin-bottom: 1rem;
}

.illustrationImg {
  max-width: 100%;
  height: auto;
  margin: 30px 0px;
}

.note {
  font-size: 0.9rem;
  color: #999;
  margin-bottom: 2rem;
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 30px;
}
.agreeButton {
  font-size: 18px !important;
  font-weight: 500 !important;
  font-family: var(--poppins);
}

.enableBtn {
  background-color: #001f4d; /* dark navy */
  border-color: #001f4d;
}

.laterBtn {
  background-color: #f0f0f0;
  border-color: #f0f0f0;
  color: #666;
}
.primaryButton {
  font-size: 18px !important;
  font-weight: 500 !important;
  font-family: var(--poppins);
  padding: 0px 48px;
}
.primaryButton:active {
  transform: translateY(0) !important;
}

.primaryButton:disabled {
  background: #6c757d !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .title {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    margin-top: 2rem;
  }
  .subtitle {
    font-size: 0.95rem;
  }
  .enableBtn,
  .laterBtn {
    width: 100%;
  }
}
