import { useNotification } from '@/components/Notification/NotificationContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { colors } from '@/theme/antdTheme';
import {
  BellOutlined,
  CloseOutlined,
  DeleteOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { Button, Modal, Space, Spin, Switch, Typography } from 'antd';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash-es';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import {
  SwipeableList,
  SwipeableListItem,
  SwipeAction,
  TrailingActions,
} from 'react-swipeable-list';
import 'react-swipeable-list/dist/styles.css';
import { Endpoints } from '../../config/endpoints';
import { images } from '../../config/images';
import { getApiData } from '../../helpers/ApiHelper';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { updateUser } from '../../store/slices/authSlice';
import styles from './notificationListingPage.module.css';
export interface NotificationExtraData {
  shared_id: string;
  folder_id: string;
  user_id: string;
  email: string;
  access_role: 'viewer' | 'editor' | 'admin'; // restrict to roles
}
// Interface for notification
interface Notification {
  _id: string;
  action_type:
    | 'payment_sent'
    | 'seal_process_failed'
    | 'seal_process_sucess'
    | 'subscription_added'
    | 'invite_members_for_idea'
    | 'done_accept_members_for_idea'
    | 'remove_member_for_idea';
  title: string;
  message: string;
  createdAt: string;
  is_read: string | number;
  folder_id: string | number;
  shared_id: string | number;
  extra_data?: NotificationExtraData | string;
}
interface loaderData {
  loader: boolean;
  id: string | number;
}
const NotificationListing: React.FC = () => {
  const { Title } = Typography;
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { user } = useAppSelector(s => s.auth);
  const [deleteAllVisible, setDeleteAllVisible] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);

  const [btnLoader, setBtnLoader] = useState<loaderData>({
    loader: false,
    id: '',
  });
  const [hasMore, setHasMore] = useState<number>();
  const [hasMoreLoader, setHasMoreLoader] = useState(false);
  const [deleteAllLoader, setDeleteAllLoader] = useState(false);
  const [switchChecked, setSwitchChecked] = useState(
    user?.notification_permission
  );
  const [toggleLoading, setToggleLoading] = useState(false);
  const notification = useNotification();
  interface ActivityLogParams {
    page: number;
  }
  interface ReadNotificationPayload {
    notification_id: string | number;
    is_read: 0 | 1;
  }

  interface ApiPagination {
    totalCount: number;
    pageSize: number;
    totalPage: number;
    currentPage: number;
    isMore: boolean;
  }
  interface ActivityLogResponse {
    status: boolean;
    data: Notification[];
    pagination: ApiPagination;
    message: string;
  }

  interface ApiError {
    status: false;
    message: string;
  }

  interface UpdateNotificationPermissionRequest {
    notificationPermission: boolean;
  }

  interface GenericResponse {
    status: boolean;
    message: string;
  }
  const handleToggleNotification = async (checked: boolean) => {
    setSwitchChecked(checked);
    setToggleLoading(true);
    try {
      const res = await getApiData<
        UpdateNotificationPermissionRequest,
        GenericResponse | ApiError
      >({
        url: Endpoints.updateNotificationPermission,
        method: 'POST',
        data: { notificationPermission: checked },
      });

      if (res && res.status === true) {
        dispatch(
          updateUser({
            user: { ...user, notification_permission: checked },
            isAuthenticated: true,
          })
        );
        setSwitchChecked(checked);
        notification.success({
          message: t('notification.notification_permission_changed'),
          description:
            res.message || 'Notification permission change successfully',
        });
      }
    } catch (error) {
      console.log('🚀 ~ handleToggleNotification ~ error:', error);
      setSwitchChecked(!checked);
      notification.error({
        message: t('error.try_again'),
        description: t('error.something_went_wrong'),
      });
    } finally {
      setToggleLoading(false);
    }
  };
  // Fetch Notifications with Pagination
  const fetchNotifications = async (pageNumber: number) => {
    const payload: ActivityLogParams = { page: pageNumber };
    try {
      if (isEmpty(notifications)) {
        setLoading(true);
      }
      const res = await getApiData<
        ActivityLogParams,
        ActivityLogResponse | ApiError
      >({
        url: Endpoints.notificationList,
        method: 'POST',
        data: payload,
      });
      if (res && 'data' in res && res.status) {
        const data = res.data;
        if (pageNumber === 1) {
          setNotifications(data);
        } else {
          setNotifications(prev => [...prev, ...data]);
        }
        setHasMore(res.pagination.totalCount);
      } else {
        notification.error({
          message: t('error.try_again'),
          description: res?.message || t('error.something_went_wrong'),
        });
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
      setHasMoreLoader(false);
    }
  };

  // Mark as Read
  const markAsRead = async (id: string) => {
    const payload: ReadNotificationPayload = {
      notification_id: id,
      is_read: 1,
    };
    try {
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      const res = await getApiData<{}, ActivityLogResponse | ApiError>({
        url: `${Endpoints.readNotification}`,
        method: 'POST',
        data: payload,
      });
      if (res && res.status === true) {
        fetchNotifications(page);
        setNotifications(prev =>
          prev.map(n => (n._id === id ? { ...n, is_read: 1 } : n))
        );
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  interface DataProps {
    notification_id?: string | number;
    shared_member_id?: string | number;
    type?: string;
  }
  // Delete notification
  const deleteNotification = async (notificationItem: Notification) => {
    const extraData =
      typeof notificationItem.extra_data === 'string'
        ? JSON.parse(notificationItem.extra_data)
        : notificationItem.extra_data || {};
    try {
      let data: DataProps = {
        notification_id: notificationItem?._id,
      };
      if (notificationItem?.action_type === 'invite_members_for_idea') {
        data = {
          notification_id: notificationItem?._id,
          shared_member_id: extraData?.shared_id,
          type: 'invite_members_for_idea',
        };
      }
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      const res = await getApiData<{}, ActivityLogResponse | ApiError>({
        url: `${Endpoints.deleteSingleNotification}`,
        method: 'POST',
        data,
      });
      if (res && res.status === true) {
        fetchNotifications(page);
        setNotifications(prev =>
          prev.filter(n => n._id !== notificationItem?._id)
        );
      } else {
        notification.error({
          message: t('error.try_again'),
          description: res?.message || t('error.something_went_wrong'),
        });
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Delete notification
  const deleteAll = async () => {
    setDeleteAllLoader(true);
    try {
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      const res = await getApiData<{}, ActivityLogResponse | ApiError>({
        url: `${Endpoints.deleteAllNotification}`,
        method: 'GET',
      });
      if (res && res.status === true) {
        notification.success({
          message: t('notification.notification_deleted'),
          description: t('notification.notficationsDeleted'),
        });
        setDeleteAllVisible(false);
        setPage(1);
        fetchNotifications(1);
        setNotifications([]);
      } else {
        notification.error({
          message: t('error.try_again'),
          description: res?.message || t('error.something_went_wrong'),
        });
      }
      setDeleteAllLoader(false);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Swipe Delete Action
  const trailingActions = (notification: Notification) => (
    <TrailingActions>
      <SwipeAction destructive onClick={() => deleteNotification(notification)}>
        <div className={styles.deleteAction}>
          <DeleteOutlined style={{ fontSize: 20 }} />
        </div>
      </SwipeAction>
    </TrailingActions>
  );

  // Load more notifications when scrolling
  const loadMore = () => {
    if (!loading && notifications.length < Number(hasMore)) {
      const nextPage = page + 1;
      setHasMoreLoader(true);
      setPage(nextPage);
      fetchNotifications(nextPage);
    }
  };
  interface joinResponseProp {
    status: boolean;
    message: string;
    data: object;
  }
  interface InvideDataProps {
    folder_id: string | number;
    shared_id: string | number;
    action: string;
    notification_id: string | number;
  }

  const joinFolderInvite = async (data: InvideDataProps) => {
    try {
      setBtnLoader({ loader: true, id: data?.notification_id });
      const response = await getApiData<
        {
          folder_id: string | number;
          shared_id: string | number;
          action: string;
        },
        joinResponseProp
      >({
        url: Endpoints.joinInvite,
        method: 'POST',
        data: {
          folder_id: data.folder_id,
          shared_id: data.shared_id,
          action: data.action,
        },
      });
      if (response && response.status === true) {
        notification.success({
          message: t('notification.invite_accepted_title'),
          description: t('notification.invite_accepted_description'),
        });
        setNotifications(prev =>
          prev.map(n =>
            n._id === data.notification_id
              ? { ...n, action_type: 'done_accept_members_for_idea' }
              : n
          )
        );
        setBtnLoader({ loader: false, id: '' });
      } else {
        fetchNotifications(page);
        notification.error({
          message: t('notification.invite_accept_failed_title'),
          description:
            response?.message ||
            t('notification.invite_accept_failed_description'),
        });
        setBtnLoader({ loader: false, id: '' });
      }
    } catch (error) {
      console.log('🚀 ~ joinFolderInvite ~ error:', error);
      notification.error({
        message: t('notification.invite_accept_failed_title'),
        description: t('notification.invite_accept_failed_description'),
      });
      setBtnLoader({ loader: false, id: '' });
    }
  };
  useEffect(() => {
    fetchNotifications(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.title}>Notifications</div>

        <div className={styles.notificationHeader}>
          <div>
            {/* Notification Toggle */}
            <span style={{ fontWeight: 500 }}>Allow Notifications</span>
            <Switch
              checked={switchChecked}
              loading={toggleLoading}
              onChange={(checked: boolean) => {
                handleToggleNotification(checked);
              }}
              size={'default'}
              className={switchChecked ? styles.switch : styles.switchDisable}
            />
          </div>
          {/* Delete All Button */}
          {notifications.length > 0 && (
            <Button
              type='primary'
              danger
              size='small'
              onClick={() => setDeleteAllVisible(true)}
              icon={<DeleteOutlined />}
              className={styles.deleteAllBtn}
            >
              Delete All
            </Button>
          )}
        </div>
      </div>
      {loading && page === 1 ? (
        <Spin style={{ display: 'block', marginTop: 100 }} />
      ) : (
        <SwipeableList>
          {notifications.map(n => (
            <SwipeableListItem
              key={n._id}
              trailingActions={trailingActions(n)}
              className={styles.parent}
            >
              <div
                className={`${styles.card} ${styles[n.action_type]}`}
                onClick={() => markAsRead(n._id)}
                style={{ position: 'relative' }}
              >
                {(n.is_read === 0 || n.is_read === '0') && (
                  <span className={styles.unreadDot} />
                )}
                <div className={styles.icon}>
                  {n.action_type === 'seal_process_failed' ||
                  n.action_type === 'remove_member_for_idea' ? (
                    <CloseOutlined
                      style={{
                        backgroundColor: 'red',
                        height: 30,
                        width: 30,
                        borderRadius: 15,
                        padding: 5,
                        color: 'white',
                      }}
                    />
                  ) : n.action_type === 'payment_sent' ||
                    n.action_type === 'seal_process_sucess' ? (
                    <DollarOutlined
                      style={{
                        height: 40,
                        width: 40,
                        borderRadius: 20,
                        padding: 5,
                        color: 'gold',
                        marginRight: 0,
                      }}
                    />
                  ) : (
                    <div style={{ borderWidth: 1, borderColor: 'black' }}>
                      <Image
                        src={images.crown}
                        alt='Plan Icon'
                        width={30}
                        height={30}
                      />
                    </div>
                  )}
                </div>
                <div className={styles.content}>
                  <strong>{n.title}</strong>
                  <p>{n.message}</p>
                  {(n.action_type === 'invite_members_for_idea' ||
                    n.action_type === 'done_accept_members_for_idea') && (
                    <Button
                      loading={btnLoader?.loader && n?._id === btnLoader?.id}
                      style={{
                        backgroundColor:
                          n.action_type === 'invite_members_for_idea'
                            ? colors.primary
                            : colors.success,
                        padding: '5px 30px',
                        color: 'white',
                        border: 'none',
                        opacity:
                          n.action_type === 'done_accept_members_for_idea'
                            ? 0.6
                            : 1,
                      }}
                      disabled={
                        n.action_type === 'done_accept_members_for_idea'
                      }
                      onClick={() => {
                        const extraData =
                          typeof n.extra_data === 'string'
                            ? JSON.parse(n.extra_data)
                            : n.extra_data || {};
                        const data = {
                          folder_id: extraData?.folder_id,
                          shared_id: extraData?.shared_id,
                          action: 'accept',
                          notification_id: n._id,
                        };
                        joinFolderInvite(data);
                      }}
                    >
                      {n.action_type === 'invite_members_for_idea'
                        ? 'Join'
                        : 'Invitation accepted'}
                    </Button>
                  )}
                </div>
                <div className={styles.time}>
                  <div>{dayjs(n.createdAt).format('DD/MM/YYYY')}</div>
                  <div>{dayjs(n.createdAt).format('hh:mm')}</div>
                </div>
              </div>
            </SwipeableListItem>
          ))}
        </SwipeableList>
      )}
      {isEmpty(notifications) && (
        <div className={styles.empty}>
          <BellOutlined className={styles.emptyBell} />
          {t('notification.no_notifications_found')}
        </div>
      )}
      {!isEmpty(notifications) &&
        notifications.length < Number(hasMore) &&
        !loading && (
          <div
            style={{ marginTop: 10, display: 'flex', justifyContent: 'center' }}
          >
            <Button loading={hasMoreLoader} onClick={loadMore}>
              Load More
            </Button>
          </div>
        )}
      <Modal
        title={null}
        open={deleteAllVisible}
        onCancel={() => {
          setDeleteAllVisible(false);
        }}
        centered
        closable={false}
        onOk={() => {
          deleteAll();
        }}
        confirmLoading={deleteAllLoader}
        width={400}
      >
        <Space direction='vertical' style={{ width: '100%' }} size='large'>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              textAlign: 'center',
            }}
          >
            <Title
              level={5}
              style={{
                color:
                  theme === 'dark'
                    ? 'var(--color-text-tertiary)'
                    : 'var(--color-text-primary)',
              }}
              className={styles.logoutTitle}
            >
              {t('notification.sure_delete_all_notification')}
            </Title>
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default NotificationListing;
