/* eslint-disable @typescript-eslint/no-unused-vars */
import authReducer, {
  loginFailure,
  loginStart,
  logout,
} from '@/store/slices/authSlice';
import { configureStore } from '@reduxjs/toolkit';

// Create a test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
  });
};

describe('Auth Slice', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
  });

  test('should handle initial state', () => {
    const state = store.getState().auth;
    expect(state.isAuthenticated).toBe(false);
    expect(state.user).toBe(null);
    expect(state.loading).toBe(false);
    expect(state.error).toBe(null);
  });

  test('should handle login start', () => {
    store.dispatch(loginStart());
    const state = store.getState().auth;
    expect(state.loading).toBe(true);
    expect(state.error).toBe(null);
  });

  test('should handle login success', () => {
    const user = { username: 'testuser', email: '<EMAIL>' };
    // store.dispatch(loginSuccess(user));
    const state = store.getState().auth;
    expect(state.isAuthenticated).toBe(true);
    expect(state.user).toEqual(user);
    expect(state.loading).toBe(false);
    expect(state.error).toBe(null);
  });

  test('should handle login failure', () => {
    const errorMessage = 'Invalid credentials';
    store.dispatch(loginFailure(errorMessage));
    const state = store.getState().auth;
    expect(state.loading).toBe(false);
    expect(state.error).toBe(errorMessage);
    expect(state.isAuthenticated).toBe(false);
  });

  test('should handle logout', () => {
    // First login
    const user = { username: 'testuser', email: '<EMAIL>' };
    // store.dispatch(loginSuccess(user));

    // Then logout
    store.dispatch(logout());
    const state = store.getState().auth;
    expect(state.isAuthenticated).toBe(false);
    expect(state.user).toBe(null);
  });
});
