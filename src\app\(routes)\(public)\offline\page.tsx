'use client';

import { ReloadOutlined, WifiOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import React from 'react';

import AuthWrapper, { authStyles } from '@/components/auth/AuthWrapper';
import { useTranslation } from '@/hooks/useTranslation';

const { Title, Text } = Typography;

const OfflinePage: React.FC = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const handleRetry = () => {
    if (navigator.onLine) {
      router.back();
    } else {
      window.location.reload();
    }
  };

  return (
    <AuthWrapper showThemeToggle={false}>
      <div className={authStyles.logoSection}>
        <div className={authStyles.logoContainer}>
          <div className={authStyles.logoIcon}>
            <WifiOutlined />
          </div>
          <div className={authStyles.logoText}>Seal my idea</div>
        </div>
        <Title level={2} className={authStyles.formTitle}>
          {t('offline.title')}
        </Title>
        <Text className={authStyles.formSubtitle}>
          {t('offline.subtitle')}
          <br />
          {t('offline.subtitle2')}
        </Text>
      </div>

      <div style={{ textAlign: 'center', marginBottom: '24px' }}>
        <div
          style={{
            width: '80px',
            height: '80px',
            margin: '0 auto 16px',
            background: 'linear-gradient(135deg, #6c757d, #495057)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '32px',
            color: 'white',
          }}
        >
          <WifiOutlined />
        </div>
        <Text type='secondary' style={{ fontSize: '14px' }}>
          {t('offline.limited_features')}
        </Text>
      </div>

      <Button
        type='primary'
        icon={<ReloadOutlined />}
        onClick={handleRetry}
        className={authStyles.primaryButton}
        style={{ marginBottom: '16px' }}
      >
        {t('offline.try_again')}
      </Button>

      <div className={authStyles.authFooter}>
        <Text style={{ color: 'var(--text-secondary)', fontSize: '12px' }}>
          {t('offline.works_offline')}
          <br />
          Connect to the internet for the full experience.
        </Text>
      </div>
    </AuthWrapper>
  );
};

export default OfflinePage;
