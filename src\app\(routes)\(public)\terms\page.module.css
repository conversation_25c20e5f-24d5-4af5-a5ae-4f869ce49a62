.root {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  padding: 80px;
  background: #ffff;
  transition: background-color 0.3s ease;
  overflow-x: hidden;
}

.title {
  text-align: center !important;
  margin-bottom: 4px !important;
  color: #000000 !important;
  font-weight: 600 !important;
  font-size: 46px !important;
  font-family: var(--font-radio-canada);
  margin-top: 10px;
}

.subtitle {
  display: block;
  text-align: center;
  margin-bottom: 10px;
  color: #555555 !important;
  font-size: 18px;
  line-height: 1.4;
  font-family: var(--font-poppins);
  font-weight: 300;
  margin-top: 0px;
}

.termsContainer {
  margin-top: 20px;
}

.termsDiv {
  margin-bottom: 30px;
}

.agreeButton {
  width: 100% !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  font-family: var(--poppins);
  margin-top: 30px;
}

.termTitle {
  font-size: 36px !important;
  font-weight: 500 !important;
  color: #000000 !important;
  margin: 0 !important;
  font-family: var(--font-radio-canada);
}

.termDescription {
  font-size: 18px !important;
  color: #555555 !important;
  font-family: var(--font-poppins);
  font-weight: 300;
  margin-top: 0px;
}
:global(html[data-theme='dark']) .root {
  background-color: #012458;
}

:global(html[data-theme='dark']) .title,
:global(html[data-theme='dark']) .termTitle {
  color: #ffff !important;
}

:global(html[data-theme='dark']) .subtitle,
:global(html[data-theme='dark']) .termDescription {
  color: #edeff1 !important;
}

@media (max-width: 768px) {
  .root {
    padding: 40px;
  }
  .title {
    font-size: 40px !important;
  }
  .subTitle {
    font-size: 16px !important;
  }
  .termTitle {
    font-size: 30px !important;
  }

  .termDescription {
    font-size: 16px !important;
  }
}

@media (max-width: 480px) {
  .root {
    padding: 30px;
  }
  .title {
    font-size: 24px !important;
  }

  .subtitle {
    font-size: 12px !important;
    margin-top: 5px;
  }

  .termTitle {
    font-size: 16px !important;
  }

  .termDescription {
    font-size: 12px !important;
  }
}
