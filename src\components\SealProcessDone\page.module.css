.container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.title {
  margin-top: 24px;
  color: #0cd857 !important;
  font-family: var(--font-radio-canada) !important;
  font-size: 30px !important;
  font-weight: 500;
}

.text {
  margin-top: 0px;
  color: #555555 !important;
  font-family: var(--font-poppins) !important;
  font-size: 16px !important;
  font-weight: 400;
}

@media (max-width: 600px) {
  .title {
    font-size: 18px !important;
    text-align: center;
  }
  .text {
    font-size: 12px !important;
    text-align: center;
  }
}
