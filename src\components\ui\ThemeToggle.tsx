'use client';

import { MoonOutlined, SunOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import React from 'react';
import styled from 'styled-components';

import { useTheme } from '@/contexts/ThemeContext';

const StyledButton = styled(Button)<{ $isDark: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: ${props =>
    props.$isDark
      ? 'linear-gradient(135deg, #ffd700 0%, #ffed4e 100%)'
      : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
  color: ${props => (props.$isDark ? '#333' : 'white')};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    background: ${props =>
      props.$isDark
        ? 'linear-gradient(135deg, #ffed4e 0%, #ffd700 100%)'
        : 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'};
  }

  &:focus {
    background: ${props =>
      props.$isDark
        ? 'linear-gradient(135deg, #ffd700 0%, #ffed4e 100%)'
        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
  }

  .anticon {
    font-size: 18px;
  }
`;

interface ThemeToggleProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = 'medium',
  className,
}) => {
  const { theme, toggleTheme } = useTheme();
  const isDark = theme === 'dark';

  const getTooltipText = (): string => {
    return isDark ? 'Switch to light mode' : 'Switch to dark mode';
  };

  const getButtonSize = (): {
    width: string;
    height: string;
    fontSize: string;
  } => {
    switch (size) {
      case 'small':
        return { width: '32px', height: '32px', fontSize: '14px' };
      case 'large':
        return { width: '50px', height: '45px', fontSize: '20px' };
      default:
        return { width: '40px', height: '40px', fontSize: '18px' };
    }
  };

  const buttonSize = getButtonSize();

  return (
    <Tooltip
      title={getTooltipText()}
      placement='bottom'
      color={isDark ? '#000' : '#fff'}
    >
      <StyledButton
        $isDark={isDark}
        onClick={toggleTheme}
        className={className}
        style={{
          width: buttonSize.width,
          height: buttonSize.height,
          alignSelf: 'end',
        }}
        aria-label={getTooltipText()}
      >
        {isDark ? (
          <SunOutlined style={{ fontSize: buttonSize.fontSize }} />
        ) : (
          <MoonOutlined style={{ fontSize: buttonSize.fontSize }} />
        )}
      </StyledButton>
    </Tooltip>
  );
};

export default ThemeToggle;
