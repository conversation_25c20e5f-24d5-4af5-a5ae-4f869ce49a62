.container {
  width: 100%;
}

:global(html[data-theme='dark']) .propTitle {
  color: #edeff1 !important;
}

:global(html[data-theme='dark']) .propText {
  color: #edeff1 !important;
}

:global(html[data-theme='dark']) .container {
  background-color: #012458 !important;
}
:global(html[data-theme='dark']) .content {
  background-color: #8edafe26 !important;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
}

.content {
  background-color: #edeff1;
  border-radius: 10px;
  padding: 30px 20px;
}

.iconSection {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.successText {
  font-size: 28px !important;
  font-weight: 500 !important;
  color: #0cd857 !important;
  margin-top: 12px;
  font-family: var(--font-radio-canada) !important;
}
.failedText {
  color: #d8240c !important;
}

.textSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.propSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.propTitle {
  font-size: 16px !important;
  font-family: var(--font-poppins) !important;
  color: #555555 !important;
  font-weight: 400;
}

.propText {
  font-size: 16px !important;
  font-family: var(--font-poppins) !important;
  color: #555555 !important;
  font-weight: 600;
}

.buttonSection {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

@media (max-width: 900) {
  .pageTitle {
    font-size: 28px !important;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .pageTitle {
    font-size: 24px !important;
  }

  .successText {
    font-size: 22px !important;
  }

  .propTitle {
    font-size: 14px !important;
  }

  .propText {
    font-size: 14px !important;
  }
  .textSection {
    gap: 12px;
  }

  .content {
    padding: 20px 12px;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 22px !important;
  }

  .successText {
    font-size: 20px !important;
  }
}
