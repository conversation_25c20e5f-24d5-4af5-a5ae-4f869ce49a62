'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function LinkedInCallback() {
  const searchParams = useSearchParams();
  const code = searchParams.get('code');
  const error = searchParams.get('error');

  useEffect(() => {
    if (code) {
      if (window.opener) {
        window.opener.postMessage({ type: 'LINKEDIN_AUTH_SUCCESS', code }, '*');
        setTimeout(() => window.close(), 1000);
      } else {
        window.location.href = `/login?code=${code}`;
      }
    }
    if (error) {
      console.error('❌ LinkedIn Error:', error);
    }
  }, [code, error]);

  return <p>Processing LinkedIn login...</p>;
}
