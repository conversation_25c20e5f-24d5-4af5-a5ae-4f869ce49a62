/* Dashboard Ant Design Overrides - Force our styles for both themes */

/* Dark Mode Layout overrides */
html[data-theme='dark'] .ant-layout-header,
body[data-theme='dark'] .ant-layout-header {
  background: var(--color-primary) !important;
}

html[data-theme='dark'] .ant-layout-sider,
body[data-theme='dark'] .ant-layout-sider {
  background: var(--color-primary) !important;
}

html[data-theme='dark'] .ant-layout-content,
body[data-theme='dark'] .ant-layout-content {
  background: var(--color-primary) !important;
}

/* Menu overrides for sidebar - Both themes */
.dashboard-sider .ant-menu {
  background: transparent !important;
  border: none !important;
}

.dashboard-sider .ant-menu-item {
  color: #ffffff !important;
  background: transparent !important;
}

.dashboard-sider .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.dashboard-sider .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.25) !important;
  color: #ffffff !important;
}

.dashboard-sider .ant-menu-item-icon {
  color: #ffffff !important;
}

/* Dark mode specific menu adjustments */
html[data-theme='dark'] .dashboard-sider .ant-menu-item:hover,
body[data-theme='dark'] .dashboard-sider .ant-menu-item:hover {
  background: var(--color-primary) !important;
}

html[data-theme='dark'] .dashboard-sider .ant-menu-item-selected,
body[data-theme='dark'] .dashboard-sider .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.3) !important;
}

/* Card overrides - Light mode (default) */
.ant-card {
  background: var(--color-white) !important;
}

/* Card overrides - Dark mode */
html[data-theme='dark'] .ant-card,
body[data-theme='dark'] .ant-card {
  background: var(--color-idea-card) !important;
  border-width: 0px;
}

html[data-theme='dark'] .ant-card-head,
body[data-theme='dark'] .ant-card-head {
  background: var(--color-idea-card);
  color: var(--color-text-primary);
  border-bottom: 0px solid var(--color-white);
}

/* Table overrides - Light mode (default) */
.ant-table-thead > tr > th {
  background: #edeff1 !important;
}

/* Table overrides - Dark mode */
html[data-theme='dark'] .ant-table-thead > tr > th,
body[data-theme='dark'] .ant-table-thead > tr > th {
  background: #edeff1 !important;
}

html[data-theme='dark'] .ant-table-tbody > tr:hover > td,
body[data-theme='dark'] .ant-table-tbody > tr:hover > td {
  background: #edeff1 !important;
}

/* Button overrides */
.ant-btn-text {
  border: none !important;
  box-shadow: none !important;
}

/* Modal overrides */
html[data-theme='light'] .ant-modal-content {
  background: #ffffff !important;
}

/* Select overrides */
html[data-theme='light'] .ant-select-dropdown {
  background: #ffffff !important;
}

/* Switch overrides */
.ant-switch {
  background: #d9d9d9 !important;
}

.ant-switch-checked {
  background: #1890ff !important;
}
