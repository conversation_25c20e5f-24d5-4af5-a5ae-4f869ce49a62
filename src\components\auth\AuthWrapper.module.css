/* Auth Pages Wrapper - Single source of truth for all auth styling */

/* Container Styles */
.authContainer {
  /* min-height: 100vh; */
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
  transition: background-color 0.3s ease;
  overflow-x: hidden;

  /* Light mode variables */
  --auth-bg: #f8f9fa;
  --card-bg: #ffffff;
  --text-primary: #1a1a1a;
  --text-secondary: #6c757d;
  --border-color: #e9ecef;
  --input-bg: #f8f9fa;
  --input-border: #ced4da;
  --button-primary: #012458;
  --button-primary-hover: #4d668a;
  --error-color: #dc3545;
  --success-color: #28a745;
  --subtitle: #555555;
}

/* Dark mode variables */
:global(html[data-theme='dark']) .authContainer,
:global(body[data-theme='dark']) .authContainer {
  background: var(--button-primary);

  --auth-bg: #0f1419;
  --card-bg: #1f1f1f;
  --text-primary: #ffffff;
  --text-secondary: #d9d9d9;
  --border-color: #434343;
  --input-bg: #06164a;
  --input-border: #434343;
  --button-primary: #012458;
  --button-primary-hover: #4d668a;
  --error-color: #ff6b6b;
  --success-color: #51cf66;
  --subtitle: #edeff1;
  --button-bg: #8edafe;
}

/* Card Styles */
.authCard {
  width: 650px;
  border: 0px solid var(--border-color) !important;
}

:global(html[data-theme='dark']) .authCard,
:global(body[data-theme='dark']) .authCard {
  box-shadow: 'none';
  background-color: var(--button-primary);
}

.authCard :global(.ant-card-body) {
  padding: 32px 24px !important;
}

/* Theme Toggle */
.themeToggle {
  position: absolute;
  top: 24px;
  right: 24px;
  z-index: 10;
}

/* Logo Section */
.logoSection {
  /* text-align: center; */
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.logoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.logoIcon {
  width: 32px;
  height: 32px;
  background: #dc3545;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logoIcon :global(.anticon) {
  font-size: 16px;
  color: white;
}

.logoText {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* Typography */
.formTitle {
  text-align: center !important;
  margin-bottom: 4px !important;
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 46px !important;
  font-family: var(--font-radio-canada);
  margin-top: 10px;
}

.formSubtitle {
  display: block;
  text-align: center;
  margin-bottom: 10px;
  color: var(--subtitle) !important;
  font-size: 18px;
  line-height: 1.4;
  font-family: var(--font-poppins);
  font-weight: 300;
  margin-top: 0px;
}

/* Form Styles */
.formItem {
  margin-bottom: 16px !important;
}

.formItem :global(.ant-form-item-label) {
  padding-bottom: 4px !important;
}

.formItem :global(.ant-form-item-label > label) {
  color: var(--text-primary) !important;
  font-weight: 400;
  font-size: 16px;
}

.formItem :global(.ant-form-item-explain-error) {
  font-size: 12px;
}

.input :global(.ant-input-prefix) {
  margin-right: 8px;
  color: var(--text-secondary);
}

/* Password Input */
.passwordInput {
  height: 50px !important;
  border-radius: 10px !important;
  background: #f5f5f5 !important;
  transition: all 0.2s ease !important;
}

:global(html[data-theme='dark']) .passwordInput {
  background: #06164a !important;
  color: #fff;
}

:global(html[data-theme='dark'])
  .passwordInput
  :global(.ant-input::placeholder) {
  color: #fff !important;
  font-size: 16px;
}

:global(html[data-theme='dark']) .passwordInput:focus,
:global(html[data-theme='dark']) .passwordInput:hover {
  border: 1px solid #fff !important;
}

.passwordInput:focus,
.passwordInput:hover {
  box-shadow: 'none';
  border: 1px solid #012458 !important;
}

.passwordInput :global(.ant-input) {
  background: transparent !important;
  border: none !important;
  color: var(--text-primary) !important;
  font-size: 16px !important;
}

.passwordInput :global(.ant-input::placeholder) {
  color: #bfbfbf !important;
  font-size: 16px;
}

.passwordInput :global(.ant-input-prefix) {
  margin-right: 8px;
  color: var(--text-secondary);
}

/* Button Styles */
.primaryButton {
  width: 100%;
}

.primaryButton:active {
  transform: translateY(0) !important;
}

.primaryButton:disabled {
  background: #6c757d !important;
  transform: none !important;
  box-shadow: none !important;
}

.logoImage {
  margin-bottom: 15px;
  justify-content: center;
  align-items: center;
  display: flex;
  align-self: center;
  text-align: center;
}

.oauthButtons > button {
  margin-top: 15px;
}

.signupLink {
  font-weight: 500;
  color: var(--button-primary);
}

:global(html[data-theme='dark']) .signupLink {
  color: var(--button-bg);
}

.footerNote {
  text-align: center;
  margin-top: 32px;
  color: #999;
}

/* Links and Actions */
.forgotPasswordLink {
  text-align: center;
  font-family: var(--font-poppins);
  font-weight: 500;
}

.forgotPasswordLink a {
  color: var(--button-primary);
  text-decoration: none;
  font-size: 16px;
}

:global(html[data-theme='dark']) .forgotPasswordLink a {
  color: var(--text-primary);
}

.oauthBtn {
  background-color: #edeff1;
}

:global(html[data-theme='dark']) .oauthBtn {
  background-color: #2e4a75;
  color: white;
}

:global(html[data-theme='dark']) .oauthBtn :hover {
  background-color: #2e4a75;
}

.forgotPasswordLink a:hover {
  text-decoration: underline;
}

.backToLoginLink {
  text-align: center;
  margin-top: 15px;
}

.backToLoginLink a {
  color: var(--button-primary);
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.backToLoginLink a:hover {
  text-decoration: underline;
}

/* Remember Me and Actions Row */
.actionsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.rememberMe {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: var(--subtitle);
  font-weight: 300;
  font-family: var(--font-poppins);
  cursor: pointer !important;
}

.rememberMe input {
  margin-right: 8px;
}

/* OTP Input Styles */
.otpContainer {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 24px;
}

.otpInput {
  width: 50px !important;
  height: 50px !important;
  text-align: center !important;
  font-size: 18px !important;
  font-weight: 300 !important;
  border-radius: 8px !important;
  border: none !important;
  transition: all 0.2s ease !important;
  font-family: var(--font-poppins);
}

.otpInput:focus,
.otpInput:hover {
  border-color: var(--button-primary) !important;
  box-shadow: 'none' !important;
  /* background: #8edafe66 !important; */
}

/* Resend Section */
.resendSection {
  text-align: center;
  margin-bottom: 16px;
}

.resendText {
  color: var(--text-secondary);
  font-size: 18px;
  margin-bottom: 8px;
  font-family: var(--font-poppins);
  font-weight: 300;
}

.resendLink {
  color: var(--button-primary);
  text-decoration: none;
  font-size: 18px;
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
  font-family: var(--font-poppins);
}
:global(html[data-theme='dark']) .resendLink {
  color: var(--button-bg);
}

.resendLink:hover {
  text-decoration: underline;
}

.resendLink:disabled {
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* Footer */
.authFooter {
  text-align: center;
  margin-top: 16px;
}

.footerText {
  color: var(--subtitle);
  font-size: 16px;
  font-weight: 300;
  font-family: var(--font-poppins);
}

.newPasswordBtns {
  display: flex;
  gap: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .authCard {
    width: 600px;
  }
  .formTitle {
    font-size: 40px !important;
  }
  .formSubtitle {
    font-size: 16px !important;
  }
  .formItem :global(.ant-form-item-label > label) {
    font-size: 16px !important;
  }
  .passwordInput :global(.ant-input::placeholder) {
    font-size: 16px;
  }
}

@media (max-width: 900px) {
  .authContainer {
    padding: 16px;
  }
  .authCard {
    width: 500px;
  }
  .formTitle {
    font-size: 32px !important;
  }
  .formSubtitle {
    font-size: 14px !important;
  }
  .formItem :global(.ant-form-item-label > label) {
    font-size: 14px !important;
  }
  .authCard :global(.ant-card-body) {
    padding: 24px 20px !important;
  }
  .themeToggle {
    top: 16px;
    right: 16px;
  }
  .passwordInput {
    height: 42px !important;
  }
  .passwordInput :global(.ant-input) {
    font-size: 14px !important;
    height: 22px !important;
  }
  .passwordInput :global(.ant-input::placeholder) {
    color: #bfbfbf !important;
    font-size: 14px;
  }
  .emailInput {
    font-size: 14px;
    height: 42px;
    padding: 0px 15px;
  }
}

.emailInput :global(.ant-input-prefix) {
  margin-right: 8px;
  color: var(--text-secondary);
}

@media (max-width: 600px) {
  .authContainer {
    padding: 12px;
  }

  .authCard {
    width: 75vw;
  }

  .authCard :global(.ant-card-body) {
    padding: 20px 16px !important;
  }

  .formTitle {
    font-size: 24px !important;
  }

  .logoContainer {
    gap: 6px;
  }

  .logoIcon {
    width: 28px;
    height: 28px;
  }

  .logoText {
    font-size: 14px;
  }

  .otpContainer {
    justify-content: space-between;
    gap: 4px;
  }

  .otpInput {
    font-size: 14px !important;
    height: 45px !important;
    width: 45px !important;
  }

  .actionsRow {
    gap: 12px;
    align-items: center;
  }

  .formSubtitle {
    font-size: 12px !important;
    margin-top: 5px;
  }

  .rememberMe {
    font-size: 12px !important;
  }

  .forgotPasswordLink a {
    font-size: 12px !important;
  }

  .footerText {
    font-size: 14px !important;
  }

  .signupLink {
    font-size: 14px !important;
  }

  .formItem :global(.ant-form-item-label > label) {
    font-size: 14px;
  }

  .sendCodeButton {
    margin-bottom: 0;
    position: fixed;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 70vw;
  }
  .backToLoginLink {
    margin-bottom: 0;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
  }

  .resendText {
    font-size: 12px;
    margin-bottom: 0px;
  }

  .resendLink {
    font-size: 12px;
  }

  .newPasswordBtns {
    display: flex;
    flex-direction: column-reverse;
    gap: 20px;
  }

  .passwordInput {
    height: 42px !important;
  }
  .passwordInput :global(.ant-input) {
    font-size: 14px !important;
    height: 22px !important;
  }
  .passwordInput :global(.ant-input::placeholder) {
    color: #bfbfbf !important;
    font-size: 14px;
  }

  .sendCodeButton {
    margin-bottom: 0;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 70vw;
  }

  /* .passwordInput {
    font-size: 14px;
  }

  .passwordInput :global(.ant-input::placeholder) {
    color: #bfbfbf !important;
    font-size: 14px;
  }

  .passwordInput :global(.ant-input) {
    font-size: 14px !important;
  } */
}

@media (max-width: 480px) {
  .authCard {
    width: 95vw;
  }
  .passwordInput :global(.ant-input::placeholder) {
    color: #bfbfbf !important;
    font-size: 14px;
  }
  .passwordInput {
    height: 45px !important;
  }
  .passwordInput :global(.ant-input) {
    font-size: 14px !important;
    height: 22px !important;
  }

  .sendCodeButton {
    margin-bottom: 0;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 90vw;
  }
}

@media (max-width: 320px) {
  .authCard :global(.ant-card-body) {
    padding: 16px 12px !important;
  }

  .formTitle {
    font-size: 20px !important;
  }

  .otpInput {
    width: 36px !important;
    height: 36px !important;
    font-size: 14px !important;
  }
}
