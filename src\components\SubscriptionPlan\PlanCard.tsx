'use client';

import { Card } from 'antd';
import { ReactNode } from 'react';
import useMediaQuery from '../../hooks/useMediaQuery';
import styles from './subscriptionPlan.module.css';

interface PlanCardProps {
  title: string;
  price: string | number;
  perMonthLabel: string;
  description: string | number;
  isActive?: boolean;
  className?: string;
  onClick?: () => void;
  titleRight?: ReactNode;
}

const PlanCard = ({
  title,
  price,
  perMonthLabel,
  description,
  isActive,
  className,
  onClick,
  titleRight,
}: PlanCardProps) => {
  const md = useMediaQuery('(max-width: 900px)');

  return (
    <Card
      className={`${styles.card} ${isActive ? styles.activeCard : ''} ${className ? className : ''}`}
      onClick={onClick}
      bodyStyle={{
        padding: md ? '15px' : '20px',
      }}
    >
      <div className={styles.title}>
        {title} {titleRight}
      </div>
      <div className={styles.price}>
        {price} <span className={styles.month}>{perMonthLabel}</span>
      </div>
      <p className={styles.description}>{description}</p>
    </Card>
  );
};

export default PlanCard;
