'use client';

import { Button, Form, Input, Typography } from 'antd';
import React, { useState } from 'react';

import AuthWrapper, { authStyles } from '@/components/auth/AuthWrapper';
import PasswordChangedFailed from '@/components/auth/password-changed-failed/page';
import PasswordChangedPage from '@/components/auth/password-changed/page';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import siteConfig from '@/config/site.config';
import { useTheme } from '@/contexts/ThemeContext';
import { getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';

const { Title, Text } = Typography;

interface Response {
  status: boolean; // Changed from string to boolean
  message: string;
}

const NewPasswordPage: React.FC = () => {
  const [form] = Form.useForm();
  const { theme } = useTheme();

  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';

  const [loading, setLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const { t } = useTranslation();

  const router = useRouter();

  const tryAgainClick = () => {
    setIsError(false);
    setIsSuccess(false);
    setErrorMessage('');
  };

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');

  const handleSubmit = async (values: {
    password: string;
    new_password: string;
  }) => {
    try {
      setLoading(true);
      const response = await getApiData<
        {
          email: string;
          password: string;
          confirm_password: string;
          type: string;
        },
        Response
      >({
        url: `${siteConfig.apiUrl}${Endpoints.changePassword}`,
        method: 'POST',
        data: {
          email,
          password: values.password,
          confirm_password: values.new_password,
          type: 'forgot_password_verification',
        },
        customUrl: true,
      });

      if (response && response.status === true) {
        setIsSuccess(true);
        setLoading(false);
      } else {
        setIsError(true);
        setErrorMessage(response?.message || 'Failed to change password');
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      setIsError(true);
      setErrorMessage(
        error?.message || 'Something went wrong.Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  return isError ? (
    <PasswordChangedFailed
      errorMessage={errorMessage}
      tryAgainClick={tryAgainClick}
    />
  ) : isSuccess ? (
    <PasswordChangedPage />
  ) : (
    <AuthWrapper>
      <div className={authStyles.logoSection}>
        <Image
          src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
          alt='Swiss Trust Layer'
          className={authStyles.logoImage}
          width={180}
          height={180}
          style={{
            width: xs ? '175px' : sm ? '190px' : '220px',
            height: 'auto',
          }}
        />

        <Title level={2} className={authStyles.formTitle}>
          {t('newPassword.title')}
        </Title>
        <Text className={authStyles.formSubtitle}>
          {t('newPassword.subtitle')}
          <br /> {t('newPassword.subtitle1')}
        </Text>
      </div>

      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        autoComplete='off'
      >
        <Form.Item
          label={t('newPassword.password')}
          name='password'
          rules={[
            {
              required: true,
              message: `${t('newPassword.required_password')}`,
            },
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('newPassword.password_placeholder')}
            className={authStyles.passwordInput}
          />
        </Form.Item>

        <Form.Item
          label={t('newPassword.new_password')}
          name='new_password'
          dependencies={['password']}
          rules={[
            {
              required: true,
              message: `${t('newPassword.required_confirm_password')}`,
            },
            { min: 6, message: t('newPassword.password_min_length') },
            {
              pattern:
                /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]/,
              message: t('newPassword.password_pattern'),
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t('newPassword.passwords_not_match'))
                );
              },
            }),
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('newPassword.confirm_password_placeholder')}
            className={authStyles.passwordInput}
          />
        </Form.Item>

        <Form.Item
          style={{ marginBottom: 0, marginTop: '40px' }}
          className={authStyles.sendCodeButton}
        >
          <div className={authStyles.newPasswordBtns}>
            <Button
              variant='outlined'
              className={authStyles.primaryButton}
              onClick={() => {
                router.push('/forgot-password');
              }}
            >
              {t('newPassword.cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={loading}
              className={authStyles.primaryButton}
            >
              {t('newPassword.change_password')}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </AuthWrapper>
  );
};

export default NewPasswordPage;
