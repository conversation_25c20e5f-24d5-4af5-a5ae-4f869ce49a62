/* CreateFolderScreen CSS Module */

.header {
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.folderIcon {
  width: 64px;
  height: 64px;
  background: var(--color-secondary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.folderIcon .anticon {
  color: var(--color-white);
  font-size: 50px;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
  margin: 0 !important;
}

.formContainer {
  /* padding: 32px; */
}

.formSection {
  margin-bottom: 24px;
}

.sectionLabel {
  display: block !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  color: var(--color-text-primary) !important;
  margin-bottom: 0px !important;
}

.actionButtons {
  display: flex;
  gap: 16px;
  justify-content: flex-start;
  margin-top: 32px;
}

.cancelButton {
  height: 48px !important;
  padding: 0 32px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  background-color: var(--color-border) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text-secondary) !important;
}

.cancelButton:hover {
  background-color: var(--color-border-secondary) !important;
  border-color: var(--color-border-secondary) !important;
  color: var(--color-text-primary) !important;
}

@media (max-width: 900px) {
  .pageTitle {
    font-size: 26px !important;
  }
  .folderIcon {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
  }
  .pageTitle {
    font-size: 24px !important;
  }
  .folderIcon {
    width: 50px;
    height: 50px;
  }
  .createButton {
    width: 100%;
  }
  .cancelButton {
    width: 100%;
  }
}
