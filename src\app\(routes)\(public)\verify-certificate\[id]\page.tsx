// pages/verify-certicate/[id].tsx
'use client';
import { useNotification } from '@/components/Notification/NotificationContext';
import { useTranslation } from '@/hooks/useTranslation';
import { Spin, Typography } from 'antd';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { Endpoints } from '../../../../../config/endpoints';
import siteConfig from '../../../../../config/site.config';
import { getApiData } from '../../../../../helpers/ApiHelper';

const VerifyCertificatePage = () => {
  const pathname = usePathname();
  const { t } = useTranslation();
  const { Title } = Typography;
  const folderId = pathname.split('/').pop();
  const notification = useNotification();
  const [htmlContent, setHtmlContent] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  interface verifyCerticateResponse {
    status: boolean;
    data: {
      html: string;
    };
  }
  useEffect(() => {
    if (!folderId || typeof folderId !== 'string') {
      return;
    }
    fetchCertificate();
  }, [folderId]);

  const fetchCertificate = async () => {
    try {
      setLoading(true);
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      const response = await getApiData<{}, verifyCerticateResponse>({
        url: `${siteConfig.apiUrl}${Endpoints.verifyCertificate}`,
        method: 'POST',
        data: { folder_id: folderId },
        customUrl: true,
        headers: {},
        formData: true,
      });
      console.log('response=====>>>>>', response);
      if (response && response.status === true) {
        setHtmlContent(response?.data?.html); // since API returns HTML string
      } else {
        notification.error({
          message: t('certificate.certificate_verification_failed'),
          description: t('myProfile.profile_update_failed_description'),
        });
      }
    } catch (error) {
      console.log('🚀 ~ fetchCertificate ~ error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Spin
        size='large'
        style={{ display: 'flex', justifyContent: 'center', marginTop: 50 }}
      />
    );
  }

  if (!htmlContent) {
    return (
      <div style={{ padding: 40, textAlign: 'center', marginTop: 50 }}>
        <Title level={3}>{t('certificate.no_certificate_found')}</Title>
      </div>
    );
  }

  return (
    // <div
    //   style={{ padding: 24 }}
    //   dangerouslySetInnerHTML={{ __html: htmlContent || '' }}
    // />
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <iframe
        srcDoc={htmlContent}
        style={{
          width: isMobile ? '100%' : '60vw',
          height: '100vh',
          border: 'none',
          backgroundColor: 'white',
        }}
        title='Big Certificate'
      />
    </div>
  );
};

export default VerifyCertificatePage;
