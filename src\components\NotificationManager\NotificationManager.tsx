'use client';

import {
  generateToken,
  reqNotificationPermission,
} from '@/notifications/firebase';
import { useAppSelector } from '@/store/hooks';
import React, { useEffect, useState } from 'react';
import { useNotificationContext } from '../FirebaseNotification/NotificationProvider';
import { useNotification } from '../Notification/NotificationContext';

interface NotificationManagerProps {
  autoPrompt?: boolean;
  promptDelay?: number;
}

const NotificationManager: React.FC<NotificationManagerProps> = ({
  autoPrompt = true,
  promptDelay = 2000,
}) => {
  const { user } = useAppSelector(state => state.auth);
  const { isFirebaseReady, showInAppNotification } = useNotificationContext();
  const notification = useNotification();

  const [permissionStatus, setPermissionStatus] =
    useState<NotificationPermission | null>('default');
  const [hasRequestedPermission, setHasRequestedPermission] = useState(false);

  const requestNotificationPermission = async () => {
    if (hasRequestedPermission) {
      console.log('🔔 Permission already requested in this session');
      return;
    }

    setHasRequestedPermission(true);

    try {
      if (!isFirebaseReady) {
        console.warn('⚠️ Firebase not ready, cannot request permission');
        return;
      }

      console.log(
        '🔔 Requesting notification permission using browser default dialog...'
      );

      const permission = await reqNotificationPermission();
      setPermissionStatus(permission);

      console.log('📋 Permission result:', permission);

      switch (permission) {
        case 'granted':
          console.log('✅ Notification permission granted');

          try {
            const isStoredToken = localStorage.getItem('fcm_token');
            // if (isStoredToken) {
            //   saveToken(isStoredToken);
            //   return false;
            // }
            await generateToken(isStoredToken ? isStoredToken : '');
            showInAppNotification(
              'Notifications Enabled!',
              'You will now receive important updates about your sealed documents.',
              'success'
            );
          } catch (tokenError) {
            console.error('❌ Error generating FCM token:', tokenError);
            notification.error({
              message: 'Token Generation Failed',
              description:
                'Permission granted but failed to set up notifications. Please try again.',
            });
          }
          break;

        case 'denied':
          console.log('🚫 Notification permission denied');
          notification.warning({
            message: 'Notifications Blocked',
            description:
              'To receive notifications, please enable them in your browser settings and refresh the page.',
          });
          break;

        case 'default':
          console.log('⏸️ Notification permission dismissed');
          break;

        default:
          console.warn('⚠️ Unknown permission state:', permission);
          notification.warning({
            message: 'Unknown Permission State',
            description:
              'An unexpected permission state was returned. Please try again.',
          });
      }
    } catch (error) {
      console.error('❌ Error requesting notification permission:', error);
      notification.error({
        message: 'Permission Request Failed',
        description:
          'Failed to request notification permission. Please try again.',
      });
    }
  };

  useEffect(() => {
    const handleNotificationPermission = async () => {
      if (typeof window === 'undefined' || !('Notification' in window)) {
        console.warn('⚠️ Notifications not supported in this browser');
        return;
      }

      const currentPermission = Notification.permission;
      setPermissionStatus(currentPermission);
      console.log(
        '🔔 Current check notification permission:',
        autoPrompt &&
          user?.notification_permission &&
          isFirebaseReady &&
          currentPermission === 'default' &&
          !hasRequestedPermission,
        autoPrompt,
        user?.notification_permission,
        isFirebaseReady,
        currentPermission === 'default',
        !hasRequestedPermission
      );

      if (
        autoPrompt &&
        user?.notification_permission &&
        isFirebaseReady &&
        currentPermission === 'default' &&
        !hasRequestedPermission
      ) {
        // const timer = setTimeout(async () => {
        await requestNotificationPermission();
        // }, 500);
        // return () => clearTimeout(timer);
      }
      console.log('🔔 Existing permission check:', currentPermission);
      if (
        currentPermission === 'granted' &&
        user?.notification_permission &&
        isFirebaseReady &&
        !hasRequestedPermission
      ) {
        setHasRequestedPermission(true);
        try {
          // await generateToken();
          console.log('✅ FCM token generated for existing permission');
        } catch (error) {
          console.error(
            '❌ Error generating FCM token for existing permission:',
            error
          );
        }
      }
    };

    handleNotificationPermission();
  }, [
    autoPrompt,
    promptDelay,
    isFirebaseReady,
    user?.notification_permission,
    hasRequestedPermission,
  ]);

  useEffect(() => {
    console.log('🔔 Notification permission status:', permissionStatus);
    console.log('🔔 User notification setting:', user?.notification_permission);
    console.log('🔔 Firebase ready:', isFirebaseReady);
    console.log('🔔 Has requested permission:', hasRequestedPermission);
  }, [
    permissionStatus,
    user?.notification_permission,
    isFirebaseReady,
    hasRequestedPermission,
  ]);

  return null;
};

export default NotificationManager;
