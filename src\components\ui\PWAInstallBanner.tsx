/**
 * @fileoverview PWA installation banner component for promoting app installation
 * @module components/ui/PWAInstallBanner
 */

'use client';

import { CloseOutlined, DownloadOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

import { useTranslation } from '@/hooks/useTranslation';
import styles from './PWAInstallBanner.module.css';

const { Text } = Typography;

/**
 * Extended Event interface for PWA installation prompt
 * @interface BeforeInstallPromptEvent
 * @extends {Event}
 * @property {string[]} platforms - Available platforms for installation
 * @property {Promise<{outcome: 'accepted' | 'dismissed', platform: string}>} userChoice - User's installation choice
 * @property {Function} prompt - Function to trigger installation prompt
 */
interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

/**
 * PWA installation banner component that promotes app installation
 *
 * @component
 * @returns {JSX.Element | null} The rendered banner component or null if not applicable
 *
 * @example
 * // Basic usage - typically placed in the main layout
 * <PWAInstallBanner />
 *
 * @description
 * This component displays a banner encouraging users to install the PWA.
 * Features include:
 * - Automatic detection of PWA installation capability
 * - Platform-specific installation instructions
 * - Dismissal functionality with timeout
 * - Integration with browser's beforeinstallprompt event
 * - Internationalized text content
 *
 * The banner automatically hides if:
 * - The app is already installed
 * - User has recently dismissed it
 * - Installation is not supported
 */
const PWAInstallBanner: React.FC = () => {
  const { t } = useTranslation();
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [showBanner, setShowBanner] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return true;
      }

      // Check for iOS Safari
      if ((window.navigator as { standalone?: boolean }).standalone === true) {
        setIsInstalled(true);
        return true;
      }

      return false;
    };

    const isAlreadyInstalled = checkIfInstalled();

    if (!isAlreadyInstalled) {
      // Check if user has dismissed the banner recently
      const lastDismissed = localStorage.getItem('pwa-banner-dismissed');
      if (lastDismissed) {
        const dismissedTime = parseInt(lastDismissed, 10);
        const now = Date.now();
        // Show again after 1 hour for testing
        if (now - dismissedTime < 60 * 60 * 1000) {
          return;
        }
      }

      // Show banner after a short delay even without beforeinstallprompt
      const timer = setTimeout(() => {
        if (!isInstalled) {
          setShowBanner(true);
        }
      }, 2000);

      // Listen for the beforeinstallprompt event
      const handleBeforeInstallPrompt = (e: Event) => {
        e.preventDefault();
        setDeferredPrompt(e as BeforeInstallPromptEvent);
        setShowBanner(true);
      };

      // Listen for app installed event
      const handleAppInstalled = () => {
        setIsInstalled(true);
        setShowBanner(false);
        setDeferredPrompt(null);
      };

      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.addEventListener('appinstalled', handleAppInstalled);

      return () => {
        clearTimeout(timer);
        window.removeEventListener(
          'beforeinstallprompt',
          handleBeforeInstallPrompt
        );
        window.removeEventListener('appinstalled', handleAppInstalled);
      };
    }
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        await deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;

        if (outcome === 'accepted') {
          setIsInstalled(true);
        }

        setShowBanner(false);
        setDeferredPrompt(null);
      } catch {
        // Handle installation error silently
        setShowBanner(false);
      }
    } else {
      // Fallback for browsers that don't support beforeinstallprompt
      // Show instructions for manual installation
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isAndroid = /Android/.test(navigator.userAgent);

      let instructions = '';
      if (isIOS) {
        instructions = t('pwa.install_ios_instructions');
      } else if (isAndroid) {
        instructions = t('pwa.install_android_instructions');
      } else {
        instructions = t('pwa.install_general_instructions');
      }

      // Show instructions in a better way
      const instructionDiv = document.createElement('div');
      instructionDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 300px;
        text-align: center;
        font-family: system-ui, -apple-system, sans-serif;
      `;
      instructionDiv.innerHTML = `
        <p style="margin: 0 0 15px 0; color: #333;">${instructions}</p>
        <button onclick="this.parentElement.remove()" style="
          background: #007bff;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        ">Got it</button>
      `;
      document.body.appendChild(instructionDiv);

      setShowBanner(false);
    }
  };

  const handleDismiss = () => {
    setShowBanner(false);
    // Don't show again for 7 days
    localStorage.setItem('pwa-banner-dismissed', Date.now().toString());
  };

  // Don't show if already installed or not ready
  if (isInstalled || !showBanner) {
    return null;
  }

  return (
    <div className={styles.installBanner}>
      <div className={styles.bannerContent}>
        <div className={styles.bannerIcon}>
          <DownloadOutlined />
        </div>
        <div className={styles.bannerText}>
          <Text strong className={styles.bannerTitle}>
            {t('pwa.install_title')}
          </Text>
          <Text className={styles.bannerDescription}>
            {t('pwa.install_description')}
          </Text>
        </div>
        <div className={styles.bannerActions}>
          <Button
            type='primary'
            size='small'
            icon={<DownloadOutlined />}
            onClick={handleInstallClick}
            className={styles.installButton}
          >
            {t('pwa.install_button')}
          </Button>
          <Button
            type='text'
            size='small'
            icon={<CloseOutlined />}
            onClick={handleDismiss}
            className={styles.dismissButton}
          />
        </div>
      </div>
    </div>
  );
};

export default PWAInstallBanner;
