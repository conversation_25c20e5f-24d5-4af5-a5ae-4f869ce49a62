'use client';

import React, { useEffect, useState } from 'react';

import LoadingScreen from '@/components/ui/LoadingScreen';
import { useAppSelector } from '@/store/hooks';
import { usePathname } from 'next/navigation';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

/**
 * Simplified ProtectedRoute component that works with middleware.ts
 * Middleware handles redirects, this component just shows loading states
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
}) => {
  const { isAuthenticated, loading } = useAppSelector(state => state.auth);
  const [isChecking, setIsChecking] = useState(true);
  const pathName = usePathname();

  const ignoreRoutes = ['/notification-permission'];
  const isIgnoreRoute = ignoreRoutes.some(route => pathName.startsWith(route));

  useEffect(() => {
    // Small delay to prevent flash of loading screen
    const timer = setTimeout(() => {
      if (!loading) {
        setIsChecking(false);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [loading]);

  // Show loading screen while checking authentication
  if ((loading || isChecking) && !isAuthenticated) {
    return <LoadingScreen text='Checking authentication...' />;
  }

  // If authentication is required but user is not authenticated
  // Middleware should handle redirect, but show loading just in case
  if (requireAuth && !isAuthenticated) {
    return <LoadingScreen text='' />;
  }

  // If authentication is NOT required but user IS authenticated
  // Middleware should handle redirect, but show loading just in case
  if (!requireAuth && isAuthenticated && !isIgnoreRoute) {
    return <LoadingScreen text='Redirecting to dashboard...' />;
  }

  // Render children if all conditions are met
  return <>{children}</>;
};

export default ProtectedRoute;
