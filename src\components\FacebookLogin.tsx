'use client';

/**
 * @fileoverview Facebook SDK loader utility for social authentication
 * @module utils/loadFbSdk
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * Dynamically loads the Facebook SDK for JavaScript
 * This function ensures the Facebook SDK is loaded only once and provides
 * a promise-based interface for initialization
 *
 * @function loadFacebookSDK
 * @returns {Promise<void>} Promise that resolves when Facebook SDK is loaded and initialized
 * @example
 * // Load Facebook SDK before using Facebook login
 * await loadFacebookSDK();
 * // Now you can use FB.login() and other Facebook API methods
 *
 * @example
 * // With error handling
 * try {
 *   await loadFacebookSDK();
 *   console.log('Facebook SDK loaded successfully');
 * } catch (error) {
 *   console.error('Failed to load Facebook SDK:', error);
 * }
 */

import { loadFacebookSDK } from '@/utils/loadFbSdk';
import { Button } from 'antd';
import { useEffect } from 'react';

import { useTranslation } from '@/hooks/useTranslation';
import { FacebookFilled } from '@ant-design/icons';
import { authStyles } from './auth/AuthWrapper';
import { useNotification } from './Notification/NotificationContext';

interface Props {
  loader?: boolean;
  onSuccess: (response: any) => void;
}

export default function FacebookLogin({ loader = false, onSuccess }: Props) {
  const { t } = useTranslation();
  const notification = useNotification();
  useEffect(() => {
    loadFacebookSDK();
  }, []);

  const handleFBLogin = () => {
    const FB = (window as any).FB;
    FB.login(
      (response: any) => {
        if (response.authResponse) {
          console.log('Logged in FB response:', response);
          FB.getLoginStatus((statusResponse: any) => {
            if (statusResponse.status === 'connected') {
              // ✅ user is logged in to FB and authorized your app
              onSuccess({
                access_token: statusResponse.authResponse.accessToken,
                ...statusResponse,
              });
            } else {
              notification.error({
                message: 'Login Failed',
                description: 'User cancelled login or did not authorize.',
              });
            }
          });
        } else {
          notification.error({
            message: 'Login Failed',
            description: 'User cancelled login or did not authorize.',
          });
          console.log('User cancelled login or did not authorize.');
        }
      },
      {
        scope: 'public_profile,email',
        enable_profile_selector: true,
        display: 'popup',
        auth_type: 'reauthenticate',
      }
    );
  };

  return (
    <Button
      icon={<FacebookFilled />}
      block
      onClick={handleFBLogin}
      loading={loader}
      className={authStyles.oauthBtn}
    >
      {t('login.continue_with_facebook')}
    </Button>
  );
}
