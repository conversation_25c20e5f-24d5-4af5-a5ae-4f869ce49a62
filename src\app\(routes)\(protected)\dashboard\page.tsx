'use client';

import React from 'react';

import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Dashboard from '@/components/dashboard/Dashboard';

/**
 * Dashboard page - Protected route that requires authentication
 */
const DashboardPage: React.FC = () => {
  return (
    <ProtectedRoute requireAuth={true}>
      <Dashboard />
    </ProtectedRoute>
  );
};

export default DashboardPage;
