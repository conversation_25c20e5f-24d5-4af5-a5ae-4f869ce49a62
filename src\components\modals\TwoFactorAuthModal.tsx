'use client';

import { Button, Input, InputRef, Modal, Typography } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { Endpoints } from '../../config/endpoints';
import { ApiResponse, getApiData } from '../../helpers/ApiHelper';
import { useTranslation } from '../../hooks/useTranslation';
import { useAppSelector } from '../../store/hooks';
import { LoginResponse } from '../auth/AuthFunction';
import { useNotification } from '../Notification/NotificationContext';
import LoadingScreen from '../ui/LoadingScreen';
import styles from './Modal.module.css';

interface VerificationModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface response {
  status?: boolean;
}

const { Title, Text } = Typography;

const TwoFactorAuthModal: React.FC<VerificationModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const inputRefs = useRef<(InputRef | null)[]>([]);
  const notification = useNotification();
  const { user } = useAppSelector(state => state.auth);

  const { t } = useTranslation();

  const [state, setState] = useState({
    qrCode: '',
    data: {
      qrCodeDataURL: '',
      secret: '',
    },
  });

  const getQrCode = async () => {
    try {
      // Simulate API call
      setLoading(true);
      const res = await getApiData<{ email?: string }, ApiResponse>({
        url: Endpoints.enable2FA,
        method: 'POST',
        data: {
          email: user?.email || '',
        },
      });

      if (res && res.status === true) {
        console.log('res', res);
        setState(p => ({ ...p, data: res.data }));
        notification.info({
          message: 'QR Code',
          description: res?.message || 'QR Code generated successfully',
        });
      }
      setLoading(false);
    } catch (error) {
      console.log('Error fetching QR code:', error);
    }
  };

  useEffect(() => {
    if (visible) {
      getQrCode();
    }
    return () => {};
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const handleOTPChange = (index: number, value: string) => {
    if (value.length > 1) {
      return;
    }

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = async () => {
    console.log('otp', otp);

    const otpCode = otp.join('');
    if (otpCode.length !== 6) {
      notification.error({
        message: 'QR Code',
        description: 'Please enter the complete 6-digit code',
      });
      return;
    }

    setBtnLoading(true);

    try {
      const res = await getApiData<
        { token: string; email: string },
        LoginResponse
      >({
        url: Endpoints.verify2FA,
        method: 'POST',
        data: {
          token: otpCode,
          email: user?.email || '',
        },
      });

      if (res && res?.status === true) {
        notification.success({
          message: 'QR Code',
          description: res?.message || '2FA successfully enabled.',
        });
        onSuccess();
      } else {
        notification.error({
          message: 'QR Code',
          description: res?.message || 'Verification failed. Please try again.',
        });
      }
      setBtnLoading(false);
    } catch (error) {
      setBtnLoading(false);
      notification.error({
        message: 'QR Code',
        description: error?.message || 'Verification failed. Please try again.',
      });
    }
  };

  const handleModalClose = () => {
    // Reset the OTP state when modal closes
    setOtp(['', '', '', '', '', '']);
    onClose();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleModalClose} // This handles the close icon click
      footer={null}
      centered
      width={600}
      maskClosable={false} // Prevents closing when clicking outside the modal
      closable={true} // Shows the close icon (X button)
      keyboard={false} // Optional: prevents closing with ESC key
    >
      <div className={styles.container}>
        {loading ? (
          <LoadingScreen text={t('twoFactorAuthentication.loading')} />
        ) : (
          <>
            <div className={styles.logoSection}>
              <Title level={2} className={styles.title}>
                {t('twoFactorAuthentication.title')}
              </Title>
              {state.data?.qrCodeDataURL && (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={state.data?.qrCodeDataURL}
                  alt='Swiss Trust Layer'
                  className={styles.logoImage}
                  width={150}
                  height={150}
                  style={{
                    width: isMobile ? '92px' : '150px',
                    height: 'auto',
                    marginTop: '20px',
                    marginBottom: '20px',
                  }}
                />
              )}
              <Text className={styles.subTitle}>
                {t('twoFactorAuthentication.subTitle')}
              </Text>
            </div>

            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                gap: '8px',
                marginBottom: '24px',
              }}
            >
              {otp.map((digit, index) => (
                <Input
                  key={index}
                  ref={el => {
                    inputRefs.current[index] = el;
                  }}
                  value={digit}
                  onChange={e => {
                    const value = e.target.value;
                    if (/^[0-9]*$/.test(value)) {
                      // Only allow digits ( no special characters)
                      handleOTPChange(index, value);
                    }
                  }}
                  onKeyDown={e => handleKeyDown(index, e)}
                  maxLength={1}
                  style={{
                    width: isMobile ? '45px' : '50px',
                    height: isMobile ? '45px' : '50px',
                    textAlign: 'center',
                    fontSize: '18px',
                  }}
                />
              ))}
            </div>

            <div>
              <Button
                type='primary'
                onClick={handleVerify}
                loading={btnLoading}
                disabled={otp.join('').length !== 6}
                className={styles.primaryButton}
              >
                {t('twoFactorAuthentication.activate_account')}
              </Button>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default TwoFactorAuthModal;
