import { images } from '@/config/images';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { updateUser } from '@/store/slices/authSlice';
import { <PERSON><PERSON>, Spin } from 'antd';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { generateToken } from '../../notifications/firebase';
import { useNotificationContext } from '../FirebaseNotification/NotificationProvider';
import { useNotification } from '../Notification/NotificationContext';
import styles from './notificationPermission.module.css';

const NotificationPermission: React.FC = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const notification = useNotification();
  const { isFirebaseReady, showInAppNotification } = useNotificationContext();

  const [isLoading, setIsLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] =
    useState<NotificationPermission | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Check current permission status on component mount
  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setPermissionStatus(Notification.permission);
    }
  }, []);

  const handleEnableNotifications = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check if notifications are supported
      if (!('Notification' in window)) {
        setError('Notifications are not supported in this browser.');
        setIsLoading(false);
        return;
      }

      console.log('🔔 Requesting notification permission...');
      const permission = await generateToken();
      console.log('📋 Permission result:', permission);
      setPermissionStatus(permission);

      if (permission === 'granted') {
        showInAppNotification(
          'Notifications Enabled!',
          'You will now receive notifications about your sealed documents and account updates.',
          'success'
        );

        // Update user state and navigate
        dispatch(updateUser({ user: { ...user }, isAuthenticated: true }));
        setTimeout(() => {
          router.replace('/dashboard');
        }, 2000);
      } else if (permission === 'denied') {
        setTimeout(() => {
          router.replace('/dashboard');
        }, 2000);
        notification.error({
          message: 'Notifications Blocked',
          description:
            'Please enable notifications in your browser settings to receive important updates.',
        });
      } else {
        setTimeout(() => {
          router.replace('/dashboard');
        }, 2000);
        notification.error({
          message: 'Notifications Blocked',
          description:
            'Notification permission was not granted. You can enable them later in settings.',
        });
      }
    } catch (error) {
      console.error('❌ Error enabling notifications:', error);
      setError('Failed to enable notifications. Please try again.');
      setTimeout(() => {
        router.replace('/dashboard');
      }, 2000);
      notification.error({
        message: 'Error',
        description: 'Failed to enable notifications. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    dispatch(updateUser({ user: { ...user }, isAuthenticated: true }));
    router.replace('/dashboard');
  };

  return (
    <div className={styles.container}>
      {/* Title */}
      <h1 className={styles.title}>{t('notification.turn_on_notification')}</h1>

      {/* Subtitle */}
      <p className={styles.subtitle}>{t('notification.stay_up_to_date')}</p>

      {/* Illustration */}
      <div className={styles.illustration}>
        <Image
          src={
            theme === 'dark'
              ? images.notificationPermissionDark
              : images.notificationPermissionLight
          }
          alt='Notification illustration'
          width={350}
          height={350}
          className={styles.illustrationImg}
        />
      </div>

      {/* Buttons */}
      <div className={styles.buttonGroup}>
        <Button
          type='primary'
          className={styles.agreeButton}
          onClick={handleEnableNotifications}
          loading={isLoading}
          // disabled={!isFirebaseReady || permissionStatus === 'granted'}
        >
          {isLoading ? (
            <>
              <Spin size='small' style={{ marginRight: 8 }} />
              {t('notification.enabling')}
            </>
          ) : permissionStatus === 'granted' ? (
            'Notifications Enabled'
          ) : (
            t('notification.enable_notification')
          )}
        </Button>

        <Button
          variant='outlined'
          className={styles.primaryButton}
          onClick={handleSkip}
          disabled={isLoading}
        >
          {t('notification.maybe_later')}
        </Button>
      </div>
    </div>
  );
};

export default NotificationPermission;
