/**
 * @fileoverview Typed wrapper functions for commonly used Lodash utilities
 * @module helpers/lodashFunctions
 * @description This module provides type-safe wrappers around Lodash functions
 * to ensure better TypeScript integration and consistent usage across the application.
 */

'use client';

/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  cloneDeep as lodashCloneDeep,
  debounce as lodashDebounce,
  flattenDeep as lodashFlattenDeep,
  isArray as lodashIsArray,
  isEmpty as lodashIsEmpty,
  isNull as lodashIsNull,
  isNumber as lodashIsNumber,
  isObject as lodashIsObject,
  isString as lodashIsString,
  isUndefined as lodashIsUndefined,
  lowerCase as lodashLowerCase,
  startCase as lodashStartCase,
  uniqBy as lodashUniqBy,
  upperCase as lodashUpperCase,
} from 'lodash-es';

// ✅ Explicitly type reverse to fix type inference issue
import reverseOrig from 'lodash-es/reverse';
const lodashReverse = reverseOrig as <T>(array: T[]) => T[];

/**
 * Creates an array of unique values from array, using iteratee to generate the criterion by which uniqueness is computed
 * @template T - Type of array elements
 * @param {T[]} array - The array to inspect
 * @param {string|Function} iteratee - The iteratee invoked per element
 * @returns {T[]} Returns the new duplicate free array
 * @example
 * uniqBy([{ id: 1, name: 'John' }, { id: 2, name: 'Jane' }, { id: 1, name: 'John' }], 'id')
 * // => [{ id: 1, name: 'John' }, { id: 2, name: 'Jane' }]
 */
export const uniqBy = <T>(
  array: T[],
  iteratee: string | ((value: T) => any)
): T[] => lodashUniqBy(array, iteratee);

/**
 * Checks if value is an empty object, collection, map, or set
 * @param {unknown} value - The value to check
 * @returns {boolean} Returns true if value is empty, else false
 * @example
 * isEmpty(null) // => true
 * isEmpty([]) // => true
 * isEmpty({}) // => true
 * isEmpty('') // => true
 */
export const isEmpty = (value: unknown): boolean => lodashIsEmpty(value);

/**
 * Checks if value is classified as an Array object
 * @param {unknown} value - The value to check
 * @returns {boolean} Returns true if value is an array, else false
 * @example
 * isArray([1, 2, 3]) // => true
 * isArray('abc') // => false
 */
export const isArray = (value: unknown): value is any[] => lodashIsArray(value);

/**
 * Checks if value is the language type of Object
 * @param {unknown} value - The value to check
 * @returns {boolean} Returns true if value is an object, else false
 * @example
 * isObject({}) // => true
 * isObject([1, 2, 3]) // => true
 * isObject(null) // => false
 */
export const isObject = (value: unknown): value is Record<string, any> =>
  lodashIsObject(value);

/**
 * Checks if value is undefined
 * @param {unknown} value - The value to check
 * @returns {boolean} Returns true if value is undefined, else false
 * @example
 * isUndefined(void 0) // => true
 * isUndefined(null) // => false
 */
export const isUndefined = (value: unknown): value is undefined =>
  lodashIsUndefined(value);

/**
 * Checks if value is null
 * @param {unknown} value - The value to check
 * @returns {boolean} Returns true if value is null, else false
 * @example
 * isNull(null) // => true
 * isNull(void 0) // => false
 */
export const isNull = (value: unknown): value is null => lodashIsNull(value);

/**
 * Converts string to lower case
 * @param {string} value - The string to convert
 * @returns {string} Returns the lower cased string
 * @example
 * lowerCase('--Foo-Bar--') // => 'foo bar'
 * lowerCase('fooBar') // => 'foo bar'
 */
export const lowerCase = (value: string): string => lodashLowerCase(value);

/**
 * Converts string to start case
 * @param {string} value - The string to convert
 * @returns {string} Returns the start cased string
 * @example
 * startCase('--foo-bar--') // => 'Foo Bar'
 * startCase('fooBar') // => 'Foo Bar'
 */
export const startCase = (value: string): string => lodashStartCase(value);

/**
 * Checks if value is classified as a String primitive or object
 * @param {unknown} value - The value to check
 * @returns {boolean} Returns true if value is a string, else false
 * @example
 * isString('abc') // => true
 * isString(1) // => false
 */
export const isString = (value: unknown): value is string =>
  lodashIsString(value);

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds
 * @template F - Type of the function to debounce
 * @param {F} func - The function to debounce
 * @param {number} wait - The number of milliseconds to delay
 * @returns {Function} Returns the new debounced function
 * @example
 * const debouncedSave = debounce(saveInput, 300);
 * // Will only call saveInput after 300ms of no new calls
 */
export const debounce = <F extends (...args: any[]) => any>(
  func: F,
  wait: number
): ((...args: Parameters<F>) => void) => lodashDebounce(func, wait);

/**
 * Checks if value is classified as a Number primitive or object
 * @param {unknown} value - The value to check
 * @returns {boolean} Returns true if value is a number, else false
 * @example
 * isNumber(3) // => true
 * isNumber(Number.MIN_VALUE) // => true
 * isNumber('3') // => false
 */
export const isNumber = (value: unknown): value is number =>
  lodashIsNumber(value);

/**
 * Recursively flattens array
 * @template T - Type of array elements
 * @param {T[]} value - The array to flatten
 * @returns {T[]} Returns the new flattened array
 * @example
 * flattenDeep([1, [2, [3, [4]], 5]]) // => [1, 2, 3, 4, 5]
 */
export const flattenDeep = <T>(value: T[]): T[] => lodashFlattenDeep(value);

/**
 * Creates a deep clone of value
 * @template T - Type of the value to clone
 * @param {T} value - The value to recursively clone
 * @returns {T} Returns the deep cloned value
 * @example
 * const objects = [{ 'a': 1 }, { 'b': 2 }];
 * const deep = cloneDeep(objects);
 * console.log(deep[0] === objects[0]); // => false
 */
export const cloneDeep = <T>(value: T): T => lodashCloneDeep(value);

/**
 * Reverses array so that the first element becomes the last, the second element becomes the second to last, and so on
 * Note: This function creates a copy of the array before reversing to avoid mutating the original
 * @template T - Type of array elements
 * @param {T[]} array - The array to reverse
 * @returns {T[]} Returns the reversed array
 * @example
 * reverse([1, 2, 3]) // => [3, 2, 1]
 */
export const reverse = <T>(array: T[]): T[] => lodashReverse([...array]);

/**
 * Converts string to upper case
 * @param {string} value - The string to convert
 * @returns {string} Returns the upper cased string
 * @example
 * upperCase('--foo-bar') // => 'FOO BAR'
 * upperCase('fooBar') // => 'FOO BAR'
 */
export const upperCase = (value: string): string => lodashUpperCase(value);
