// components/StepOne.tsx
import { useAppSelector } from '@/store/hooks';
import { DatePicker, Form, FormInstance, Input, Select } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import styles from './TellAbout.module.css';

const { Option } = Select;

interface formDataInterface {
  gender?: string;
  birthday?: dayjs.Dayjs;
  purpose?: string;
}

const StepOne = ({ form }: { form: FormInstance }) => {
  const { user } = useAppSelector(state => state.auth);
  const { t } = useTranslation();

  useEffect(() => {
    if (user) {
      const formData: formDataInterface = {};

      if (user.gender) {
        formData.gender = user.gender;
      }

      if (user.birth_date) {
        // Convert DD/MM/YYYY to dayjs object
        formData.birthday = dayjs(user.birth_date, 'DD/MM/YYYY');
      }

      if (user.purpose_of_joining) {
        formData.purpose = user.purpose_of_joining;
      }

      form.setFieldsValue(formData);
    }
  }, [user, form]);

  return (
    <div className={styles.container}>
      <Form.Item
        name='gender'
        label={t('tell_about.gender')}
        rules={[{ required: true }]}
      >
        <Select placeholder={t('tell_about.choose_gender')}>
          <Option value='male'>{t('tell_about.male')}</Option>
          <Option value='female'>{t('tell_about.female')}</Option>
          <Option value='other'>{t('tell_about.other')}</Option>
        </Select>
      </Form.Item>

      <Form.Item
        name='birthday'
        label={t('tell_about.birthday')}
        rules={[{ required: true }]}
      >
        <DatePicker
          style={{ width: '100%' }}
          format='DD/MM/YYYY'
          inputReadOnly
          placeholder={t('tell_about.enter_birthday')}
          disabledDate={current => current && current > dayjs().endOf('day')}
        />
      </Form.Item>

      <Form.Item
        name='purpose'
        label={t('tell_about.purpose')}
        rules={[{ required: true }]}
      >
        <Input placeholder={t('tell_about.enter_purpose')} />
      </Form.Item>
    </div>
  );
};

export default StepOne;
