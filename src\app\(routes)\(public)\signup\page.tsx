/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { handleRoute, handleSocialLogin } from '@/components/auth/AuthFunction';
import AuthWrapper, { authStyles } from '@/components/auth/AuthWrapper';
import FacebookLogin from '@/components/FacebookLogin';
import { useNotification } from '@/components/Notification/NotificationContext';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import siteConfig from '@/config/site.config';
import { useTheme } from '@/contexts/ThemeContext';
import { getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import { getPlatform } from '@/utils/CommonFunctions';
import {
  AppleFilled,
  GoogleCircleFilled,
  LinkedinFilled,
} from '@ant-design/icons';
import {
  CredentialResponse,
  TokenResponse,
  useGoogleLogin,
} from '@react-oauth/google';
import { Alert, Button, Divider, Form, Input, Typography } from 'antd';
import { isEmpty } from 'lodash-es';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import AppleSignin from 'react-apple-signin-auth';
import { isMobile, isTablet } from 'react-device-detect';
import 'react-international-phone/style.css';
import { LinkedIn } from 'react-linkedin-login-oauth2';
import PhoneNumberInput from '../../../../components/PhoneNumberInput/PhoneNumberInput';
const { Title, Text } = Typography;

interface SignupResponse {
  status: boolean;
  message: string;
  otp?: string;
}

const SignupPage: React.FC = () => {
  const [form] = Form.useForm();
  const [phoneData, setPhoneData] = useState({
    phone: '',
    country: 'us',
    countryCode: '+1',
  });

  const [loading, setLoading] = useState(false);
  const [socialLoading, setSocialLoading] = useState('');
  const [error, setError] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);

  const { user } = useAppSelector(state => state.auth);

  const notification = useNotification();

  const { t } = useTranslation();

  const router = useRouter();

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'LINKEDIN_AUTH_SUCCESS') {
        const { code } = event.data;
        const data = {
          credential: code,
        };
        handleSuccess(data, 'linkedIn');
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  interface AppleResponse {
    authorization: {
      id_token: string;
    };
    user?: {
      name?: {
        firstName?: string;
        lastName?: string;
      };
    };
  }
  interface SocialLoginPayload {
    token: string;
    type: string;
    role: string;
    full_name_obj?: object;
  }

  const { theme } = useTheme();

  const handleSubmit = async (values: {
    name: string;
    email: string;
    mobile_number: string;
    password: string;
    confirm_password: string;
  }) => {
    type BaseSubmitData = {
      platform: 'ios' | 'android' | 'web';
      email: string;
      password: string;
      mobile_number?: string;
      country_code?: string;
    };

    type SubmitData =
      | (BaseSubmitData & { role: 'agency'; agencyName: string })
      | (BaseSubmitData & { role?: string; fullname: string });

    let submitData: SubmitData;

    if (user?.role === 'agency') {
      submitData = {
        role: 'agency',
        platform: getPlatform(),
        agencyName: values.name,
        email: values.email,
        password: values.password,
      };
    } else {
      submitData = {
        role: user?.role,
        platform: getPlatform(),
        fullname: values.name,
        email: values.email,
        password: values.password,
      };
    }

    if (phoneData?.phone) {
      submitData.mobile_number = phoneData.phone;
      submitData.country_code = phoneData.countryCode;
    }

    setLoading(true);
    try {
      setLoading(true);
      setError('');

      const response = await getApiData<typeof submitData, SignupResponse>({
        url: `${siteConfig.apiUrl}${Endpoints.signup}`,
        method: 'POST',
        data: submitData,
        customUrl: true,
      });

      if (response && response.status === true) {
        router.push(
          `/verify-otp?email=${encodeURIComponent(values.email)}&from=signup`
        );
        notification.success({
          message: 'Signup Successful',
          description: response?.otp
            ? `Your OTP is ${response?.otp}`
            : response?.message || 'Signup successful',
        });
        setLoading(false);
      } else {
        notification.error({
          message: 'Signup Failed',
          description: response?.message || 'Please try again later.',
        });
        setLoading(false);
      }
    } catch (error) {
      notification.error({
        message: 'Signup Failed',
        description: error?.message || 'Please try again later.',
      });
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  // Define a type guard
  const isTokenResponse = (
    response: TokenResponse | CredentialResponse
  ): response is TokenResponse => {
    return 'access_token' in response;
  };

  const isCredentialResponse = (
    response: TokenResponse | CredentialResponse
  ): response is CredentialResponse => {
    return 'credential' in response;
  };

  const handleSuccess = async (
    credentialResponse: TokenResponse | CredentialResponse,
    type?: string,
    userObj?: object
  ) => {
    let token = '';
    setSocialLoading(type || '');

    try {
      if (isTokenResponse(credentialResponse)) {
        token = credentialResponse.access_token;
      } else if (isCredentialResponse(credentialResponse)) {
        token = credentialResponse.credential || '';
      }

      if (token) {
        // const decoded = jwtDecode(credentialResponse.credential);
        const values: SocialLoginPayload = {
          token,
          type: type || 'google',
          role: user?.role || 'individual',
        };
        if (type === 'apple' && userObj && !isEmpty(userObj)) {
          values.full_name_obj = userObj;
        }
        const res = await handleSocialLogin(values);
        if (res?.data) {
          handleRoute(res, router);
        }
        setSocialLoading('');
        // console.log('Decoded JWT:', credentialResponse, decoded);
      }
    } catch (error) {
      console.error('Login error ==>', error);
    } finally {
      setSocialLoading('');
    }
  };
  const handleError = (error?: unknown) => {
    notification.error({
      message: 'Login Failed',
      description: 'Please try again later.',
    });
    // console.error('Login Failed');
  };

  const login = useGoogleLogin({
    onSuccess: tokenResponse => handleSuccess(tokenResponse, 'google'),
    onError: error => {
      handleError(error);
      console.error(error);
    },
  });

  const type = user?.role || '';

  let btnWidth = '600px';
  if (isTablet) {
    btnWidth = '500px';
  } else if (isMobile) {
    btnWidth = '400px';
  }

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');
  // get the domain
  const getBaseUrl = () => {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return process.env.NEXT_PUBLIC_BASE_URL || '';
  };
  return (
    <AuthWrapper>
      <div className={authStyles.logoSection}>
        <Image
          src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
          alt='Swiss Trust Layer'
          className={authStyles.logoImage}
          width={180}
          height={180}
          style={{
            width: xs ? '175px' : sm ? '190px' : '220px',
            height: 'auto',
          }}
        />

        <Title level={2} className={authStyles.formTitle}>
          {type === 'agency'
            ? t('signup.agency_title')
            : t('signup.title_user')}
        </Title>
        <Text className={authStyles.formSubtitle}>{t('signup.subtitle')}</Text>
      </div>

      {error && (
        <Alert
          message={error}
          type='error'
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        autoComplete='off'
      >
        <Form.Item
          label={
            type === 'agency' ? t('signup.agency_name') : t('signup.full_name')
          }
          name='name'
          rules={[
            {
              required: true,
              message:
                type === 'agency'
                  ? t('signup.agency_name_required')
                  : `${t('signup.required_full_name')}`,
            },
            {
              pattern: /^[a-zA-Z\s]+$/,
              message: t('signup.valid_full_name'),
            },
            {
              min: 2,
              message: 'First name must be at least 2 characters',
            },
            {
              max: 50,
              message: 'Full name must be between 2 and 50 characters',
            },
          ]}
          className={authStyles.formItem}
        >
          <Input
            placeholder={
              type === 'agency'
                ? t('signup.agency_name_placeholder')
                : t('signup.full_name_placeholder')
            }
            className={authStyles.input}
          />
        </Form.Item>

        <Form.Item
          label={t('signup.email')}
          name='email'
          rules={[
            { required: true, message: `${t('signup.required_email')}` },
            { type: 'email', message: `${t('signup.valid_email')}` },
          ]}
          className={authStyles.formItem}
        >
          <Input
            placeholder={t('signup.email_placeholder')}
            type='email'
            className={authStyles.input}
          />
        </Form.Item>
        <PhoneNumberInput
          value={phoneData.phone}
          onChange={data => {
            setPhoneData({
              phone: data.phone,
              country: data.country,
              countryCode: data.countryCode,
            });
          }}
          theme={theme}
          form={form}
          t={t}
          authStyles={authStyles}
        />

        <Form.Item
          label={t('signup.password')}
          name='password'
          rules={[
            { required: true, message: `${t('login.required_password')}` },
            { min: 6, message: 'Password must be at least 6 characters long' },
            {
              max: 64,
              message: 'Password length must be at less than 64 characters',
            },

            {
              pattern:
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{6,}$/,
              message:
                'Password must contain at least one uppercase and one lowercase letter, one number, and one special character',
            },
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('login.password_placeholder')}
            className={authStyles.passwordInput}
            maxLength={20}
          />
        </Form.Item>
        <Form.Item
          label={t('signup.confirm_password')}
          name='confirm_password'
          dependencies={['password']}
          rules={[
            {
              required: true,
              message: `${t('signup.required_confirm_password')}`,
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t('signup.passwords_not_match'))
                );
              },
            }),
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('signup.confirm_password_placeholder')}
            className={authStyles.passwordInput}
            maxLength={20}
          />
        </Form.Item>

        <div className={authStyles.actionsRow}>
          <label className={authStyles.rememberMe}>
            <input
              type='checkbox'
              checked={termsAccepted}
              onChange={e => setTermsAccepted(e.target.checked)}
            />
            {t('signup.agreement')}
          </label>
        </div>

        <Form.Item style={{ marginBottom: 0 }}>
          <Button
            type='primary'
            htmlType='submit'
            loading={loading}
            disabled={!termsAccepted}
            className={authStyles.primaryButton}
          >
            {t('signup.sign_up')}
          </Button>
        </Form.Item>
      </Form>

      <div className={authStyles.authFooter}>
        <Text className={authStyles.footerText}>
          {t('signup.already_account')}{' '}
          <Link href='/login' className={authStyles.signupLink}>
            {t('signup.login')}
          </Link>
        </Text>

        <Divider plain>or</Divider>

        <div className={authStyles.oauthButtons}>
          <Button
            icon={<GoogleCircleFilled />}
            block
            loading={socialLoading === 'google'}
            className={authStyles.oauthBtn}
            onClick={() => {
              login();
            }}
          >
            {t('login.continue_with_google')}
          </Button>
          <AppleSignin
            authOptions={{
              clientId: `${process.env.NEXT_PUBLIC_APPLE_CLIENT_ID}`,
              scope: 'name email',
              redirectURI: `${getBaseUrl()}/apple-callback`,
              state: 'state',
              usePopup: true, // use redirect if false
            }}
            onSuccess={(response: AppleResponse) => {
              const idToken = response?.authorization?.id_token;
              const data = {
                credential: idToken,
              };
              handleSuccess(data, 'apple', response?.user?.name);
            }}
            onError={(error: unknown) => {
              console.error('Apple login error:', error);
            }}
            render={(props: {
              onClick: React.MouseEventHandler<HTMLElement> | undefined;
            }) => (
              <Button
                icon={<AppleFilled />}
                block
                loading={socialLoading === 'apple'}
                className={authStyles.oauthBtn}
                onClick={props.onClick}
              >
                {t('login.continue_with_apple')}
              </Button>
            )}
            uiType={'light'}
          />
          <FacebookLogin
            loader={socialLoading === 'facebook'}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSuccess={(response: any) => {
              handleSuccess(response, 'facebook');
            }}
          />
          <LinkedIn
            clientId={`${process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID}`}
            redirectUri={`${getBaseUrl()}/linkedin-callback`}
            onSuccess={code => {
              console.log('Authorization Code::', code);
            }}
            scope='openid profile email'
            onError={error => {
              handleError(error);
            }}
          >
            {({ linkedInLogin }) => (
              <Button
                onClick={linkedInLogin}
                block
                loading={socialLoading === 'linkedIn'}
                icon={<LinkedinFilled />}
              >
                {t('login.continue_with_linkedin')}
              </Button>
            )}
          </LinkedIn>
        </div>
      </div>

      <div className={authStyles.footerNote}>
        <Text>© SwissTrustLayer</Text>
      </div>
    </AuthWrapper>
  );
};

export default SignupPage;
