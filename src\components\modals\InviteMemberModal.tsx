// components/InviteMemberModal.tsx

'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { colors } from '@/theme/antdTheme';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Select, Space, Typography } from 'antd';
import { isEmpty } from 'lodash-es';
import React from 'react';
import { Endpoints } from '../../config/endpoints';
import { ApiResponse, getApiData } from '../../helpers/ApiHelper';
import { validateEmail } from '../../utils/validation';

const { Title, Text } = Typography;
const { Option } = Select;

interface preDataProps {
  email: string;
  fullname: string;
  profile_pic: string;
  access_role: string;
  _id: string | number;
  user_id: string | number;
  owner_id: string | number;
}

interface FolderOption {
  _id: string;
  name: string;
}

interface InviteMemberModalProps {
  visible: boolean;
  onClose: () => void;
  type?: string;
  onSendInvite: (payload: {
    email: string;
    role: string;
    folder_id?: string;
  }) => void;
  changePermission?: (payload: { email: string; role: string }) => void;
  btnLoader: boolean;
  preData?: preDataProps;
  accessRole?: string;
}

const InviteMemberModal: React.FC<InviteMemberModalProps> = ({
  visible,
  onClose,
  onSendInvite,
  btnLoader,
  preData,
  changePermission = () => {},
  accessRole,
  type,
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [email, setEmail] = React.useState(
    preData?.email ? preData?.email : ''
  );
  const [emailError, setEmailError] = React.useState('');
  const [role, setRole] = React.useState<string>(
    preData?.access_role ? preData?.access_role : 'user'
  );
  const [selectedFolder, setSelectedFolder] = React.useState<string>();

  const [folders, setFolders] = React.useState<FolderOption[]>([]);

  const [folderError, setFolderError] = React.useState('');

  const [foldersLoading, setFoldersLoading] = React.useState(false);

  React.useEffect(() => {
    getFoldersList();
    // Sync fields when preData changes or when modal visibility toggles
    if (visible) {
      setEmail(preData?.email ?? '');
      setRole(preData?.access_role ?? 'user');
      setFolderError(''); // Clear folder error when modal opens
      setSelectedFolder(undefined);
    }
  }, [preData, visible]);

  const handleSendInvite = () => {
    let hasError = false;

    // Validate email
    if (!validateEmail(email)) {
      setEmailError(t('createFolder.validation.enter_valid_email'));
      hasError = true;
    } else {
      setEmailError('');
    }

    // Validate folder selection for userManagement type
    if (type === 'userManagement' && !selectedFolder) {
      setFolderError(t('inviteModal.select_folder_required'));
      hasError = true;
    } else {
      setFolderError('');
    }

    // Only proceed if no validation errors
    if (!hasError) {
      const data: { email: string; role: string; folder_id?: string } = {
        email: email,
        role: role,
      };

      // Only include folder_id if type is userManagement and a folder is selected
      if (type === 'userManagement' && selectedFolder) {
        data.folder_id = selectedFolder;
      }

      if (isEmpty(preData)) {
        onSendInvite(data);
      } else {
        changePermission(data);
      }
    }
  };

  const handleClose = () => {
    onClose();
    setEmail('');
    setEmailError('');
    setRole('user');
    setSelectedFolder(undefined);
    setFolderError('');
  };

  const getFoldersList = async () => {
    setFoldersLoading(true);
    try {
      const res = await getApiData<
        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        {},
        ApiResponse & {}
      >({
        url: Endpoints.folderList,
        method: 'GET',
      });
      if (res && res.status === true) {
        if (Array.isArray(res.data)) {
          setFolders(res?.data);
        }
      }
      setFoldersLoading(false);
    } catch (error) {
      console.log('Error fetching user data:', error);
      setFoldersLoading(false);
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={handleClose}
      footer={null}
      closeIcon={<CloseOutlined style={{ fontSize: 18 }} />}
      centered
      width={500}
      bodyStyle={{
        backgroundColor: theme === 'dark' ? colors.primary : colors.white,
        padding: '32px 24px', // your spacing
        borderRadius: '10px',
      }}
      styles={{
        content: {
          backgroundColor: theme === 'dark' ? colors.primary : colors.white, // override white bg
          padding: 0, // remove default white padding
          boxShadow: '0 8px 24px rgba(0,0,0,0.2)', // keep nice shadow
        },
      }}
    >
      <div style={{ textAlign: 'center' }}>
        <Title level={3} style={{ marginBottom: 24 }}>
          {isEmpty(preData)
            ? t('inviteModal.send_invite')
            : t('inviteModal.update_invite')}
        </Title>
      </div>

      {/* Folder Selection - Only show when type is userManagement */}
      {type === 'userManagement' && (
        <div style={{ marginBottom: 30 }}>
          <Text strong>{t('inviteModal.select_folder')}</Text>
          <Select
            value={selectedFolder}
            onChange={value => {
              setSelectedFolder(value);
              if (value) {
                setFolderError('');
              }
            }}
            loading={foldersLoading}
            placeholder={t('inviteModal.folder_placeholder')}
            style={{ width: '100%', marginTop: 8 }}
            allowClear
            status={folderError ? 'error' : ''}
          >
            {folders.map(folder => (
              <Option key={folder._id} value={folder._id}>
                {folder.name}
              </Option>
            ))}
          </Select>
          {folderError && <Text style={{ color: 'red' }}>{folderError}</Text>}
        </div>
      )}

      {/* Role dropdown */}
      <div style={{ marginBottom: 30 }}>
        <Text strong>{t('inviteModal.select_role')}</Text>
        <Select
          value={role}
          onChange={setRole}
          style={{ width: '100%', marginTop: 8 }}
          disabled={accessRole === 'admin'}
        >
          <Option
            value='admin'
            style={{
              color: role === 'admin' ? 'white' : 'black',
              backgroundColor: role === 'admin' ? colors.primary : 'white',
            }}
          >
            {t('roles.admin')}
          </Option>
          <Option
            value='legal_user'
            style={{
              color: role === 'legal_user' ? 'white' : 'black',
              backgroundColor: role === 'legal_user' ? colors.primary : 'white',
            }}
          >
            {t('roles.legal_user')}
          </Option>
          <Option
            value='user'
            style={{
              color: role === 'user' ? 'white' : 'black',
              backgroundColor: role === 'user' ? colors.primary : 'white',
            }}
          >
            {t('roles.user')}
          </Option>
        </Select>
      </div>

      <div style={{ marginBottom: 30 }}>
        <Text strong>{t('inviteModal.invite_friends')}</Text>
        <Input
          placeholder={t('inviteModal.email_placeholder')}
          value={email}
          type='email'
          disabled={!isEmpty(preData?.email)}
          onChange={e => {
            if (validateEmail(e.target.value) || isEmpty(e.target.value)) {
              setEmailError('');
            } else {
              setEmailError(t('createFolder.validation.enter_valid_email'));
            }
            setEmail(e.target.value);
          }}
          style={{ marginTop: 8 }}
        />
        <Text style={{ color: 'red' }}>{emailError}</Text>
      </div>

      <Space style={{ width: '100%', justifyContent: 'center' }}>
        <Button onClick={handleClose}>{t('common.cancel')}</Button>
        <Button type='primary' loading={btnLoader} onClick={handleSendInvite}>
          {t(
            isEmpty(preData)
              ? 'inviteModal.send_invite'
              : 'inviteModal.update_invite'
          )}
        </Button>
      </Space>
    </Modal>
  );
};

export default InviteMemberModal;
