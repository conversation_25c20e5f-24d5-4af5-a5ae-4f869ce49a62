import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import {
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Dropdown, MenuProps, Modal, Space, Typography } from 'antd';
import { useState } from 'react';
import { useAppSelector } from '../../../store/hooks';
import styles from './UserCard.module.css';
interface User {
  email: string;
  fullname: string;
  profile_pic: string;
  access_role: string;
  _id: string | number;
  user_id: string | number;
  owner_id: string | number;
  status?: string;
}

const { Title, Text } = Typography;

const UserCard = ({
  invitedUser,
  onCancel,
  onEdit,
  removeUserLoader,
  folderAccess,
}: {
  invitedUser: User;
  onCancel: () => void;
  onEdit: () => void;
  removeUserLoader: boolean;
  folderAccess: string;
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [removeUserVisible, setRemoveUserVisible] = useState(false);
  const { user } = useAppSelector(s => s.auth);
  const menuItems: MenuProps['items'] = [
    {
      key: 'edit',
      label: t('myIdeas.actions.edit'),
      icon: <EditOutlined />,
    },
    {
      key: 'delete',
      label: t('myIdeas.actions.delete'),
      icon: <DeleteOutlined />,
      danger: true,
    },
  ];
  const handleMenuClick: MenuProps['onClick'] = info => {
    if (info.key === 'edit') {
      onEdit();
    } else if (info.key === 'delete') {
      setRemoveUserVisible(true);
    }
  };
  const showDeleteButton = () => {
    if (
      folderAccess === 'admin' &&
      invitedUser?.access_role !== 'owner' &&
      invitedUser?.access_role === 'user' &&
      user?._id !== invitedUser?.user_id
    ) {
      return true;
    }
    return false;
  };
  const showMoreOptions = () => {
    if (
      folderAccess === 'owner' &&
      user?._id !== invitedUser?.user_id &&
      !showDeleteButton() &&
      invitedUser?.access_role !== 'owner'
    ) {
      return true;
    }
    return false;
  };

  return (
    <div className={styles.userCard}>
      {showDeleteButton() && (
        <div
          className={styles.actionButton}
          onClick={() => setRemoveUserVisible(true)}
        >
          <DeleteOutlined style={{ color: 'red' }} />
        </div>
      )}
      {showMoreOptions() && (
        <div className={styles.actionButton}>
          <Dropdown
            menu={{ items: menuItems, onClick: handleMenuClick }}
            trigger={['click']}
            placement='bottomRight'
          >
            <MoreOutlined />
          </Dropdown>
        </div>
      )}
      <div className={styles.avatarContainer}>
        <Avatar
          size={100}
          src={invitedUser.profile_pic}
          icon={!invitedUser.profile_pic ? <UserOutlined /> : <></>}
        />
      </div>
      <Title level={4} className={styles.name}>
        {invitedUser.fullname}
      </Title>
      <Text className={styles.role}>
        {invitedUser.access_role === 'admin'
          ? 'Admin'
          : invitedUser.access_role === 'legal_user'
            ? 'Legal User'
            : invitedUser.access_role === 'user'
              ? 'User'
              : invitedUser?.access_role === 'owner'
                ? 'Owner'
                : '-'}
      </Text>
      {invitedUser?.status === 'pending_invite' && (
        <Text
          className={styles.roleStatus}
        >{`(${t('createFolder.validation.pending')})`}</Text>
      )}

      <Modal
        title={null}
        open={removeUserVisible}
        onCancel={() => {
          setRemoveUserVisible(false);
        }}
        centered
        closable={false}
        onOk={() => {
          onCancel();
        }}
        confirmLoading={removeUserLoader}
        width={400}
      >
        <Space direction='vertical' style={{ width: '100%' }} size='large'>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              textAlign: 'center',
            }}
          >
            <Title
              level={5}
              style={{
                color:
                  theme === 'dark'
                    ? 'var(--color-text-tertiary)'
                    : 'var(--color-text-primary)',
              }}
              className={styles.logoutTitle}
            >
              {t('users.confirm_remove_access_user')}
            </Title>
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default UserCard;
